.product-list {
  width: var(--width-content);
  padding-top: 30px;
  margin: auto;
  &__tabs {
    display: flex;
    justify-content: center;
    gap: 14px;
    &__item {
      padding: 0 30px;
      height: 42px;
      line-height: 42px;
      border-radius: 4px;
      background-color: #fff;
      font-size: var(--font-medium);
      color: var(--text-description);
      &--active {
        background-color: var(--color-theme);
        color: #fff;
      }
    }
  }
  &__items {
    margin-top: 30px;
  }
}

.pagination {
  margin-top: 30px;
}

.product-preview {
  padding: 30px;
  display: flex;
  gap: 30px;
  background-color: #fff;
  &__left {
    &__image {
      width: 240px;
      height: 240px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
    }
    &__switch {
      margin-top: 10px;
      display: flex;
      &__image-list {
        display: flex;
        max-width: 180px;
        overflow: hidden;
      }
      &__switcher,
      &__image {
        width: 30px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }
      &__image {
        width: 60px;
      }
    }
  }
  &__right {
    flex: 1;
    display: flex;
    flex-direction: column;
    h1 {
      font-size: 28px;
      font-weight: var(--font-bolder);
      margin: 0;
    }
    p {
      color: var(--text-description);
      font-size: var(--font-medium);
      margin-top: 8px;
    }
    &__line {
      height: 0;
      border: 1px solid #eee;
      margin: 20px 0;
    }
    &__types {
      display: flex;
      gap: 20px;
      &__item {
        border-radius: 4px;
        height: 40px;
        padding: 0 30px;
        border: 1px solid var(--gray-3);
        background-color: #fff;
        &--active {
          border: 1px solid var(--color-theme);
          color: var(--color-theme);
        }
      }
    }
    &__properties {
      display: flex;
      flex-wrap: wrap;
      column-gap: 24px;
      row-gap: 14px;
      margin-top: 20px;
      span {
        color: var(--text-description);
      }
    }
    &__buttons {
      display: flex;
      column-gap: 20px;
      button {
        height: 42px;
        padding: 0 30px;
        border-radius: 6px;
        background-color: var(--color-theme);
        color: #fff;
        font-size: var(--font-medium);
        line-height: 42px;
        align-items: center;
        display: flex;
        gap: 10px;
        &:last-of-type {
          color: var(--color-theme);
          background-color: #e3f2ff;
        }
      }
    }
  }
}

.product-info {
  background-color: #fff;
  padding: 30px;
  &__tabs {
    display: flex;
    gap: 30px;
    border-bottom: 1px solid var(--gray-3);
    &__button {
      height: 33px;
      padding: 0 10px;
      font-size: var(--font-medium);
      color: var(--text-description);
      background-color: transparent;
      &--active {
        color: var(--color-theme);
        border-bottom: 2px solid var(--color-theme);
      }
    }
  }
  h2 {
    font-size: 24px;
    text-align: left;
    margin-bottom: 20px;
    font-weight: var(--font-bolder);
  }
  &__description {
    margin-top: 30px;
    p {
      font-size: var(--font-medium);
      line-height: 32px;
      margin: 20px auto;
    }
    h4 {
      text-align: center;
    }
    &__image {
      width: 500px;
      height: 500px;
      margin: auto;
    }
  }
  &__spec {
    &__item {
      display: flex;
      gap: 1px;
      div {
        background-color: #f4f4f4;
        text-align: center;
        padding: 13.5px;
        &:first-of-type {
          width: 180px;
        }
        &:last-of-type {
          flex: 1;
        }
      }
    }
    &__item--white {
      div {
        background-color: #fff;
      }
    }
  }
  &__videos {
    margin-top: 30px;
  }
  &__helps {
    margin-top: 30px;
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .product-list {
    padding: 34px 20px 0;
  }
}

@media (max-width: 450px) {
  .product-list {
    padding: 10px 16px 0;
    &__items {
      margin-top: 0;
    }
  }
  .pagination {
    margin-top: 0;
  }
  .product-preview {
    flex-direction: column;
    padding: 0;
    &__left {
      &__image {
        width: 100%;
        height: 100%;
      }
    }
    &__right {
      padding: 20px 16px;
      h1 {
        font-size: 20px;
      }
      &__types {
        margin-top: 16px;
        overflow: scroll;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
        overflow: -moz-scrollbars-none; /* Firefox */
        ::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }
        &__item {
          flex-shrink: 0;
        }
      }
      &__properties {
        row-gap: 14px;
        column-gap: 6px;
        span {
          background-color: var(--gray-7);
          border-radius: 4px;
          font-size: 13px;
          padding: 5px 12px;
        }
      }
    }
  }
  .product-info {
    padding: 0;
    &__tabs {
      padding-left: 20px;
      gap: 20px;
      overflow: scroll;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
      overflow: -moz-scrollbars-none; /* Firefox */
      ::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
      &__button {
        flex-shrink: 0;
        padding: 0;
        height: 42px;
      }
    }
    &__content {
      padding: 20px 16px;
      width: 100vw;
    }
    &__description {
      margin-top: 0;
      &__image {
        width: 100%;
        margin: auto;
      }
    }
  }
}
