"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/product/(overview)/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/product/product-list.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/ui/product/product-list.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductListLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _product_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./product.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/product/product.module.scss\");\n/* harmony import */ var _product_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_product_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/flex-4items-box */ \"(app-pages-browser)/./app/[locale]/ui/components/flex-4items-box.tsx\");\n/* harmony import */ var _components_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/pagination */ \"(app-pages-browser)/./app/[locale]/ui/components/pagination.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\nfunction ProductListLayout(param) {\n    let { groups, products } = param;\n    _s();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductList, {\n            groups: groups,\n            products: products\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductListLayout, \"9zQ3KOL0Rwq6cPrON/AdiF1Ksas=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale\n    ];\n});\n_c = ProductListLayout;\nfunction ProductList(param) {\n    let { groups, products, initialTab } = param;\n    _s1();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const tabs = [\n        ...groups\n    ];\n    tabs.unshift({\n        id: \"00\",\n        name: t(\"all\"),\n        nameEn: t(\"all\")\n    });\n    const _tabs = [\n        {\n            id: \"0\",\n            text: \"全部\"\n        },\n        {\n            id: \"1\",\n            text: \"摄像机类型\"\n        }\n    ];\n    // 根据initialTab设置初始选中的tab\n    const getInitialTab = ()=>{\n        if (initialTab) {\n            // 检查initialTab是否存在于tabs中\n            const foundTab = tabs.find((tab)=>tab.id === initialTab);\n            if (foundTab) {\n                return initialTab;\n            }\n        }\n        return tabs[0].id;\n    };\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTab());\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(products.length);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const getCurrentProducts = (page, groupId)=>{\n        let datas = products.filter((_, index)=>index >= (page - 1) * 8 && index < page * 8);\n        if (groupId !== tabs[0].id) datas = datas.filter((item)=>item.groupId === groupId);\n        return datas;\n    };\n    const getTabProducts = (groupId)=>{\n        if (groupId === tabs[0].id) return products;\n        else {\n            return products.filter((item)=>item.groupId === groupId);\n        }\n    };\n    const [currentProducts, setCurrentProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getCurrentProducts(currentPage, currentTab));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: t(\"productCenter\"),\n                iconSrc: \"/product-center-icon.svg\",\n                currentTab: currentTab,\n                tabs: _tabs,\n                background: \"rgb(200,228,250)\",\n                bannerSrc: \"/product/banner-pc.jpg\",\n                bannerMobileSrc: \"/product/banner-mobile.png\",\n                isSearch: true,\n                onTabChange: (tab)=>{\n                    setCurrentTab(tab);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list\"]),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                        tabs: tabs,\n                        currentTab: currentTab,\n                        onClick: (id)=>{\n                            setCurrentTab(id);\n                            setCurrentPage(1);\n                            const products = getCurrentProducts(1, id);\n                            setCount(getTabProducts(id).length);\n                            setCurrentProducts(products);\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(List, {\n                        products: currentProducts\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListPagination, {\n                        currentPage: currentPage,\n                        count: count,\n                        onChange: (page)=>{\n                            setCurrentPage(page);\n                            setCurrentProducts(getCurrentProducts(page, currentTab));\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(ProductList, \"5LcL08ccO9RgBkaQQnH8xGdLFtI=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n\n    ];\n});\n_c1 = ProductList;\nfunction Tabs(param) {\n    let { tabs, currentTab, onClick } = param;\n    _s2();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const TabItem = (param)=>{\n        let { name, id, nameEn } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>{\n                if (id !== currentTab) onClick(id);\n            },\n            className: \"\".concat((_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs__item\"]), \" \").concat(currentTab === id ? (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs__item--active\"]) : \"\"),\n            children: locale === \"zh\" ? name : nameEn\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 158,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat((_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs\"]), \" hide-on-small\"),\n        children: tabs.map((item, index)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(TabItem, {\n                ...item,\n                key: index,\n                __source: {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                },\n                __self: this\n            }))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s2(Tabs, \"0bgUYh39Ymd94mXW1RH411vtZ4A=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale\n    ];\n});\n_c2 = Tabs;\nfunction List(param) {\n    let { products } = param;\n    _s3();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const infos = products.map((item)=>{\n        return {\n            imageSrc: item.imageSrc,\n            title: locale === \"zh\" ? item.name : item.nameEn,\n            tip: locale === \"zh\" ? item.description : item.descriptionEn,\n            link: item.id === \"c31\" ? \"/product/c31\" : \"\"\n        };\n    });\n    const productsInfo = {\n        infos,\n        imageSize: {\n            width: 200,\n            height: 200\n        },\n        imageBox: {\n            width: 300,\n            height: 300\n        },\n        mode: _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__.itemsMode.product\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__items\"]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            ...productsInfo\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s3(List, \"0bgUYh39Ymd94mXW1RH411vtZ4A=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale\n    ];\n});\n_c3 = List;\nfunction ListPagination(param) {\n    let { currentPage, count, onChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default().pagination),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pagination__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentPage: currentPage,\n                count: count,\n                onChange: (page)=>{\n                    onChange(page);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_c4 = ListPagination;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ProductListLayout\");\n$RefreshReg$(_c1, \"ProductList\");\n$RefreshReg$(_c2, \"Tabs\");\n$RefreshReg$(_c3, \"List\");\n$RefreshReg$(_c4, \"ListPagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/product/product-list.tsx\n"));

/***/ })

});