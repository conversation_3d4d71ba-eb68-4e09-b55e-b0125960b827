.about {
  position: relative;
  &__image {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 1920px;
    height: 900px;
  }
  &__content {
    width: var(--width-content);
    margin: auto;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
    > div {
      border-radius: 6px;
      background-color: #fff;
      padding: 30px;
      h1 {
        text-align: center;
        margin-bottom: 16px;
      }
      p {
      }
    }
    &__main {
      &__view {
        display: flex;
        justify-content: space-around;
        gap: 10px;
        &__card {
          border: 1px solid #ccc;
          flex: 1;
          padding: 10px;
          &__content {
            margin-top: 30px;
            display: flex;
            flex-direction: column;
            gap: 4px;
          }
        }
      }

      &__quotes {
        margin-top: 34px;
        display: flex;
        justify-content: space-around;
        &__item {
          width: 394px;
          height: 123px;
          display: flex;
          padding: 20px 30px;
          align-items: center;
          gap: 20px;
          > div {
            display: flex;
            flex-direction: column;
            gap: 10px;
            text-align: left;
            h5 {
              margin: 0;
            }
            span {
              color: var(--text-description);
              font-size: var(--font-medium);
            }
          }
        }
      }
    }
    &__career {
      &__companytime {
        margin-top: 30px;
      }
      &__timeline {
        margin-top: 30px;
        &__item {
          display: flex;
          justify-content: center;
          gap: 10px;
          > div:first-of-type {
            display: flex;
            justify-content: flex-end;
            width: 400px;
            > div {
              text-align: center;
              > div {
                border-radius: 6px;
                overflow: hidden;
              }
              h6 {
                margin-top: 10px;
              }
            }
          }
          > div:nth-of-type(2) {
            display: flex;
            flex-direction: column;
            align-items: center;
            > div:first-of-type {
              flex: 1;
              border-right: 1px solid var(--gray-4);
            }
            > div:nth-of-type(2) {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 16px;
              height: 16px;
              > div {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: var(--color-theme);
              }
            }
            > div:last-of-type {
              flex: 1;
              border-right: 1px solid var(--gray-4);
            }
          }
          > div:nth-of-type(3) {
            width: 400px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 10px;
            h4 {
              margin: 0;
            }
            p {
              color: var(--text-description);
            }
          }
          &--reverse {
            flex-direction: row-reverse;
            > div:first-of-type {
              justify-content: flex-start;
            }
            p,
            h4 {
              text-align: right;
            }
          }
          &:first-of-type {
            > div:nth-of-type(2) {
              > div:first-of-type {
                opacity: 0;
              }
            }
          }
          &:last-of-type {
            > div:nth-of-type(2) {
              > div:last-of-type {
                opacity: 0;
              }
            }
          }
        }
      }
    }
    &__prides {
      margin-top: 30px;
      &__list {
        display: flex;
        column-gap: 15px;
        row-gap: 16px;
        flex-wrap: nowrap;
        &__item {
          position: relative;
          &--normal {
            max-width: 231px;
            aspect-ratio: 231/326;
          }
          &--large {
            max-width: 478px;
            aspect-ratio: 478/326;
          }
          &--small {
            max-width: 231px;
            aspect-ratio: 231/154;
          }
          &__cover {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            background-color: rgba($color: #0075eb, $alpha: 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: var(--font-large);
            border-radius: 6px;
            padding: 0 16px;
            text-align: center;
          }
        }
        &:last-of-type {
          margin-top: 16px;
        }
      }
    }
    &__contacts {
      background-color: transparent !important;
      padding: 0 !important;
      margin-top: 30px;
    }
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .about {
    &__image {
      width: 100vw;
      height: 50vh;
    }
    &__content {
      padding: 0 20px;
      > div {
        padding: 20px 16px;
      }
      &__main {
      }
    }
  }
}

@media (max-width: 450px) {
  .about {
    &__content {
      padding: 16px 16px 0;
      > div {
        padding: 20px 16px;
      }
      &__main {
        &__quotes {
          flex-direction: column;
          justify-content: flex-start;
          gap: 20px;
          &__item {
            width: 100%;
            height: auto;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            h5 {
              text-align: center;
            }
          }
        }
      }
      &__career {
        &__timeline {
          margin-top: 20px;
          &__item {
            > div:last-of-type {
              > div {
                width: 100%;
                aspect-ratio: 285/190;
              }
            }
            h6 {
              margin: 0;
              text-align: left;
            }
            p {
              margin-bottom: 20px;
            }
            &--reverse {
              flex-direction: row;
              > div:first-of-type {
                justify-content: flex-end;
              }
              p,
              h4 {
                text-align: left;
              }
            }
            &:last-of-type {
              p {
                margin-bottom: 0;
              }
            }
          }
        }
      }
      &__prides {
        background-color: transparent !important;
        padding: 0 !important;
        margin-top: 0 !important;
      }
      &__contacts {
        margin-top: 0;
      }
    }
  }
}
