{"sortedMiddleware": ["/"], "middleware": {"/": {"files": ["prerender-manifest.js", "server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|static|.*\\..*|_next|favicon.ico|robots.txt|ads.txt|union.txt|_next\\/static|_next\\/image).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!api|static|.*\\..*|_next|favicon.ico|robots.txt|ads.txt|union.txt|_next/static|_next/image).*)"}], "wasm": [], "assets": []}}, "functions": {}, "version": 2}