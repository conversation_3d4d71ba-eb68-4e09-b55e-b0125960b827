(()=>{var t={};t.id=981,t.ids=[981],t.modules={7849:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external")},2934:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external")},4580:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:t=>{"use strict";t.exports=require("path")},7310:t=>{"use strict";t.exports=require("url")},3030:(t,e,i)=>{"use strict";i.r(e),i.d(e,{GlobalError:()=>o.a,__next_app__:()=>l,originalPathname:()=>n,pages:()=>a,routeModule:()=>u,tree:()=>p});var r=i(482),_=i(9108),c=i(2563),o=i.n(c),s=i(8300),d={};for(let t in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(t)&&(d[t]=()=>s[t]);i.d(e,d);let p=["",{children:["[locale]",{children:["product",{children:["product-detail",{children:["[productId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,7912)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\product-detail\\[productId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,3931)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,7481))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async t=>(await Promise.resolve().then(i.bind(i,7481))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],a=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\product-detail\\[productId]\\page.tsx"],n="/[locale]/product/product-detail/[productId]/page",l={require:i,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:_.x.APP_PAGE,page:"/[locale]/product/product-detail/[productId]/page",pathname:"/[locale]/product/product-detail/[productId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},252:(t,e,i)=>{Promise.resolve().then(i.bind(i,8746)),Promise.resolve().then(i.t.bind(i,1900,23)),Promise.resolve().then(i.t.bind(i,1476,23))},8746:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>d});var r=i(5344),_=i(6655),c=i.n(_),o=i(9410),s=i(3729);function d({productData:t}){let{imageSrc:e,subImageSrcs:i,name:_,types:o,description:d}=t,[n,l]=(0,s.useState)(o[0].name);return(0,r.jsxs)("div",{className:c()["product-preview"],children:[r.jsx(p,{imageSrc:e,subImageSrcs:i}),r.jsx(a,{title:_,description:d,types:o.map(t=>t.name),properties:o.find(t=>t.name===n)?.properties,currentType:n,onClick:t=>{l(t)}})]})}function p({imageSrc:t,subImageSrcs:e}){return(0,r.jsxs)("div",{className:c()["product-preview__left"],children:[r.jsx(()=>r.jsx("div",{className:c()["product-preview__left__image"],children:r.jsx(o.default,{src:t,width:200,height:200,alt:"product",style:{width:"100%",height:"100%"}})}),{}),r.jsx(()=>{let i=({src:t})=>r.jsx("div",{className:c()["product-preview__left__switch__image"],children:r.jsx(o.default,{src:t,width:40,height:40,alt:"product",style:{width:"100%",height:"100%",objectFit:"contain"}})});return(0,r.jsxs)("div",{className:`${c()["product-preview__left__switch"]} hide-on-small`,children:[r.jsx("div",{className:c()["product-preview__left__switch__switcher"],style:{transform:"rotate(180deg)"},children:r.jsx(o.default,{src:"/arrow-right.svg",width:12,height:24,alt:"<"})}),(0,r.jsxs)("div",{className:c()["product-preview__left__switch__image-list"],children:[r.jsx(i,{src:t}),e.map((t,e)=>r.jsx(i,{src:t},e))]}),r.jsx("div",{className:c()["product-preview__left__switch__switcher"],children:r.jsx(o.default,{src:"/arrow-right.svg",width:12,height:24,alt:">"})})]})},{})]})}function a({title:t,description:e,types:i,currentType:_,properties:s,onClick:d}){return(0,r.jsxs)("div",{className:c()["product-preview__right"],children:[r.jsx("h1",{children:t}),r.jsx("p",{children:e}),r.jsx("div",{className:`${c()["product-preview__right__line"]} hide-on-small`}),r.jsx("div",{className:c()["product-preview__right__types"],children:i.map((t,e)=>r.jsx("button",{onClick:()=>{d(t)},className:`${c()["product-preview__right__types__item"]} ${t===_?c()["product-preview__right__types__item--active"]:""}`,children:t},e))}),r.jsx("div",{className:c()["product-preview__right__properties"],children:s&&s.map((t,e)=>r.jsx("span",{children:t},e))}),r.jsx("div",{className:c()["product-preview__right__line"]}),r.jsx("div",{style:{flex:1}}),(0,r.jsxs)("div",{className:c()["product-preview__right__buttons"],children:[r.jsx("button",{children:"去购买"}),(0,r.jsxs)("button",{children:[r.jsx(o.default,{src:"/download-blue.svg",width:18,height:18,alt:""}),"下载说明书"]})]})]})}},8364:t=>{t.exports={"product-detail":"product_product-detail___4Gnp","product-detail__breadcrumbs":"product_product-detail__breadcrumbs__mmH6s","product-detail__preview":"product_product-detail__preview__SY_4j","product-detail__info":"product_product-detail__info__dEjWi"}},3439:t=>{t.exports={"product-list":"product_product-list__nFwZu","product-list__tabs":"product_product-list__tabs__EGq1z","product-list__tabs__item":"product_product-list__tabs__item__hKEh0","product-list__tabs__item--active":"product_product-list__tabs__item--active__L1eXV","product-list__items":"product_product-list__items__EZwk3",pagination:"product_pagination__RiZRT","product-preview":"product_product-preview__JUk6N","product-preview__left__image":"product_product-preview__left__image__sZxBA","product-preview__left__switch":"product_product-preview__left__switch__jxy85","product-preview__left__switch__image-list":"product_product-preview__left__switch__image-list__f0lfQ","product-preview__left__switch__switcher":"product_product-preview__left__switch__switcher__ZGIgH","product-preview__left__switch__image":"product_product-preview__left__switch__image__2TIUy","product-preview__right":"product_product-preview__right__pd9wO","product-preview__right__line":"product_product-preview__right__line__HwV3B","product-preview__right__types":"product_product-preview__right__types__aeNfH","product-preview__right__types__item":"product_product-preview__right__types__item__rwDrW","product-preview__right__types__item--active":"product_product-preview__right__types__item--active__rCWIZ","product-preview__right__properties":"product_product-preview__right__properties__HEUmq","product-preview__right__buttons":"product_product-preview__right__buttons__bNbx9","product-info":"product_product-info__SI8WL","product-info__tabs":"product_product-info__tabs__qiGK6","product-info__tabs__button":"product_product-info__tabs__button__O9Yio","product-info__tabs__button--active":"product_product-info__tabs__button--active__kBKmC","product-info__description":"product_product-info__description__unz1S","product-info__description__image":"product_product-info__description__image__0SqVD","product-info__spec__item":"product_product-info__spec__item__ok_g_","product-info__spec__item--white":"product_product-info__spec__item--white__X6XxS","product-info__videos":"product_product-info__videos__rLEpl","product-info__helps":"product_product-info__helps__klLFQ","product-info__content":"product_product-info__content__x3cOl"}},6655:t=>{t.exports={"product-list":"product_product-list__nFwZu","product-list__tabs":"product_product-list__tabs__EGq1z","product-list__tabs__item":"product_product-list__tabs__item__hKEh0","product-list__tabs__item--active":"product_product-list__tabs__item--active__L1eXV","product-list__items":"product_product-list__items__EZwk3",pagination:"product_pagination__RiZRT","product-preview":"product_product-preview__JUk6N","product-preview__left__image":"product_product-preview__left__image__sZxBA","product-preview__left__switch":"product_product-preview__left__switch__jxy85","product-preview__left__switch__image-list":"product_product-preview__left__switch__image-list__f0lfQ","product-preview__left__switch__switcher":"product_product-preview__left__switch__switcher__ZGIgH","product-preview__left__switch__image":"product_product-preview__left__switch__image__2TIUy","product-preview__right":"product_product-preview__right__pd9wO","product-preview__right__line":"product_product-preview__right__line__HwV3B","product-preview__right__types":"product_product-preview__right__types__aeNfH","product-preview__right__types__item":"product_product-preview__right__types__item__rwDrW","product-preview__right__types__item--active":"product_product-preview__right__types__item--active__rCWIZ","product-preview__right__properties":"product_product-preview__right__properties__HEUmq","product-preview__right__buttons":"product_product-preview__right__buttons__bNbx9","product-info":"product_product-info__SI8WL","product-info__tabs":"product_product-info__tabs__qiGK6","product-info__tabs__button":"product_product-info__tabs__button__O9Yio","product-info__tabs__button--active":"product_product-info__tabs__button--active__kBKmC","product-info__description":"product_product-info__description__unz1S","product-info__description__image":"product_product-info__description__image__0SqVD","product-info__spec__item":"product_product-info__spec__item__ok_g_","product-info__spec__item--white":"product_product-info__spec__item--white__X6XxS","product-info__videos":"product_product-info__videos__rLEpl","product-info__helps":"product_product-info__helps__klLFQ","product-info__content":"product_product-info__content__x3cOl"}},3931:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>_});var r=i(5036);function _({children:t}){return r.jsx("div",{children:t})}},7912:(t,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>N});var r=i(5036),_=i(5251),c=i(8364),o=i.n(c);let s=(0,i(6843).createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\product\product-preview.tsx`),{__esModule:d,$$typeof:p}=s,a=s.default;var n=i(4727),l=i(3439),u=i.n(l),h=i(68),m=i(5008),f=i(2813);function x({productData:t}){let{information:e,spec:i,videos:_,helps:c}=t;return(0,r.jsxs)("div",{className:u()["product-info"],children:[r.jsx(v,{}),(0,r.jsxs)("div",{className:u()["product-info__content"],children:[r.jsx(g,{information:e}),r.jsx(j,{spec:i}),r.jsx(b,{}),r.jsx(P,{})]})]})}function v(){let t=({text:t="",id:e})=>r.jsx("button",{className:`${0===e?u()["product-info__tabs__button--active"]:""} ${u()["product-info__tabs__button"]}`,children:t});return r.jsx("div",{className:u()["product-info__tabs"],children:[{id:0,text:"产品介绍"},{id:1,text:"产品参数"},{id:2,text:"产品视频"},{id:3,text:"产品使用帮助"}].map((e,i)=>r.jsx(t,{text:e.text,id:e.id},i))})}function w({text:t=""}){return r.jsx("h2",{children:t})}function g({information:t}){return(0,r.jsxs)("div",{className:u()["product-info__description"],children:[r.jsx(w,{text:"产品介绍"}),t.map((t,e)=>r.jsx(r.Fragment,{children:t.type===m.tv.image?r.jsx("div",{className:u()["product-info__description__image"],children:r.jsx(f.default,{src:t.src,width:500,height:500,alt:"",style:{width:"100%",height:"100%",objectFit:"contain"}})},e):t.type===m.tv.title?r.jsx("h4",{children:t.text},e):r.jsx("p",{children:t.text},e)}))]})}function j({spec:t}){let e=({isWhite:t=!1,title:e="",value:i=""})=>(0,r.jsxs)("div",{className:`${u()["product-info__spec__item"]} ${t?u()["product-info__spec__item--white"]:""}`,children:[r.jsx("div",{children:e}),r.jsx("div",{children:i})]});return(0,r.jsxs)("div",{className:u()["product-info__spec"],children:[r.jsx(w,{text:"产品参数"}),t.map((t,i)=>r.jsx(e,{isWhite:i%2==1,title:t.type,value:t.detail},i))]})}function b(){let t={infos:[{imageSrc:"/videos-image-1.png",title:"C31复位配网-中英文字幕版",tip:"2021-03-05"},{imageSrc:"/videos-image-1.png",title:"C31复位配网-中英文字幕版",tip:"2021-03-05"},{imageSrc:"/videos-image-1.png",title:"C31复位配网-中英文字幕版",tip:"2021-03-05"},{imageSrc:"/videos-image-1.png",title:"C31复位配网-中英文字幕版",tip:"2021-03-05"}],imageSize:{width:290,height:162},imageBox:{width:290,height:162},mode:h.H.normal,gap:20};return(0,r.jsxs)("div",{className:u()["product-info__videos"],children:[r.jsx(w,{text:"产品视频"}),r.jsx(h.Z,{...t,isDetail:!0})]})}function P(){return(0,r.jsxs)("div",{className:u()["product-info__helps"],children:[r.jsx(w,{text:"产品使用帮助"}),r.jsx(n.Z,{infos:[{imageSrc:"/hotspot-image-1.png",title:"首批天玑8000系列旗舰!疑似OPPO K10系列入网",description:"OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.",time:"2020-11-27",link:"/article"},{imageSrc:"/hotspot-image-1.png",title:"首批天玑8000系列旗舰!疑似OPPO K10系列入网",description:"OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.",time:"2020-11-27",link:"/article"},{imageSrc:"/hotspot-image-1.png",title:"首批天玑8000系列旗舰!疑似OPPO K10系列入网",description:"OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.",time:"2020-11-27",link:"/article"},{imageSrc:"/hotspot-image-1.png",title:"首批天玑8000系列旗舰!疑似OPPO K10系列入网",description:"OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.",time:"2020-11-27",link:"/article"},{imageSrc:"/hotspot-image-1.png",title:"首批天玑8000系列旗舰!疑似OPPO K10系列入网",description:"OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.",time:"2020-11-27",link:"/article"},{imageSrc:"/hotspot-image-1.png",title:"首批天玑8000系列旗舰!疑似OPPO K10系列入网",description:"OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.",time:"2020-11-27",link:"/article"}],isProductDetail:!0})]})}var y=i(1782);async function N({params:t}){let e=await (0,y.b6)(t.productId);return e?(0,r.jsxs)("div",{className:o()["product-detail"],children:[r.jsx("div",{className:o()["product-detail__breadcrumbs"],children:r.jsx(_.Z,{navs:[{link:"/product",name:"产品中心"},{link:"/product",name:"产品分类1"}]})}),r.jsx("div",{className:o()["product-detail__preview"],children:r.jsx(a,{productData:e})}),r.jsx("div",{className:o()["product-detail__info"],children:r.jsx(x,{productData:e})})]}):r.jsx("div",{})}},5251:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(5036);i(878);var _=i(2813),c=i(6274),o=i(6904);async function s({navs:t}){let e=await (0,o.nI)();return(0,r.jsxs)("div",{className:"bread-crumbs",children:[r.jsx(_.default,{src:"/position.svg",width:10,height:13,alt:"position"}),r.jsx(c.default,{className:"bread-crumbs__item",href:"/",children:e("home")}),t.map((t,e)=>(0,r.jsxs)(r.Fragment,{children:[r.jsx(_.default,{src:"/arrow-right.svg",width:11,height:17,alt:">"}),r.jsx(c.default,{className:"bread-crumbs__item",href:t.link,children:t.name},e)]})),r.jsx(_.default,{src:"/arrow-right.svg",width:11,height:17,alt:">"}),r.jsx("span",{className:"bread-crumbs__item--current",children:e("detail")})]})}},4727:(t,e,i)=>{"use strict";i.d(e,{Z:()=>s});var r=i(5036),_=i(2813),c=i(2),o=i.n(c);async function s({infos:t,isProductDetail:e=!1}){let i=({imageSrc:t,title:e,description:i,time:c,link:o="/"})=>(0,r.jsxs)("a",{href:o,className:"flex-box-with-2items__item",children:[r.jsx("div",{children:r.jsx(_.default,{src:t,width:240,height:160,style:{width:"100%",height:"100%"},alt:""})}),(0,r.jsxs)("div",{children:[r.jsx("h6",{children:e}),r.jsx("p",{className:"hide-on-small",children:i}),r.jsx("div",{style:{flex:1}}),r.jsx("span",{children:c})]})]});return r.jsx("div",{className:`flex-box-with-2items ${e?"flex-box-with-2items--product-detail":""}`,children:t.map((t,e)=>r.jsx(o().Fragment,{children:r.jsx(i,{...t})},`${e}-${t.title}`))})}},68:(t,e,i)=>{"use strict";i.d(e,{H:()=>r,Z:()=>p});var r,_=i(5036),c=i(2813),o=i(2),s=i.n(o);i(878);var d=i(6274);function p({infos:t,imageSize:e,imageBox:i,mode:r="",gap:o=0,isDetail:p=!1}){let a=({imageSrc:t,title:o,tip:s="",link:p="",videoSrc:a=""})=>{let n=!!p,l=!!a,u=`flex-box-with-4items__card ${"product"===r?"flex-box-with-4items__card--product":""} ${n?"flex-box-with-4items__card--link":""} ${l?"flex-box-with-4items__card--video":""}`,h=`${""===r?"":`flex-box-with-4items__card__info--${r}`} flex-box-with-4items__card__info`,m=(0,_.jsxs)(_.Fragment,{children:[_.jsx("div",{className:"flex-box-with-4items__card__image",style:{aspectRatio:i.width/i.height},children:l?(0,_.jsxs)("video",{width:e.width,height:e.height,controls:!0,poster:t,children:[_.jsx("source",{src:a,type:"video/mp4"}),"Your browser does not support the video tag."]}):_.jsx(c.default,{unoptimized:!0,src:t,width:e.width,height:e.height,alt:"image",style:{objectFit:"fill",width:"100%",height:"auto"}})}),(0,_.jsxs)("div",{className:h,children:[_.jsx("div",{children:o}),"pride"===r?"":s?_.jsx("span",{children:s}):""]})]});return n?_.jsx(d.default,{href:p,className:u,children:m}):_.jsx("div",{className:u,children:m})};return _.jsx("div",{className:`flex-box-with-4items ${"product"===r?"flex-box-with-4items--product":""} ${p?"flex-box-with-4items--detail":""}`,style:o?{gap:o}:{},children:t.map((t,e)=>_.jsx(s().Fragment,{children:a(t)},`${t.link}-${e}`))})}!function(t){t.normal="",t.product="product",t.pride="pride"}(r||(r={}))},878:()=>{}};var e=require("../../../../../webpack-runtime.js");e.C(t);var i=t=>e(e.s=t),r=e.X(0,[638,47,563,613,782],()=>i(3030));module.exports=r})();