import { HelpData } from '@/data/type'
import styles from './article.module.scss'
import Image from 'next/image'
import { formatDate } from '@/utils/utils'
import React from 'react'
import Link from 'next/link'
import { getCurrentLocale, getI18n } from '@/locales/server'

export default async function ArticleHot({
  datas,
  type,
}: {
  datas: HelpData[]
  type: string
}) {
  const t = await getI18n()
  const locale = await getCurrentLocale()

  const items = datas.map((item) => {
    return {
      imageSrc: item.coverSrc,
      title:
        locale === 'zh' ? item.title : item.titleEn ? item.titleEn : item.title,
      time: formatDate(item.time, 'YYYY-MM-DD'),
      id: item.id,
    }
  })

  const title = type === 'help' ? `${t('hot')}${t('help')}` : ''

  return (
    <div className={styles['article-hot']}>
      <h5>{title}</h5>
      <div className={styles['article-hot__list']}>
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <Item {...item}></Item>
          </React.Fragment>
        ))}
      </div>
    </div>
  )
}

function Item({
  imageSrc,
  title,
  time,
  id,
}: {
  imageSrc: string
  title: string
  time: string
  id: string
}) {
  return (
    <Link href={`/article/${id}`} className={styles['article-hot__list__item']}>
      <Image
        src={imageSrc}
        width={135}
        height={90}
        alt=""
        style={{ borderRadius: 4 }}
      ></Image>
      <div>
        <div>{title}</div>
        <span>{time}</span>
      </div>
    </Link>
  )
}
