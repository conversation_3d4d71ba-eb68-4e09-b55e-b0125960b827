/* 文章相关 */
export enum ArticleContentType {
  paragraph,
  title,
  image
}
export enum ArticleType {
  news = '/news',
  support = '/support',
  product = '/product',
}
type ArticleText = {
  type: ArticleContentType.paragraph | ArticleContentType.title;
  text: string;
  src?: never;
}

type ArticleImage = {
  type: ArticleContentType.image;
  text?: never;
  src: string;
}

export type Article = ArticleText | ArticleImage;

export interface PrevAndNextArticle {
  prev: number
  prevLink: string
  prevTitle: string
  prevTitleEn?: string
  next: number
  nextLink: string
  nextTitle: string
  nextTitleEn?: string
}

export interface ArticleData {
  title: string
  time: Date
  content: Article[]
}
/* 文章相关结束 */
// ----------------------------------------
/* 产品相关 */
export interface ProductData {
  id: string
  groupId: string
  groupName: string
  name: string
  nameEn: string
  imageSrc: string
  subImageSrcs: string[]
  model: string
  description: string
  descriptionEn: string
  properties: string[]
  types: Array<{
    name: string
    properties: string[]
  }>
  information: Article[]
  spec: Array<{
    type: string
    detail: string
  }>
  videos: Array<{
    videoId: string
  }>
  helps: Array<{
    helpId: string
  }>
}

/* 产品相关结束 */
// ----------------------------------------
/* 新闻相关 */
export enum NewsCategory {
  cylan,
  industry
}
export interface NewsData {
  title: string
  tip: string
  time: Date
  content: Array<Article>
  id: string
  category: NewsCategory
  type: ArticleType.news
  linkProductId?: string
  coverSrc: string
}
/* 新闻相关结束 */
// ----------------------------------------
/* 使用帮助相关 */
export interface HelpData {
  title: string
  titleEn?: string
  tip: string
  time: Date
  content: Array<Article>
  contentEn?: Array<Article>
  id: string
  coverSrc: string
  groupId: string
  type: ArticleType
  groupName: string
  groupNameEn: string
  categoryName: string
  categoryNameEn: string
}
/* 使用帮助相关结束 */
// ----------------------------------------
export type PageProps = {
  params: { locale: string; }
  searchParams: { [key: string]: string }
}

export type Group = {
  id: string; name: string; nameEn: string; categoryName?: never; categoryNameEn?: never
} | {
  id: string; name: string; nameEn: string; categoryName: string; categoryNameEn: string
}