import styles from './article.module.scss'
import Link from 'next/link'
import Image from 'next/image'
import {
  PrevAndNextArticle,
  ArticleContentType,
  ArticleData,
} from '@/data/type'
import { formatDate } from '@/utils/utils'
import { getCurrentLocale, getI18n } from '@/locales/server'

export default async function ArticleContent({
  articleData,
  prevAndNext,
}: {
  articleData: ArticleData
  prevAndNext: PrevAndNextArticle
}) {
  enum LinkType {
    prev,
    next,
  }

  const locale = getCurrentLocale()
  const t = await getI18n()

  const LinkItem = ({
    type,
    text,
    link,
  }: {
    type: LinkType
    text: string
    link: string
  }) => (
    <Link href={link} className={styles['article-content__link__item']}>
      <span>
        {type === LinkType.prev ? t('prevArticle') : t('nextArticle')}:
      </span>
      <span>{text}</span>
    </Link>
  )

  const { title, content, time } = articleData

  return (
    <div className={styles['article-content']}>
      <h1>{title}</h1>
      <div className={styles['article-content__time']}>
        {t('time')} : {formatDate(time, 'YYYY-MM-DD')}
      </div>
      <div className={styles['article-content__line']}></div>
      <div className={styles['article-content__content']}>
        {content.map((item, index) => (
          <div key={index}>
            {item.type === ArticleContentType.image ? (
              <div className={styles['article-content__content__image']}>
                <Image
                  src={item.src}
                  width={500}
                  height={500}
                  alt=""
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                  }}
                ></Image>
              </div>
            ) : item.type === ArticleContentType.title ? (
              <h4>{item.text}</h4>
            ) : (
              <p>{item.text}</p>
            )}
          </div>
        ))}
      </div>
      <div className={styles['article-content__link']}>
        {prevAndNext.prev >= 0 && (
          <LinkItem
            type={LinkType.prev}
            text={
              locale === 'zh'
                ? prevAndNext.prevTitle
                : prevAndNext.prevTitleEn
                ? prevAndNext.prevTitleEn
                : prevAndNext.prevTitle
            }
            link={prevAndNext.prevLink}
          />
        )}
        {prevAndNext.next >= 0 && (
          <LinkItem
            type={LinkType.next}
            text={
              locale === 'zh'
                ? prevAndNext.nextTitle
                : prevAndNext.nextTitleEn
                ? prevAndNext.nextTitleEn
                : prevAndNext.nextTitle
            }
            link={prevAndNext.nextLink}
          />
        )}
      </div>
    </div>
  )
}
