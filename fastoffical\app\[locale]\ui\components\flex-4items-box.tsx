import Image from 'next/image'
import React from 'react'
import './components.scss'
import Link from 'next/link'

export type CardInfo = {
  imageSrc: string
  title: string
  tip?: string
  link?: string
  videoSrc?: string
}
export type Flex4ItemsInfo = Array<CardInfo>
export enum itemsMode {
  normal = '',
  product = 'product',
  pride = 'pride',
}

export default function Flex4ItemsBox({
  infos,
  imageSize,
  imageBox,
  mode = itemsMode.normal,
  gap = 0,
  isDetail = false,
}: {
  infos: Flex4ItemsInfo
  imageSize: {
    width: number
    height: number
  }
  imageBox: {
    width: number
    height: number
  }
  mode?: itemsMode
  gap?: number
  isDetail?: boolean
}) {
  const renderCard = ({
    imageSrc,
    title,
    tip = '',
    link = '',
    videoSrc = '',
  }: CardInfo) => {
    const isLinkCard = !!link
    const isVideoCard = !!videoSrc

    const cardClassName = `flex-box-with-4items__card ${
      mode === itemsMode.product ? 'flex-box-with-4items__card--product' : ''
    } ${isLinkCard ? 'flex-box-with-4items__card--link' : ''} ${
      isVideoCard ? 'flex-box-with-4items__card--video' : ''
    }`

    const infoClassName = `${
      mode === itemsMode.normal
        ? ''
        : `flex-box-with-4items__card__info--${mode}`
    } flex-box-with-4items__card__info`

    const cardContent = (
      <>
        <div
          className="flex-box-with-4items__card__image"
          style={{ aspectRatio: imageBox.width / imageBox.height }}
        >
          {isVideoCard ? (
            <video
              width={imageSize.width}
              height={imageSize.height}
              controls
              poster={imageSrc}
            >
              <source src={videoSrc} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          ) : (
            <Image
              unoptimized
              src={imageSrc}
              width={imageSize.width}
              height={imageSize.height}
              alt="image"
              style={{
                objectFit: 'fill',
                width: '100%',
                height: 'auto',
              }}
            ></Image>
          )}
        </div>
        <div className={infoClassName}>
          <div>{title}</div>
          {mode === itemsMode.pride ? '' : tip ? <span>{tip}</span> : ''}
        </div>
      </>
    )

    return isLinkCard ? (
      <Link href={link} className={cardClassName}>
        {cardContent}
      </Link>
    ) : (
      <div className={cardClassName}>{cardContent}</div>
    )
  }
  return (
    <div
      className={`${`flex-box-with-4items`} ${
        mode === itemsMode.product ? 'flex-box-with-4items--product' : ''
      } ${isDetail ? 'flex-box-with-4items--detail' : ''}`}
      style={gap ? { gap } : {}}
    >
      {infos.map((info: CardInfo, index: number) => (
        <React.Fragment key={`${info.link}-${index}`}>
          {renderCard(info)}
        </React.Fragment>
      ))}
    </div>
  )
}
