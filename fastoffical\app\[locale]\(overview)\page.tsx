import Image from 'next/image'
import styles from '@/app/[locale]/page.module.scss'
import BannerSlider from '@/app/[locale]/ui/home/<USER>'
import HotSpot from '@/app/[locale]/ui/home/<USER>'
import About from '@/app/[locale]/ui/home/<USER>'
import { PageProps } from '@/data/type'
import type { Metadata, ResolvingMetadata } from 'next'

export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    title:
      locale === 'zh'
        ? '深圳市赛蓝科技有限公司-智能家居品牌-看家王智能摄像头-赛蓝加菲狗产品网站'
        : 'Cylan - Cylan Clever Dog English Site',
    description: '赛蓝科技 引领生活',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default function Home() {
  return (
    <>
      <BannerSlider />
      <HotSpot />
      <About />
    </>
  )
}
