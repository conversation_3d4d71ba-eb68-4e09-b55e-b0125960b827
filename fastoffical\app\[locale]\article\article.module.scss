.article {
  width: var(--width-content);
  margin: auto;
  &__breadcrumbs {
    height: 65px;
    display: flex;
    align-items: center;
  }
  &__page {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    h3 {
      margin-bottom: 17.5px;
    }
    &__news {
      width: 100%;
    }
    &__other {
      width: 100%;
      &__card {
        border-radius: 6px;
        background-color: #fff;
        padding: 20px;
        display: flex;
        gap: 10px;
        justify-content: center;
        align-items: center;
        > div {
          display: flex;
          flex-direction: column;
          gap: 10px;
          justify-content: center;
          > span {
            font-size: var(--font-medium);
          }
          > p {
            font-size: var(--font-small);
            color: var(--text-description);
          }
          > div {
            display: flex;
            flex-direction: column;
            gap: 4px;
            span {
              font-size: var(--font-small);
              color: var(--text-mark);
            }
          }
        }
      }
    }
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .article {
    padding: 0 20px;
    &__page {
      width: 100%;
      flex-direction: column;
      gap: 31.5px;
    }
  }
}

@media (max-width: 450px) {
  .article {
    padding: 0 16px;
    &__breadcrumbs {
      height: 37px;
    }
    &__page {
      width: 100%;
      flex-direction: column;
      gap: 20px;
    }
  }
}
