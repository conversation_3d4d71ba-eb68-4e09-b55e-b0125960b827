(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[203],{4627:function(e,t,n){Promise.resolve().then(n.bind(n,6222)),Promise.resolve().then(n.bind(n,8701)),Promise.resolve().then(n.bind(n,5927)),Promise.resolve().then(n.t.bind(n,548,23)),Promise.resolve().then(n.t.bind(n,9989,23))},6222:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return l}});var _=n(7437),o=n(703),i=n(2265);n(2135);var s=n(5421);function l(){return(0,_.jsx)(s.e4,{locale:(0,s.eV)(),children:(0,_.jsx)(r,{})})}function r(){let e=(0,s.QT)(),t=(0,s.eV)(),[n,l]=(0,i.useState)(!1),r=null,a=!1;return(0,i.useEffect)(()=>{let e=()=>{l((window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop)>window.innerHeight)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]),(0,_.jsxs)("button",{className:"back-top ".concat(n?"":"back-top--hide"),onClick:()=>{a||(a=!0,r&&clearTimeout(r),r=setTimeout(()=>{window.scrollTo({top:0,behavior:"smooth"}),a=!1},100))},children:[(0,_.jsx)(o.default,{src:"/back-top.svg",width:26,height:26,alt:"Top"}),"zh"===t&&(0,_.jsx)("span",{className:"hide-on-small",children:e("backToTop")})]})}},1426:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var _=n(7437),o=n(703),i=n(8792);function s(e){let{children:t,list:n,show:s=!1,onClick:l=()=>{},onClickMask:r=()=>{}}=e,a=e=>{l(e)};return(0,_.jsxs)("div",{style:{position:"relative"},children:[t,s&&(0,_.jsxs)(_.Fragment,{children:[(0,_.jsxs)("div",{className:"dropdown-window",children:[(0,_.jsx)("div",{className:"dropdown-window__above",children:(0,_.jsx)(o.default,{src:"/dropdown-window-above.svg",width:15,height:8,alt:"drop"})}),(0,_.jsx)("div",{className:"dropdown-window__list",children:n.map((e,t)=>(0,_.jsx)("div",{children:e.link?(0,_.jsx)(i.default,{href:e.link,children:(0,_.jsx)("div",{className:"dropdown-window__list__item",onClick:()=>{a(e.link)},children:e.text})}):(0,_.jsx)("div",{className:"dropdown-window__list__item",onClick:()=>{a(null==e?void 0:e.id)},children:e.text})},t))}),(0,_.jsx)("div",{className:"dropdown-window__placeholder"})]}),(0,_.jsx)("div",{className:"dropdown-window__mask hide-on-medium hide-on-large",onClick:()=>{r()}})]})]})}},1649:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var _=n(7437),o=n(8792),i=n(703),s=n(2265),l=n(5421);function r(e){let{isFooter:t=!1,onClick:n=()=>{}}=e;return(0,_.jsx)(l.e4,{locale:(0,l.eV)(),children:(0,_.jsx)(a,{isFooter:t,onClick:n})})}function a(e){let{isFooter:t=!1,onClick:n}=e,o=(0,l.QT)(),i=[{iconSrc:"/menu-home-icon.svg",title:o("home"),id:0,link:"/"},{iconSrc:"/menu-product-icon.svg",title:o("productCenter"),id:1,active:!1},{subNavs:[{title:o("productCamera"),link:"/product",id:1}]},{iconSrc:"/menu-support-icon.svg",title:o("support"),link:"/support",id:3},{iconSrc:"/menu-about-icon.svg",title:o("aboutCylan"),link:"/about",id:5}],[r,a]=(0,s.useState)(i),d=(e,t)=>{if(t){n&&n();return}let _=[...r];_.forEach(t=>{t.id===e&&(t.active=!t.active)}),a(_)};return(0,_.jsx)("div",{className:"nav-list ".concat(t?"nav-list--footer":""),children:r.map((e,t)=>(0,_.jsx)(c,{navItemPros:e,navItemStatus:r,onClick:d},t))})}function c(e){let{navItemPros:t,navItemStatus:n,onClick:s}=e;if(t.subNavs){let e=t.subNavs,i=e[0].id,l=n.find(e=>(null==e?void 0:e.id)===i),r=()=>{s(i,!0)};return(0,_.jsx)(_.Fragment,{children:(null==l?void 0:l.active)&&e.map((e,t)=>(0,_.jsx)(o.default,{href:e.link,onClick:r,children:(0,_.jsxs)("div",{className:"nav-list__item nav-list__item--sub",children:[(0,_.jsx)("div",{className:"nav-list__item__icon"}),(0,_.jsx)("span",{className:"nav-list__item__title",children:e.title})]})},t))})}{let{iconSrc:e,title:n,link:l,id:r,active:a}=t,c=()=>(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("div",{className:"nav-list__item__icon",children:e&&(0,_.jsx)(i.default,{src:e,width:24,height:24,alt:""})}),(0,_.jsx)("span",{className:"nav-list__item__title",children:n}),(0,_.jsx)("div",{style:{flex:1}}),!l&&(0,_.jsx)("div",{className:"nav-list__item__button",children:(0,_.jsx)(i.default,{src:"/menu-arrow-".concat(a?"up":"down",".svg"),width:32,height:32,alt:""})})]});return(0,_.jsx)(_.Fragment,{children:(0,_.jsxs)("div",{onClick:()=>{s(r,!!l)},className:"nav-list__item",children:[!l&&(0,_.jsx)(c,{}),l&&(0,_.jsx)(o.default,{style:{width:"100%",height:"100%"},className:"nav-list__item",href:l,children:(0,_.jsx)(c,{})})]})})}}},8701:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return d}});var _=n(7437),o=n(2265),i=n(1809),s=n.n(i),l=n(703),r=n(8792),a=n(1649),c=n(5421);function d(){return(0,_.jsx)(c.e4,{locale:(0,c.eV)(),children:(0,_.jsx)(u,{})})}function u(){let e=(0,c.QT)(),t=e=>{let{link:t,text:n}=e;return(0,_.jsx)("a",{href:t,children:(0,_.jsx)("span",{children:n})})},n=e=>{let{title:n,links:i}=e;return(0,_.jsxs)("div",{className:s().footer__links__item,children:[(0,_.jsx)("div",{children:n}),i.map((e,n)=>(0,_.jsx)(o.Fragment,{children:t({link:e.link,text:e.text})},"".concat(e.link,"-").concat(n)))]})},i=[{link:"/product?tab=01",text:e("productCamera")},{link:"/product?tab=02",text:e("productTranslator")}],d=[{link:"/support/download_client",text:e("downloadClient")},{link:"/support/help",text:e("help")}],u=[{link:"/about#about-cylan",text:e("aboutCylan")},{link:"/about#prides",text:e("cylanPrides")},{link:"/about#contacts",text:e("contactUs")}],h=()=>{let[t,n]=(0,o.useState)(!1);return(0,_.jsxs)("div",{className:s().footer__links__follow,children:[(0,_.jsx)("div",{children:e("followUs")}),(0,_.jsxs)("div",{onMouseEnter:()=>{n(!0)},onMouseLeave:()=>{n(!1)},className:s().footer__links__follow__weixin,children:[(0,_.jsx)(l.default,{src:"/weixin.svg",width:20,height:20,alt:"weixin"}),t&&(0,_.jsxs)("div",{children:[(0,_.jsx)(l.default,{src:"/support/imcam-gongzhonghao.jpg",width:140,height:140,alt:""}),(0,_.jsx)("span",{children:e("imcamGongzhonghao")}),(0,_.jsx)("a",{className:"hide-on-medium hide-on-large",href:"/support/imcam-gongzhonghao.jpg",download:!0,children:e("downloadQRcode")})]})]})]})};return(0,_.jsxs)("div",{className:s().footer,children:[(0,_.jsx)("div",{className:"".concat(s().footer__logo," hide-on-medium hide-on-large"),children:(0,_.jsx)(r.default,{href:"/",children:(0,_.jsx)(l.default,{src:"/cylan_logo-white.png",width:125,height:44,alt:"logo",unoptimized:!0})})}),(0,_.jsxs)("div",{className:"".concat(s().footer__links," hide-on-small"),children:[(0,_.jsx)("div",{className:"".concat(s().footer__logo),children:(0,_.jsx)(r.default,{href:"/",children:(0,_.jsx)(l.default,{src:"/cylan_logo-white.png",width:125,height:44,alt:"logo",unoptimized:!0})})}),(0,_.jsx)(n,{title:e("productCenter"),links:i}),(0,_.jsx)(n,{title:e("support"),links:d}),(0,_.jsx)(n,{title:e("aboutUs"),links:u}),(0,_.jsx)(h,{})]}),(0,_.jsxs)("div",{className:"hide-on-large hide-on-medium",children:[(0,_.jsx)(a.Z,{isFooter:!0}),(0,_.jsx)(h,{})]}),(0,_.jsxs)("div",{className:s().footer__copyright,children:[e("copyrightText"),(0,_.jsx)(r.default,{className:s().footer__copyright__link,href:"https://beian.miit.gov.cn/",target:"_blank",children:e("copyrightLink")})]})]})}},5927:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return h}});var _=n(7437),o=n(1809),i=n.n(o),s=n(8792),l=n(703),r=n(1426),a=n(2265),c=n(7907),d=n(1649),u=n(5421);function h(){return"__DEFAULT__"===(0,c.useSelectedLayoutSegment)()?(0,_.jsx)("div",{}):(0,_.jsx)(u.e4,{locale:(0,u.eV)(),children:(0,_.jsx)(m,{})})}function m(){let e=(0,u.QT)(),[t,n]=(0,a.useState)(!1),[o,r]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=()=>{n(window.scrollY>0)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]);let c=()=>{r(!1)};return(0,_.jsxs)("div",{style:{position:"relative"},children:[(0,_.jsx)("div",{className:i().nav__placeholder}),(0,_.jsx)("div",{className:"".concat(i().nav," ").concat(t?i()["nav--scrolled"]:""," ").concat(o?i()["nav--scrolled"]:""),children:(0,_.jsxs)("div",{className:i().nav__content,children:[(0,_.jsx)(s.default,{href:"/",style:{height:44},children:(0,_.jsx)(l.default,{src:"/cylan_logo.png",width:125,height:44,alt:"Cylan Logo",unoptimized:!0})}),(0,_.jsxs)("div",{className:"".concat(i().nav__list," hide-on-small hide-on-medium"),children:[(0,_.jsx)(v,{title:e("home"),link:"/"}),(0,_.jsx)(v,{title:e("productCenter"),showArrow:!0,link:"/product",links:[{link:"/product?tab=01",text:e("productCamera")},{link:"/product?tab=02",text:e("productTranslator")}]}),(0,_.jsx)(v,{title:e("support"),showArrow:!0,link:"/support",links:[{link:"/support/download_client",text:e("downloadClient")},{link:"/support/help",text:e("help")}]}),(0,_.jsx)(v,{title:e("aboutUs"),link:"/about"})]}),(0,_.jsxs)("div",{className:i().nav__right,children:[(0,_.jsx)(p,{}),(0,_.jsx)("button",{onClick:()=>{r(!o)},className:"".concat(i().nav__right__menu," hide-on-large"),children:(0,_.jsx)(l.default,{src:"/menu.svg",width:49,height:50,alt:"menu"})})]})]})}),o&&(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("div",{className:"".concat(i().nav__drop," hide-on-large"),children:(0,_.jsx)(d.Z,{onClick:()=>c()})}),(0,_.jsx)("div",{className:i().nav__mask,onClick:()=>{r(!1)}})]})]})}function f(){return(0,_.jsx)(l.default,{src:"/arrow-down.svg",height:10,width:16,alt:""})}function v(e){let{title:t,link:n="/",showArrow:o=!1,links:l=[]}=e,[d,u]=(0,a.useState)(!1),h=(0,c.usePathname)();return h="/"+(h=h.includes("/zh/")||h.includes("/en/")?h.replace(/\/zh|\/en/,""):"/").split("/")[1],(0,_.jsx)("div",{onMouseEnter:()=>{u(!0)},onMouseLeave:()=>{u(!1)},children:o?(0,_.jsx)(r.Z,{onClick:()=>u(!1),list:l,show:d,children:(0,_.jsx)(s.default,{href:n,children:(0,_.jsxs)("div",{className:"".concat(i().nav__list__item," ").concat(h===n.split("?")[0]?i()["nav__list__item--active"]:""),children:[t," ",o&&(0,_.jsx)(f,{})]})})}):(0,_.jsx)(s.default,{href:n,children:(0,_.jsxs)("div",{className:"".concat(i().nav__list__item," ").concat(i()["nav__list__item--link"]," ").concat(h==="/".concat(n.split("/")[1])?i()["nav__list__item--active"]:""),children:[t," ",o&&(0,_.jsx)(f,{})]})})})}function p(){let e=(0,u.Zt)(),t=(0,u.eV)(),n=(0,u.QT)(),o=t=>{let{lang:o,isActive:s=!1}=t;return(0,_.jsx)("span",{onClick:()=>{s||e(o)},className:"".concat(s?i()["nav__right__language__text--active"]:""," ").concat(i().nav__right__language__text),children:"zh"===o?n("chinese"):n("english")})};return(0,_.jsxs)("div",{className:i().nav__right__language,children:[(0,_.jsx)(o,{lang:"zh",isActive:"zh"===t}),(0,_.jsx)("span",{children:"/"}),(0,_.jsx)(o,{lang:"en",isActive:"en"===t})]})}},5421:function(e,t,n){"use strict";n.d(t,{QT:function(){return _},Zt:function(){return l},e4:function(){return i},eV:function(){return s}});let{useI18n:_,useScopedI18n:o,I18nProviderClient:i,useCurrentLocale:s,useChangeLocale:l}=(0,n(8333).createI18nClient)({en:()=>n.e(673).then(n.bind(n,6673)),zh:()=>n.e(105).then(n.bind(n,6105))})},8333:function(e,t,n){"use strict";var _,o,i=Object.create,s=Object.defineProperty,l=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,a=Object.getOwnPropertySymbols,c=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,h=(e,t,n)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t)=>{for(var n in t||(t={}))d.call(t,n)&&h(e,n,t[n]);if(a)for(var n of a(t))u.call(t,n)&&h(e,n,t[n]);return e},f=(e,t,n,_)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of r(t))d.call(e,o)||o===n||s(e,o,{get:()=>t[o],enumerable:!(_=l(t,o))||_.enumerable});return e},v={};((e,t)=>{for(var n in t)s(e,n,{get:t[n],enumerable:!0})})(v,{createI18nClient:()=>T}),e.exports=f(s({},"__esModule",{value:!0}),v),n(2219);var p=n(7907),g=(o=null!=(_=n(2265))?i(c(_)):{},f(_&&_.__esModule?o:s(o,"default",{value:_,enumerable:!0}),_)),x=(e,t="")=>Object.entries(e).reduce((e,[n,_])=>m(m({},e),"string"==typeof _?{[t+n]:_}:x(_,`${t}${n}.`)),{}),b=e=>null,j=e=>null,w=new Map,k=n(2265),N=n(2265),y=n(2265);function C(e,t){let{localeContent:n,fallbackLocale:_}=e,o=_&&"string"==typeof n?_:Object.assign(null!=_?_:{},n),i=new Set(Object.keys(o).filter(e=>e.includes("#")).map(e=>e.split("#",1)[0])),s=new Intl.PluralRules(e.locale);return function(e,...n){var _,l,r;let a=n[0],c=!1;a&&"count"in a&&(t?i.has(`${t}.${e}`):i.has(e))&&(e=`${e}#${0===(r=a.count)?"zero":s.select(r)}`,c=!0);let d=t?o[`${t}.${e}`]:o[e];if(!d&&c){let t=e.split("#",1)[0];d=null==(_=o[`${t}#other`]||e)?void 0:_.toString()}else d=null==(l=d||e)?void 0:l.toString();if(!a)return d;let u=!0,h=null==d?void 0:d.split(/({[^}]*})/).map((e,t)=>{let n=e.match(/{(.*)}/);if(n){let e=n[1],_=a[e];return(0,y.isValidElement)(_)?(u=!1,(0,y.cloneElement)(_,{key:`${String(e)}-${t}`})):_}return e});return u?null==h?void 0:h.join(""):h}}var P=n(2265),S=n(7907),O=n(7907),E=n(2265);function T(e,t={}){let n=Object.keys(e),_=(0,k.createContext)(null),o=function(){var e;let _=(0,O.useParams)()[null!=(e=t.segmentName)?e:"locale"];return(0,E.useMemo)(()=>{for(let e of n)if(_===e)return e;j(`Locale "${_}" not found in locales (${n.join(", ")}), returning "notFound()"`),(0,O.notFound)()},[_])};return{useI18n:function(){let e=(0,N.useContext)(_);if(!e)throw Error("`useI18n` must be used inside `I18nProvider`");return(0,N.useMemo)(()=>C(e,void 0),[e])},useScopedI18n:function(e){let t=(0,P.useContext)(_);if(!t)throw Error("`useI18n` must be used inside `I18nProvider`");return(0,P.useMemo)(()=>C(t,e),[t,e])},I18nProviderClient:function(e,t,n){function _({locale:t,importLocale:_,children:o}){var i;let s=null!=(i=w.get(t))?i:(0,g.use)(_).default;w.has(t)||w.set(t,s);let l=(0,g.useMemo)(()=>({localeContent:x(s),fallbackLocale:n?x(n):void 0,locale:t}),[s,t]);return g.default.createElement(e.Provider,{value:l},o)}return function({locale:e,fallback:n,children:o}){let i=t[e];return i||(j(`The locale '${e}' is not supported. Defined locales are: [${Object.keys(t).join(", ")}].`),(0,p.notFound)()),g.default.createElement(g.Suspense,{fallback:n},g.default.createElement(_,{locale:e,importLocale:i()},o))}}(_,e,t.fallbackLocale),I18nClientContext:_,useChangeLocale:function(n){let{push:_,refresh:i}=(0,S.useRouter)(),s=o(),l=(0,S.usePathname)(),r=(null==n?void 0:n.preserveSearchParams)?(0,S.useSearchParams)().toString():void 0,a=r?`?${r}`:"",c=l;return t.basePath&&(c=c.replace(t.basePath,"")),c.startsWith(`/${s}/`)?c=c.replace(`/${s}/`,"/"):c===`/${s}`&&(c="/"),function(t){if(t===s)return;let n=e[t];if(!n){b(`The locale '${t}' is not supported. Defined locales are: [${Object.keys(e).join(", ")}].`);return}n().then(e=>{w.set(t,e.default),_(`/${t}${c}${a}`),i()})}},defineLocale:function(e){return e},useCurrentLocale:o}}},703:function(e,t,n){"use strict";n.d(t,{default:function(){return o.a}});var _=n(7447),o=n.n(_)},8792:function(e,t,n){"use strict";n.d(t,{default:function(){return o.a}});var _=n(5250),o=n.n(_)},7907:function(e,t,n){"use strict";n.r(t);var _=n(5313),o={};for(var i in _)"default"!==i&&(o[i]=(function(e){return _[e]}).bind(0,i));n.d(t,o)},2219:function(){},7447:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getImageProps:function(){return l},default:function(){return r}});let _=n(6921),o=n(8630),i=n(1749),s=_._(n(536)),l=e=>{let{props:t}=(0,o.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}},r=i.Image},2135:function(){},9989:function(){},1809:function(e){e.exports={"nav--scrolled":"home_nav--scrolled__f5oaX",nav:"home_nav__gr65i",nav__placeholder:"home_nav__placeholder__R_bDj",nav__content:"home_nav__content__gXoig",nav__list:"home_nav__list__dmRBz",nav__list__item:"home_nav__list__item__Ti9E4","nav__list__item--link":"home_nav__list__item--link__dx88I","nav__list__item--active":"home_nav__list__item--active__oPRJX",nav__right:"home_nav__right__4GRXj",nav__right__language:"home_nav__right__language__YzU4O",nav__right__language__text:"home_nav__right__language__text__yUNmB","nav__right__language__text--active":"home_nav__right__language__text--active__e4h1y",nav__right__search:"home_nav__right__search__QAvd_",nav__right__menu:"home_nav__right__menu__tMG4s",nav__drop:"home_nav__drop__RNd3y",nav__mask:"home_nav__mask__YVj5E","banner-slider":"home_banner-slider__UBj9I","banner-slider__swiper":"home_banner-slider__swiper__9Bl8q","banner-slider__slide":"home_banner-slider__slide__2U7Uu","banner-slider__button":"home_banner-slider__button__GKjGy","swiper-button-disabled":"home_swiper-button-disabled__siaDk","banner-slider__button-prev":"home_banner-slider__button-prev__VeikB","banner-slider__button-next":"home_banner-slider__button-next__UC5d2","banner-slider__pagination":"home_banner-slider__pagination__J58et","banner-slider__bullet":"home_banner-slider__bullet__c3a9X","banner-slider__bullet--active":"home_banner-slider__bullet--active__5BpSZ","banner-slider__switcher":"home_banner-slider__switcher__SoaxS","banner-slider__switcher--right":"home_banner-slider__switcher--right___84yN","banner-slider__indicator":"home_banner-slider__indicator__0OOU4","banner-slider__indicator__item":"home_banner-slider__indicator__item__f8vBh","hot-spot":"home_hot-spot__HmXBc","hot-spot__captain":"home_hot-spot__captain__P7sAg","hot-spot__captain__more":"home_hot-spot__captain__more__hoe30","hot-spot__news":"home_hot-spot__news__mFPbX","hot-spot__news__left":"home_hot-spot__news__left__bYNbF","hot-spot__news__right":"home_hot-spot__news__right__IYxxG","hot-spot__news__item":"home_hot-spot__news__item__i6svw","hot-spot__news__item__info":"home_hot-spot__news__item__info__GSDkz","hot-spot__news__item__image":"home_hot-spot__news__item__image__0Dj0A","hot-spot__news__item__image--right":"home_hot-spot__news__item__image--right__scey9","hot-spot__news__item--left":"home_hot-spot__news__item--left__W7YL9",about:"home_about__vPbFi",about__cover:"home_about__cover__SPvuD",about__content:"home_about__content__EA9EW",about__content__time:"home_about__content__time__HcHq6",about__content__time__item:"home_about__content__time__item__n4W8C","about__content__time--page":"home_about__content__time--page__Azkeq",about__content__prides:"home_about__content__prides__zHCpT",contacts:"home_contacts__TRH4N","contacts--page":"home_contacts--page__0BV0w",contacts__info:"home_contacts__info__pIGy0",contacts__info__items:"home_contacts__info__items__qUSi9",contacts__info__title:"home_contacts__info__title__3_UHT",contacts__info__item:"home_contacts__info__item__eDIm0",contacts__address:"home_contacts__address___ZQdr",footer:"home_footer__qefFZ",footer__logo:"home_footer__logo__jG71u",footer__links:"home_footer__links__q5uiZ",footer__links__item:"home_footer__links__item__gB0TO",footer__links__follow:"home_footer__links__follow__jv8nP",footer__links__follow__weixin:"home_footer__links__follow__weixin__yeCNp",footer__copyright:"home_footer__copyright__M6lua",footer__copyright__link:"home_footer__copyright__link__PBT0B","banner-slider__indicator__item--active":"home_banner-slider__indicator__item--active__Pkcak"}},548:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}},function(e){e.O(0,[647,912,971,69,744],function(){return e(e.s=4627)}),_N_E=e.O()}]);