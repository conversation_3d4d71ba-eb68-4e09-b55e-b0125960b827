import Flex2ItemsBox from '../components/flex-2items-box'
import styles from './product.module.scss'
import Flex4ItemsBox, {
  Flex4ItemsInfo,
  itemsMode,
} from '@/app/[locale]/ui/components/flex-4items-box'
import { ProductData, Article, ArticleContentType } from '@/data/type'
import Image from 'next/image'

export default function ProductInfo({
  productData,
}: {
  productData: ProductData
}) {
  const { information, spec, videos, helps } = productData

  return (
    <div className={styles['product-info']}>
      <Tabs />
      <div className={styles['product-info__content']}>
        <Description information={information} />
        <Spec spec={spec} />
        <Videos />
        <Helps />
      </div>
    </div>
  )
}

function Tabs() {
  const tabs = [
    {
      id: 0,
      text: '产品介绍',
    },
    {
      id: 1,
      text: '产品参数',
    },
    { id: 2, text: '产品视频' },
    { id: 3, text: '产品使用帮助' },
  ]
  const currentTab = 0

  const TabItem = ({ text = '', id }: { text: string; id: number }) => (
    <button
      className={`${
        currentTab === id ? styles['product-info__tabs__button--active'] : ''
      } ${styles['product-info__tabs__button']}`}
    >
      {text}
    </button>
  )

  return (
    <div className={styles['product-info__tabs']}>
      {tabs.map((item, index) => (
        <TabItem key={index} text={item.text} id={item.id}></TabItem>
      ))}
    </div>
  )
}

function Title({ text = '' }: { text: string }) {
  return <h2>{text}</h2>
}

function Description({ information }: { information: Article[] }) {
  return (
    <div className={styles['product-info__description']}>
      <Title text="产品介绍" />

      {information.map((item, index) => (
        <>
          {item.type === ArticleContentType.image ? (
            <div
              key={index}
              className={styles['product-info__description__image']}
            >
              <Image
                src={item.src}
                width={500}
                height={500}
                alt=""
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                }}
              ></Image>
            </div>
          ) : item.type === ArticleContentType.title ? (
            <h4 key={index}>{item.text}</h4>
          ) : (
            <p key={index}>{item.text}</p>
          )}
        </>
      ))}
    </div>
  )
}

function Spec({
  spec,
}: {
  spec: Array<{
    type: string
    detail: string
  }>
}) {
  const SpecItem = ({ isWhite = false, title = '', value = '' }) => (
    <div
      className={`${styles['product-info__spec__item']} ${
        isWhite ? styles['product-info__spec__item--white'] : ''
      }`}
    >
      <div>{title}</div>
      <div>{value}</div>
    </div>
  )

  return (
    <div className={styles['product-info__spec']}>
      <Title text="产品参数" />
      {spec.map((item, index) => (
        <SpecItem
          key={index}
          isWhite={index % 2 === 1}
          title={item.type}
          value={item.detail}
        ></SpecItem>
      ))}
    </div>
  )
}

function Videos() {
  const VideosInfo: {
    infos: Flex4ItemsInfo
    imageSize: {
      width: number
      height: number
    }
    imageBox: {
      width: number
      height: number
    }
    mode: itemsMode
    gap: number
  } = {
    infos: [
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
    ],
    imageSize: {
      width: 290,
      height: 162,
    },
    imageBox: {
      width: 290,
      height: 162,
    },
    mode: itemsMode.normal,
    gap: 20,
  }

  return (
    <div className={styles['product-info__videos']}>
      <Title text="产品视频" />
      <Flex4ItemsBox {...VideosInfo} isDetail />
    </div>
  )
}

function Helps() {
  const infos = [
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
  ]
  return (
    <div className={styles['product-info__helps']}>
      <Title text="产品使用帮助" />
      <Flex2ItemsBox infos={infos} isProductDetail />
    </div>
  )
}
