"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/product/(overview)/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/product/product-list.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/ui/product/product-list.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductListLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _product_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./product.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/product/product.module.scss\");\n/* harmony import */ var _product_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_product_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(app-pages-browser)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/flex-4items-box */ \"(app-pages-browser)/./app/[locale]/ui/components/flex-4items-box.tsx\");\n/* harmony import */ var _components_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/pagination */ \"(app-pages-browser)/./app/[locale]/ui/components/pagination.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\nfunction ProductListLayout(param) {\n    let { groups, products } = param;\n    _s();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductList, {\n            groups: groups,\n            products: products\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductListLayout, \"9zQ3KOL0Rwq6cPrON/AdiF1Ksas=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale\n    ];\n});\n_c = ProductListLayout;\nfunction ProductList(param) {\n    let { groups, products, initialTab } = param;\n    _s1();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const tabs = [\n        ...groups\n    ];\n    tabs.unshift({\n        id: \"00\",\n        name: t(\"all\"),\n        nameEn: t(\"all\")\n    });\n    const _tabs = [\n        {\n            id: \"0\",\n            text: \"全部\"\n        },\n        {\n            id: \"1\",\n            text: \"摄像机类型\"\n        }\n    ];\n    // 根据initialTab设置初始选中的tab\n    const getInitialTab = ()=>{\n        if (initialTab) {\n            // 检查initialTab是否存在于tabs中\n            const foundTab = tabs.find((tab)=>tab.id === initialTab);\n            if (foundTab) {\n                return initialTab;\n            }\n        }\n        return tabs[0].id;\n    };\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTab());\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const initialTab = getInitialTab();\n        return getTabProducts(initialTab).length;\n    });\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const getCurrentProducts = (page, groupId)=>{\n        let datas = products.filter((_, index)=>index >= (page - 1) * 8 && index < page * 8);\n        if (groupId !== tabs[0].id) datas = datas.filter((item)=>item.groupId === groupId);\n        return datas;\n    };\n    const getTabProducts = (groupId)=>{\n        if (groupId === tabs[0].id) return products;\n        else {\n            return products.filter((item)=>item.groupId === groupId);\n        }\n    };\n    const [currentProducts, setCurrentProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        const initialTab = getInitialTab();\n        return getCurrentProducts(1, initialTab);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: t(\"productCenter\"),\n                iconSrc: \"/product-center-icon.svg\",\n                currentTab: currentTab,\n                tabs: _tabs,\n                background: \"rgb(200,228,250)\",\n                bannerSrc: \"/product/banner-pc.jpg\",\n                bannerMobileSrc: \"/product/banner-mobile.png\",\n                isSearch: true,\n                onTabChange: (tab)=>{\n                    setCurrentTab(tab);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list\"]),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                        tabs: tabs,\n                        currentTab: currentTab,\n                        onClick: (id)=>{\n                            setCurrentTab(id);\n                            setCurrentPage(1);\n                            const products = getCurrentProducts(1, id);\n                            setCount(getTabProducts(id).length);\n                            setCurrentProducts(products);\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(List, {\n                        products: currentProducts\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListPagination, {\n                        currentPage: currentPage,\n                        count: count,\n                        onChange: (page)=>{\n                            setCurrentPage(page);\n                            setCurrentProducts(getCurrentProducts(page, currentTab));\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(ProductList, \"jaLPWgP03nbbl3z0a8SrdOhQ12s=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n\n    ];\n});\n_c1 = ProductList;\nfunction Tabs(param) {\n    let { tabs, currentTab, onClick } = param;\n    _s2();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const TabItem = (param)=>{\n        let { name, id, nameEn } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>{\n                if (id !== currentTab) onClick(id);\n            },\n            className: \"\".concat((_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs__item\"]), \" \").concat(currentTab === id ? (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs__item--active\"]) : \"\"),\n            children: locale === \"zh\" ? name : nameEn\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 162,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat((_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs\"]), \" hide-on-small\"),\n        children: tabs.map((item, index)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(TabItem, {\n                ...item,\n                key: index,\n                __source: {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                },\n                __self: this\n            }))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n_s2(Tabs, \"0bgUYh39Ymd94mXW1RH411vtZ4A=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale\n    ];\n});\n_c2 = Tabs;\nfunction List(param) {\n    let { products } = param;\n    _s3();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const infos = products.map((item)=>{\n        return {\n            imageSrc: item.imageSrc,\n            title: locale === \"zh\" ? item.name : item.nameEn,\n            tip: locale === \"zh\" ? item.description : item.descriptionEn,\n            link: item.id === \"c31\" ? \"/product/c31\" : \"\"\n        };\n    });\n    const productsInfo = {\n        infos,\n        imageSize: {\n            width: 200,\n            height: 200\n        },\n        imageBox: {\n            width: 300,\n            height: 300\n        },\n        mode: _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__.itemsMode.product\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__items\"]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            ...productsInfo\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 220,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, this);\n}\n_s3(List, \"0bgUYh39Ymd94mXW1RH411vtZ4A=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale\n    ];\n});\n_c3 = List;\nfunction ListPagination(param) {\n    let { currentPage, count, onChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default().pagination),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pagination__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentPage: currentPage,\n                count: count,\n                onChange: (page)=>{\n                    onChange(page);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_c4 = ListPagination;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ProductListLayout\");\n$RefreshReg$(_c1, \"ProductList\");\n$RefreshReg$(_c2, \"Tabs\");\n$RefreshReg$(_c3, \"List\");\n$RefreshReg$(_c4, \"ListPagination\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/product/product-list.tsx\n"));

/***/ })

});