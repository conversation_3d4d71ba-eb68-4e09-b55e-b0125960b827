(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[862],{6015:function(e,t,n){Promise.resolve().then(n.bind(n,477)),Promise.resolve().then(n.bind(n,4978)),Promise.resolve().then(n.t.bind(n,1374,23)),Promise.resolve().then(n.t.bind(n,1749,23)),Promise.resolve().then(n.t.bind(n,5250,23))},1426:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var a=n(7437),i=n(703),l=n(8792);function s(e){let{children:t,list:n,show:s=!1,onClick:r=()=>{},onClickMask:o=()=>{}}=e,c=e=>{r(e)};return(0,a.jsxs)("div",{style:{position:"relative"},children:[t,s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"dropdown-window",children:[(0,a.jsx)("div",{className:"dropdown-window__above",children:(0,a.jsx)(i.default,{src:"/dropdown-window-above.svg",width:15,height:8,alt:"drop"})}),(0,a.jsx)("div",{className:"dropdown-window__list",children:n.map((e,t)=>(0,a.jsx)("div",{children:e.link?(0,a.jsx)(l.default,{href:e.link,children:(0,a.jsx)("div",{className:"dropdown-window__list__item",onClick:()=>{c(e.link)},children:e.text})}):(0,a.jsx)("div",{className:"dropdown-window__list__item",onClick:()=>{c(null==e?void 0:e.id)},children:e.text})},t))}),(0,a.jsx)("div",{className:"dropdown-window__placeholder"})]}),(0,a.jsx)("div",{className:"dropdown-window__mask hide-on-medium hide-on-large",onClick:()=>{o()}})]})]})}},477:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return o}});var a=n(7437);n(2135);var i=n(703);function l(e){let{src:t="/search-banner.webp",mobileSrc:n="/search-banner.webp"}=e;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"banner hide-on-small",children:(0,a.jsx)(i.default,{src:t,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})}),(0,a.jsx)("div",{className:"banner hide-on-medium hide-on-large",children:(0,a.jsx)(i.default,{src:n,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})})]})}var s=n(2265),r=n(8792);function o(e){let{title:t="",iconSrc:n="",tabs:o=[],currentTab:_,bannerSrc:u="/pic_shipinbg@2x (1).webp",bannerMobileSrc:d="/pic_shipinbg@2x (1).webp",background:p="rgb(179, 220, 252)",onTabChange:m=()=>{},showBanner:h=!0,isSearch:f=!1,isLink:g=!1}=e,v=e=>{m(e)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"page-tabs",style:p?{background:p}:{},children:[h&&(0,a.jsx)(l,{src:u,mobileSrc:d}),(0,a.jsx)(()=>(0,a.jsx)("div",{className:"page-tabs__content",children:(0,a.jsxs)("div",{className:"page-tabs__content__title",children:[(0,a.jsx)(i.default,{src:n,width:34,height:34,alt:""}),(0,a.jsx)("h1",{children:t})]})}),{}),!f&&(0,a.jsx)(e=>{let{isLink:t=!1}=e;return(0,a.jsx)("div",{className:"page-tabs__content__items hide-on-small",children:o.map((e,n)=>(0,a.jsx)(s.Fragment,{children:t?(0,a.jsx)(r.default,{href:"".concat(e.id),children:(0,a.jsx)("button",{className:"page-tabs__content__items__item ".concat(_===e.id?"page-tabs__content__items__item--active":""),onClick:()=>v(e.id),children:e.text})}):(0,a.jsx)("button",{className:"page-tabs__content__items__item ".concat(_===e.id?"page-tabs__content__items__item--active":""),onClick:()=>v(e.id),children:e.text})},n))})},{isLink:g})]}),(0,a.jsx)(c,{onTabChange:m,tabs:o,currentTab:_,isLink:g})]})}function c(e){let{tabs:t,currentTab:n,onTabChange:i,isLink:l}=e,o=e=>{let{id:t,text:l}=e;return(0,a.jsx)("div",{className:"".concat(n===t?"page-tabs__tabs-small__tab--active":""," page-tabs__tabs-small__tab"),onClick:()=>{i(t)},children:l})};return(0,a.jsx)("div",{className:"page-tabs__tabs-small hide-on-medium hide-on-large",children:t.map((e,t)=>(0,a.jsx)(s.Fragment,{children:l?(0,a.jsx)(r.default,{href:e.id,children:(0,a.jsx)(o,{...e})}):(0,a.jsx)(o,{...e})},t))})}},8398:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return i}});var a=n(7437);function i(e){var t;let n,{currentPage:i=1,pageSize:l=8,count:s=0,onChange:r=()=>{}}=e;(t=n||(n={}))[t.first=0]="first",t[t.last=1]="last";let o=Math.ceil(s/l),c=e=>{let{type:t=0}=e;return(0,a.jsx)("button",{onClick:()=>{1===i&&0===t||i===o&&1===t||r(0===t?1:o)},className:"pagination__pagejumper",disabled:0===t&&1===i||1===t&&i===o,children:0===t?"首页":"尾页"})};return(0,a.jsx)(a.Fragment,{children:s>8&&(0,a.jsxs)("div",{className:"pagination hide-on-small",children:[(0,a.jsx)(c,{type:0}),Array(o).fill(0).map((e,t)=>(0,a.jsx)("button",{onClick:()=>{i!==t+1&&r(t+1)},className:"pagination__page ".concat(i===t+1?"pagination__page--active":""),children:t+1},t)),o>5&&(0,a.jsx)("button",{className:"pagination__page",children:"..."}),(0,a.jsx)(c,{type:1})]})})}},4978:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return p}});var a=n(7437),i=n(1374),l=n.n(i),s=n(703),r=n(2265);async function o(e){let{infos:t,isProductDetail:n=!1}=e,i=e=>{let{imageSrc:t,title:n,description:i,time:l,link:r="/"}=e;return(0,a.jsxs)("a",{href:r,className:"flex-box-with-2items__item",children:[(0,a.jsx)("div",{children:(0,a.jsx)(s.default,{src:t,width:240,height:160,style:{width:"100%",height:"100%"},alt:""})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h6",{children:n}),(0,a.jsx)("p",{className:"hide-on-small",children:i}),(0,a.jsx)("div",{style:{flex:1}}),(0,a.jsx)("span",{children:l})]})]})};return(0,a.jsx)("div",{className:"flex-box-with-2items ".concat(n?"flex-box-with-2items--product-detail":""),children:t.map((e,t)=>(0,a.jsx)(r.Fragment,{children:(0,a.jsx)(i,{...e})},"".concat(t,"-").concat(e.title)))})}var c=n(8398),_=n(1426),u=n(7907),d=n(5421);function p(e){let{datas:t,groups:n}=e,[i,s]=(0,r.useState)(1);return(0,a.jsx)(d.e4,{locale:(0,d.eV)(),children:(0,a.jsxs)("div",{className:l().help,children:[(0,a.jsx)(m,{groups:n}),(0,a.jsx)(h,{datas:t}),(0,a.jsx)(f,{count:t.length,currentPage:i})]})})}function m(e){var t;let{groups:n}=e,i=(0,d.eV)(),o=(0,u.usePathname)(),c=(0,u.useRouter)(),p=(0,d.QT)(),m=(0,u.useSearchParams)().get("nav"),h=n.map(e=>({id:e.id,text:"zh"===i?e.name:e.nameEn}));h.unshift({id:"0",text:p("all")});let[f,g]=(0,r.useState)(m||h[0].id),[v,x]=(0,r.useState)(!1),b=e=>{e&&(g(e),x(!1))},j=e=>{e&&(g(e),c.replace("".concat(o,"?nav=").concat(e)))},w=e=>{let{text:t,id:n}=e;return(0,a.jsx)("button",{className:"".concat(l().help__nav__item," ").concat(f===n?l()["help__nav__item--active"]:""),onClick:()=>{j(n)},children:t})};return(0,a.jsxs)("div",{style:{display:"flex"},children:[(0,a.jsx)("div",{className:"".concat(l().help__nav," hide-on-small"),children:h.map((e,t)=>(0,a.jsx)(w,{...e},t))}),(0,a.jsx)("div",{className:"".concat(l().manuals__captain__menu," hide-on-medium hide-on-large"),children:(0,a.jsx)(_.Z,{show:v,list:h,onClick:e=>{b(e)},onClickMask:()=>{x(!1)},children:(0,a.jsxs)("div",{onClick:()=>{x(!v)},className:l().manuals__captain__menu__item,children:[null===(t=h.find(e=>e.id===f))||void 0===t?void 0:t.text,(0,a.jsx)(s.default,{src:"/arrow-down.svg",width:16,height:10,alt:""})]})})})]})}function h(e){let{datas:t}=e,n=(0,d.eV)(),i=t.map(e=>{var t,a;let i=e.content;"en"===n&&e.contentEn&&(i=e.contentEn);let l="".concat(null===(t=i[0])||void 0===t?void 0:t.text).concat(null===(a=i[1])||void 0===a?void 0:a.text);return{imageSrc:e.coverSrc,title:"zh"===n?e.title:e.titleEn?e.titleEn:e.title,description:l,time:function(e,t){let n=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0"),l=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),r=String(e.getSeconds()).padStart(2,"0");return t.replace(/YYYY/g,String(n)).replace(/MM/g,a).replace(/DD/g,i).replace(/HH/g,l).replace(/mm/g,s).replace(/ss/g,r)}(e.time,"YYYY-MM-DD"),link:"/article/".concat(e.id)}});return(0,a.jsx)("div",{className:l().help__list,children:(0,a.jsx)(o,{infos:i})})}function f(e){let{count:t,currentPage:n}=e;return(0,a.jsx)("div",{className:l().help__pagination,children:(0,a.jsx)(c.default,{count:t,currentPage:n})})}},5421:function(e,t,n){"use strict";n.d(t,{QT:function(){return a},Zt:function(){return r},e4:function(){return l},eV:function(){return s}});let{useI18n:a,useScopedI18n:i,I18nProviderClient:l,useCurrentLocale:s,useChangeLocale:r}=(0,n(8333).createI18nClient)({en:()=>n.e(673).then(n.bind(n,6673)),zh:()=>n.e(105).then(n.bind(n,6105))})},8333:function(e,t,n){"use strict";var a,i,l=Object.create,s=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,_=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,p=(e,t,n)=>t in e?s(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&p(e,n,t[n]);if(c)for(var n of c(t))d.call(t,n)&&p(e,n,t[n]);return e},h=(e,t,n,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of o(t))u.call(e,i)||i===n||s(e,i,{get:()=>t[i],enumerable:!(a=r(t,i))||a.enumerable});return e},f={};((e,t)=>{for(var n in t)s(e,n,{get:t[n],enumerable:!0})})(f,{createI18nClient:()=>E}),e.exports=h(s({},"__esModule",{value:!0}),f),n(2219);var g=n(7907),v=(i=null!=(a=n(2265))?l(_(a)):{},h(a&&a.__esModule?i:s(i,"default",{value:a,enumerable:!0}),a)),x=(e,t="")=>Object.entries(e).reduce((e,[n,a])=>m(m({},e),"string"==typeof a?{[t+n]:a}:x(a,`${t}${n}.`)),{}),b=e=>null,j=e=>null,w=new Map,N=n(2265),P=n(2265),k=n(2265);function y(e,t){let{localeContent:n,fallbackLocale:a}=e,i=a&&"string"==typeof n?a:Object.assign(null!=a?a:{},n),l=new Set(Object.keys(i).filter(e=>e.includes("#")).map(e=>e.split("#",1)[0])),s=new Intl.PluralRules(e.locale);return function(e,...n){var a,r,o;let c=n[0],_=!1;c&&"count"in c&&(t?l.has(`${t}.${e}`):l.has(e))&&(e=`${e}#${0===(o=c.count)?"zero":s.select(o)}`,_=!0);let u=t?i[`${t}.${e}`]:i[e];if(!u&&_){let t=e.split("#",1)[0];u=null==(a=i[`${t}#other`]||e)?void 0:a.toString()}else u=null==(r=u||e)?void 0:r.toString();if(!c)return u;let d=!0,p=null==u?void 0:u.split(/({[^}]*})/).map((e,t)=>{let n=e.match(/{(.*)}/);if(n){let e=n[1],a=c[e];return(0,k.isValidElement)(a)?(d=!1,(0,k.cloneElement)(a,{key:`${String(e)}-${t}`})):a}return e});return d?null==p?void 0:p.join(""):p}}var S=n(2265),O=n(7907),C=n(7907),$=n(2265);function E(e,t={}){let n=Object.keys(e),a=(0,N.createContext)(null),i=function(){var e;let a=(0,C.useParams)()[null!=(e=t.segmentName)?e:"locale"];return(0,$.useMemo)(()=>{for(let e of n)if(a===e)return e;j(`Locale "${a}" not found in locales (${n.join(", ")}), returning "notFound()"`),(0,C.notFound)()},[a])};return{useI18n:function(){let e=(0,P.useContext)(a);if(!e)throw Error("`useI18n` must be used inside `I18nProvider`");return(0,P.useMemo)(()=>y(e,void 0),[e])},useScopedI18n:function(e){let t=(0,S.useContext)(a);if(!t)throw Error("`useI18n` must be used inside `I18nProvider`");return(0,S.useMemo)(()=>y(t,e),[t,e])},I18nProviderClient:function(e,t,n){function a({locale:t,importLocale:a,children:i}){var l;let s=null!=(l=w.get(t))?l:(0,v.use)(a).default;w.has(t)||w.set(t,s);let r=(0,v.useMemo)(()=>({localeContent:x(s),fallbackLocale:n?x(n):void 0,locale:t}),[s,t]);return v.default.createElement(e.Provider,{value:r},i)}return function({locale:e,fallback:n,children:i}){let l=t[e];return l||(j(`The locale '${e}' is not supported. Defined locales are: [${Object.keys(t).join(", ")}].`),(0,g.notFound)()),v.default.createElement(v.Suspense,{fallback:n},v.default.createElement(a,{locale:e,importLocale:l()},i))}}(a,e,t.fallbackLocale),I18nClientContext:a,useChangeLocale:function(n){let{push:a,refresh:l}=(0,O.useRouter)(),s=i(),r=(0,O.usePathname)(),o=(null==n?void 0:n.preserveSearchParams)?(0,O.useSearchParams)().toString():void 0,c=o?`?${o}`:"",_=r;return t.basePath&&(_=_.replace(t.basePath,"")),_.startsWith(`/${s}/`)?_=_.replace(`/${s}/`,"/"):_===`/${s}`&&(_="/"),function(t){if(t===s)return;let n=e[t];if(!n){b(`The locale '${t}' is not supported. Defined locales are: [${Object.keys(e).join(", ")}].`);return}n().then(e=>{w.set(t,e.default),a(`/${t}${_}${c}`),l()})}},defineLocale:function(e){return e},useCurrentLocale:i}}},703:function(e,t,n){"use strict";n.d(t,{default:function(){return i.a}});var a=n(7447),i=n.n(a)},8792:function(e,t,n){"use strict";n.d(t,{default:function(){return i.a}});var a=n(5250),i=n.n(a)},7907:function(e,t,n){"use strict";n.r(t);var a=n(5313),i={};for(var l in a)"default"!==l&&(i[l]=(function(e){return a[e]}).bind(0,l));n.d(t,i)},2219:function(){},7447:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getImageProps:function(){return r},default:function(){return o}});let a=n(6921),i=n(8630),l=n(1749),s=a._(n(536)),r=e=>{let{props:t}=(0,i.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}},o=l.Image},2135:function(){},1374:function(e){e.exports={downloads:"support_downloads___WU_3",downloads__content:"support_downloads__content__adctV",downloads__content__card:"support_downloads__content__card__8k3mb",manuals:"support_manuals__NEn7J",manuals__captain:"support_manuals__captain__cEP0z",manuals__captain__navs__button:"support_manuals__captain__navs__button__g3bbO","manuals__captain__navs__button--active":"support_manuals__captain__navs__button--active__vPDNu",manuals__captain__languages:"support_manuals__captain__languages__VlDtT",manuals__captain__languages__item:"support_manuals__captain__languages__item__YhoHX","manuals__captain__languages__item--active":"support_manuals__captain__languages__item--active__L9vmB",manuals__list:"support_manuals__list__UUsyR",manuals__list__item:"support_manuals__list__item__SGCXz",manuals__list__item__image:"support_manuals__list__item__image__w84lm",manuals__pagination:"support_manuals__pagination__mn4kp",help:"support_help__JADd0",help__nav:"support_help__nav__oL9P3",help__nav__item:"support_help__nav__item__RRqPO","help__nav__item--active":"support_help__nav__item--active__TqKwA",help__list:"support_help__list__7uY42",help__pagination:"support_help__pagination__50GG4",manuals__captain__menu__item:"support_manuals__captain__menu__item___M8XD"}}},function(e){e.O(0,[647,971,69,744],function(){return e(e.s=6015)}),_N_E=e.O()}]);