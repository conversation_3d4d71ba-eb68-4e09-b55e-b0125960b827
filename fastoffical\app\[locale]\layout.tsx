import type { Metadata, ResolvingMetadata } from 'next'
import { Inter } from 'next/font/google'
import '@/app/globals.scss'
import FooterLayout from '@/app/[locale]/ui/home/<USER>'
import NavLayout from '@/app/[locale]/ui/home/<USER>'
import BackToTop from '@/app/[locale]/ui/components/back-to-top'
import { PageProps } from '@/data/type'

const inter = Inter({ subsets: ['latin'] })
export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    description: '赛蓝科技 引领生活',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode
  params: { locale: string }
}>) {
  return (
    <html lang={params.locale}>
      <body className={inter.className}>
        <header>
          <NavLayout />
        </header>
        <main>{children}</main>
        <footer>
          <FooterLayout />
        </footer>
        <BackToTop />
      </body>
    </html>
  )
}
