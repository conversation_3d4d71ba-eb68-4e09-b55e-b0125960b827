(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[725],{2608:function(n,u,t){Promise.resolve().then(t.bind(t,2893))},2893:function(n,u,t){"use strict";t.r(u),t.d(u,{default:function(){return r}});var e=t(7907);function r(){return(0,e.useRouter)().replace("/support/download_client"),null}},7907:function(n,u,t){"use strict";t.r(u);var e=t(5313),r={};for(var i in e)"default"!==i&&(r[i]=(function(n){return e[n]}).bind(0,i));t.d(u,r)}},function(n){n.O(0,[971,69,744],function(){return n(n.s=2608)}),_N_E=n.O()}]);