import styles from './support.module.scss'
import Image from 'next/image'
import Link from 'next/link'
import { getI18n, getCurrentLocale } from '@/locales/server'

export enum DownloadType {
  android = 'Android',
  ios = 'iOS',
  miniprogram = '小程序',
  yuebanAndroid = '悦办Android',
  yuebaniOS = '悦办iOS'
}

export default async function Downloads() {
  const t = await getI18n()
  const locale = await getCurrentLocale()

  return (
    <div className={styles.downloads}>
      <h2>{t('imcamApp')}</h2>
      <p>{t('imcamAppTip')}</p>
      <div className={styles.downloads__content}>
        <Card type={DownloadType.android} version="1.2.333" />
        <Card type={DownloadType.ios} version="1.2.333" />
        {locale === 'zh' && (
          <Card type={DownloadType.miniprogram} version="1.1.0" />
        )}
        <Card type={DownloadType.yuebanAndroid} version="1.2.333" />
        <Card type={DownloadType.yuebaniOS} version="1.2.333" />

      </div>
    </div>
  )
}

async function Card({
  type = DownloadType.android,
  version = '',
}: {
  type: DownloadType
  version?: string
}) {
  const t = await getI18n()
  return (
    <div className={styles.downloads__content__card}>
      {/* 标题 */}
      <h5>{type === DownloadType.miniprogram ? t('dangdang') : type === DownloadType.yuebanAndroid || type === DownloadType.yuebaniOS ? t('imMate') : t('imcam')}</h5>
      {/* 副标题 */}
      <h5>
        {type === DownloadType.miniprogram
          ? t('miniProgram')
          : type === DownloadType.android || type === DownloadType.yuebanAndroid
            ? t('androidApp')
            : t('iosApp')}
      </h5>
      {/* 二维码图片 */}
      <Image
        src={
          type === DownloadType.miniprogram
            ? '/miniprogram-qrcode.jpg'
            : '/support/download-code.webp'
        }
        width={160}
        height={160}
        alt="qrcode"
      ></Image>
      {/* <p>版本：{version}</p> */}
      {(type === DownloadType.android || type === DownloadType.yuebanAndroid) && (
        <Link
          href={
            type === DownloadType.android ? 'https://cn-pub.oss-cn-hangzhou.aliyuncs.com/forever/app/android/IM_Cam.apk' :
              'https://cn-pub.oss-cn-hangzhou.aliyuncs.com/forever/app/android/ImMate.apk'
          }
        >
          <button>
            <Image
              src={'/download-white.svg'}
              width={20}
              height={20}
              alt=""
            ></Image>
            {t('download')}
          </button>
        </Link>
      )}
      {(type === DownloadType.ios || type === DownloadType.yuebaniOS) && (
        <>
          <Link href={type === DownloadType.ios ? 'https://apps.apple.com/app/im-cam/id6447252954'
            : 'https://apps.apple.com/cn/app/%E6%82%A6%E5%8A%9E/id6743839622'
          }>
            <button>{t('goToAppstore')}</button>
          </Link>
        </>
      )}
    </div>
  )
}
