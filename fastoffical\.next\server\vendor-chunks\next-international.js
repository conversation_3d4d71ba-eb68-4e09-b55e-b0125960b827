"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-international";
exports.ids = ["vendor-chunks/next-international"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-international/dist/app/client/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/next-international/dist/app/client/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/app/client/index.ts\nvar client_exports = {};\n__export(client_exports, {\n  createI18nClient: () => createI18nClient\n});\nmodule.exports = __toCommonJS(client_exports);\nvar import_client_only = __webpack_require__(/*! client-only */ \"(ssr)/./node_modules/next/dist/compiled/client-only/index.js\");\n\n// src/app/client/create-i18n-provider-client.tsx\nvar import_navigation = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar import_react = __toESM(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\n\n// src/common/flatten-locale.ts\nvar flattenLocale = (locale, prefix = \"\") => Object.entries(locale).reduce(\n  (prev, [name, value]) => __spreadValues(__spreadValues({}, prev), typeof value === \"string\" ? { [prefix + name]: value } : flattenLocale(value, `${prefix}${name}.`)),\n  {}\n);\n\n// src/helpers/log.ts\nfunction log(type, message) {\n  if (true) {\n    console[type](`[next-international] ${message}`);\n  }\n  return null;\n}\nvar warn = (message) => log(\"warn\", message);\nvar error = (message) => log(\"error\", message);\n\n// src/app/client/create-i18n-provider-client.tsx\nvar localesCache = /* @__PURE__ */ new Map();\nfunction createI18nProviderClient(I18nClientContext, locales, fallbackLocale) {\n  function I18nProvider({ locale, importLocale, children }) {\n    var _a;\n    const clientLocale = (_a = localesCache.get(locale)) != null ? _a : (0, import_react.use)(importLocale).default;\n    if (!localesCache.has(locale)) {\n      localesCache.set(locale, clientLocale);\n    }\n    const value = (0, import_react.useMemo)(\n      () => ({\n        localeContent: flattenLocale(clientLocale),\n        fallbackLocale: fallbackLocale ? flattenLocale(fallbackLocale) : void 0,\n        locale\n      }),\n      [clientLocale, locale]\n    );\n    return /* @__PURE__ */ import_react.default.createElement(I18nClientContext.Provider, { value }, children);\n  }\n  return function I18nProviderWrapper({ locale, fallback, children }) {\n    const importFnLocale = locales[locale];\n    if (!importFnLocale) {\n      error(`The locale '${locale}' is not supported. Defined locales are: [${Object.keys(locales).join(\", \")}].`);\n      (0, import_navigation.notFound)();\n    }\n    return /* @__PURE__ */ import_react.default.createElement(import_react.Suspense, { fallback }, /* @__PURE__ */ import_react.default.createElement(I18nProvider, { locale, importLocale: importFnLocale() }, children));\n  };\n}\n\n// src/app/client/index.ts\nvar import_react6 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n// src/common/create-use-i18n.ts\nvar import_react3 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n// src/common/create-t.ts\nvar import_react2 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction createT(context, scope) {\n  const { localeContent, fallbackLocale } = context;\n  const content = fallbackLocale && typeof localeContent === \"string\" ? fallbackLocale : Object.assign(fallbackLocale != null ? fallbackLocale : {}, localeContent);\n  const pluralKeys = new Set(\n    Object.keys(content).filter((key) => key.includes(\"#\")).map((key) => key.split(\"#\", 1)[0])\n  );\n  const pluralRules = new Intl.PluralRules(context.locale);\n  function getPluralKey(count) {\n    if (count === 0)\n      return \"zero\";\n    return pluralRules.select(count);\n  }\n  function t(key, ...params) {\n    var _a, _b;\n    const paramObject = params[0];\n    let isPlural = false;\n    if (paramObject && \"count\" in paramObject) {\n      const isPluralKey = scope ? pluralKeys.has(`${scope}.${key}`) : pluralKeys.has(key);\n      if (isPluralKey) {\n        key = `${key}#${getPluralKey(paramObject.count)}`;\n        isPlural = true;\n      }\n    }\n    let value = scope ? content[`${scope}.${key}`] : content[key];\n    if (!value && isPlural) {\n      const baseKey = key.split(\"#\", 1)[0];\n      value = (_a = content[`${baseKey}#other`] || key) == null ? void 0 : _a.toString();\n    } else {\n      value = (_b = value || key) == null ? void 0 : _b.toString();\n    }\n    if (!paramObject) {\n      return value;\n    }\n    let isString = true;\n    const result = value == null ? void 0 : value.split(/({[^}]*})/).map((part, index) => {\n      const match = part.match(/{(.*)}/);\n      if (match) {\n        const param = match[1];\n        const paramValue = paramObject[param];\n        if ((0, import_react2.isValidElement)(paramValue)) {\n          isString = false;\n          return (0, import_react2.cloneElement)(paramValue, { key: `${String(param)}-${index}` });\n        }\n        return paramValue;\n      }\n      return part;\n    });\n    return isString ? result == null ? void 0 : result.join(\"\") : result;\n  }\n  return t;\n}\n\n// src/common/create-use-i18n.ts\nfunction createUsei18n(I18nClientContext) {\n  return function useI18n() {\n    const context = (0, import_react3.useContext)(I18nClientContext);\n    if (!context) {\n      throw new Error(\"`useI18n` must be used inside `I18nProvider`\");\n    }\n    return (0, import_react3.useMemo)(() => createT(context, void 0), [context]);\n  };\n}\n\n// src/common/create-use-scoped-i18n.ts\nvar import_react4 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nfunction createScopedUsei18n(I18nClientContext) {\n  return function useScopedI18n(scope) {\n    const context = (0, import_react4.useContext)(I18nClientContext);\n    if (!context) {\n      throw new Error(\"`useI18n` must be used inside `I18nProvider`\");\n    }\n    return (0, import_react4.useMemo)(() => createT(context, scope), [context, scope]);\n  };\n}\n\n// src/app/client/create-use-change-locale.ts\nvar import_navigation2 = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nfunction createUseChangeLocale(useCurrentLocale, locales, config) {\n  return function useChangeLocale(changeLocaleConfig) {\n    const { push, refresh } = (0, import_navigation2.useRouter)();\n    const currentLocale = useCurrentLocale();\n    const path = (0, import_navigation2.usePathname)();\n    const searchParams = (changeLocaleConfig == null ? void 0 : changeLocaleConfig.preserveSearchParams) ? (0, import_navigation2.useSearchParams)().toString() : void 0;\n    const finalSearchParams = searchParams ? `?${searchParams}` : \"\";\n    let pathWithoutLocale = path;\n    if (config.basePath) {\n      pathWithoutLocale = pathWithoutLocale.replace(config.basePath, \"\");\n    }\n    if (pathWithoutLocale.startsWith(`/${currentLocale}/`)) {\n      pathWithoutLocale = pathWithoutLocale.replace(`/${currentLocale}/`, \"/\");\n    } else if (pathWithoutLocale === `/${currentLocale}`) {\n      pathWithoutLocale = \"/\";\n    }\n    return function changeLocale(newLocale) {\n      if (newLocale === currentLocale)\n        return;\n      const importFnLocale = locales[newLocale];\n      if (!importFnLocale) {\n        warn(`The locale '${newLocale}' is not supported. Defined locales are: [${Object.keys(locales).join(\", \")}].`);\n        return;\n      }\n      importFnLocale().then((module2) => {\n        localesCache.set(newLocale, module2.default);\n        push(`/${newLocale}${pathWithoutLocale}${finalSearchParams}`);\n        refresh();\n      });\n    };\n  };\n}\n\n// src/common/create-define-locale.ts\nfunction createDefineLocale() {\n  return function defineLocale(locale) {\n    return locale;\n  };\n}\n\n// src/app/client/create-use-current-locale.ts\nvar import_navigation3 = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\nvar import_react5 = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n// src/common/constants.ts\nvar DEFAULT_SEGMENT_NAME = \"locale\";\n\n// src/app/client/create-use-current-locale.ts\nfunction createUseCurrentLocale(locales, config) {\n  return function useCurrentLocale() {\n    var _a;\n    const params = (0, import_navigation3.useParams)();\n    const segment = params[(_a = config.segmentName) != null ? _a : DEFAULT_SEGMENT_NAME];\n    return (0, import_react5.useMemo)(() => {\n      for (const locale of locales) {\n        if (segment === locale) {\n          return locale;\n        }\n      }\n      error(`Locale \"${segment}\" not found in locales (${locales.join(\", \")}), returning \"notFound()\"`);\n      (0, import_navigation3.notFound)();\n    }, [segment]);\n  };\n}\n\n// src/app/client/index.ts\nfunction createI18nClient(locales, config = {}) {\n  const localesKeys = Object.keys(locales);\n  const I18nClientContext = (0, import_react6.createContext)(null);\n  const useCurrentLocale = createUseCurrentLocale(localesKeys, config);\n  const I18nProviderClient = createI18nProviderClient(I18nClientContext, locales, config.fallbackLocale);\n  const useI18n = createUsei18n(I18nClientContext);\n  const useScopedI18n = createScopedUsei18n(I18nClientContext);\n  const useChangeLocale = createUseChangeLocale(useCurrentLocale, locales, config);\n  const defineLocale = createDefineLocale();\n  return {\n    useI18n,\n    useScopedI18n,\n    I18nProviderClient,\n    I18nClientContext,\n    useChangeLocale,\n    defineLocale,\n    useCurrentLocale\n  };\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-international/dist/app/client/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-international/dist/app/server/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/next-international/dist/app/server/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/app/server/index.ts\nvar server_exports = {};\n__export(server_exports, {\n  createI18nServer: () => createI18nServer,\n  setStaticParamsLocale: () => setStaticParamsLocale\n});\nmodule.exports = __toCommonJS(server_exports);\nvar import_server_only = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n\n// src/app/server/get-locale-cache.tsx\nvar import_headers = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\nvar import_react = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n\n// src/common/constants.ts\nvar LOCALE_HEADER = \"X-Next-Locale\";\nvar LOCALE_COOKIE = \"Next-Locale\";\nvar DEFAULT_SEGMENT_NAME = \"locale\";\n\n// src/app/server/get-locale-cache.tsx\nvar import_navigation = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.js\");\n\n// src/helpers/log.ts\nfunction log(type, message) {\n  if (true) {\n    console[type](`[next-international] ${message}`);\n  }\n  return null;\n}\nvar error = (message) => log(\"error\", message);\n\n// src/app/server/get-locale-cache.tsx\nvar getLocale = (0, import_react.cache)(() => ({ current: void 0 }));\nvar getStaticParamsLocale = () => getLocale().current;\nvar setStaticParamsLocale = (value) => {\n  getLocale().current = value;\n};\nvar getLocaleCache = (0, import_react.cache)(() => __async(void 0, null, function* () {\n  var _a;\n  let locale;\n  locale = getStaticParamsLocale();\n  if (!locale) {\n    try {\n      locale = (yield (0, import_headers.headers)()).get(LOCALE_HEADER);\n      if (!locale) {\n        locale = (_a = (yield (0, import_headers.cookies)()).get(LOCALE_COOKIE)) == null ? void 0 : _a.value;\n      }\n    } catch (e) {\n      throw new Error(\n        \"Could not find locale while pre-rendering page, make sure you called `setStaticParamsLocale` at the top of your pages\"\n      );\n    }\n  }\n  if (!locale) {\n    error(`Locale not found in headers or cookies, returning \"notFound()\"`);\n    (0, import_navigation.notFound)();\n  }\n  return locale;\n}));\n\n// src/app/server/create-get-current-locale.ts\nfunction createGetCurrentLocale() {\n  return function getCurrentLocale() {\n    return getLocaleCache();\n  };\n}\n\n// src/common/create-t.ts\nvar import_react2 = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\nfunction createT(context, scope) {\n  const { localeContent, fallbackLocale } = context;\n  const content = fallbackLocale && typeof localeContent === \"string\" ? fallbackLocale : Object.assign(fallbackLocale != null ? fallbackLocale : {}, localeContent);\n  const pluralKeys = new Set(\n    Object.keys(content).filter((key) => key.includes(\"#\")).map((key) => key.split(\"#\", 1)[0])\n  );\n  const pluralRules = new Intl.PluralRules(context.locale);\n  function getPluralKey(count) {\n    if (count === 0)\n      return \"zero\";\n    return pluralRules.select(count);\n  }\n  function t(key, ...params) {\n    var _a, _b;\n    const paramObject = params[0];\n    let isPlural = false;\n    if (paramObject && \"count\" in paramObject) {\n      const isPluralKey = scope ? pluralKeys.has(`${scope}.${key}`) : pluralKeys.has(key);\n      if (isPluralKey) {\n        key = `${key}#${getPluralKey(paramObject.count)}`;\n        isPlural = true;\n      }\n    }\n    let value = scope ? content[`${scope}.${key}`] : content[key];\n    if (!value && isPlural) {\n      const baseKey = key.split(\"#\", 1)[0];\n      value = (_a = content[`${baseKey}#other`] || key) == null ? void 0 : _a.toString();\n    } else {\n      value = (_b = value || key) == null ? void 0 : _b.toString();\n    }\n    if (!paramObject) {\n      return value;\n    }\n    let isString = true;\n    const result = value == null ? void 0 : value.split(/({[^}]*})/).map((part, index) => {\n      const match = part.match(/{(.*)}/);\n      if (match) {\n        const param = match[1];\n        const paramValue = paramObject[param];\n        if ((0, import_react2.isValidElement)(paramValue)) {\n          isString = false;\n          return (0, import_react2.cloneElement)(paramValue, { key: `${String(param)}-${index}` });\n        }\n        return paramValue;\n      }\n      return part;\n    });\n    return isString ? result == null ? void 0 : result.join(\"\") : result;\n  }\n  return t;\n}\n\n// src/common/flatten-locale.ts\nvar flattenLocale = (locale, prefix = \"\") => Object.entries(locale).reduce(\n  (prev, [name, value]) => __spreadValues(__spreadValues({}, prev), typeof value === \"string\" ? { [prefix + name]: value } : flattenLocale(value, `${prefix}${name}.`)),\n  {}\n);\n\n// src/app/server/create-get-i18n.ts\nfunction createGetI18n(locales, config) {\n  const localeCache = /* @__PURE__ */ new Map();\n  return function getI18n() {\n    return __async(this, null, function* () {\n      const locale = yield getLocaleCache();\n      const cached = localeCache.get(locale);\n      if (cached) {\n        return yield cached;\n      }\n      const localeFnPromise = (() => __async(this, null, function* () {\n        const localeModule = yield locales[locale]();\n        return createT(\n          {\n            localeContent: flattenLocale(localeModule.default),\n            fallbackLocale: config.fallbackLocale ? flattenLocale(config.fallbackLocale) : void 0,\n            locale\n          },\n          void 0\n        );\n      }))();\n      localeCache.set(locale, localeFnPromise);\n      return yield localeFnPromise;\n    });\n  };\n}\n\n// src/app/server/create-get-scoped-i18n.ts\nfunction createGetScopedI18n(locales, config) {\n  const localeCache = /* @__PURE__ */ new Map();\n  return function getScopedI18n(scope) {\n    return __async(this, null, function* () {\n      const locale = yield getLocaleCache();\n      const cacheKey = `${locale}-${scope}`;\n      const cached = localeCache.get(cacheKey);\n      if (cached) {\n        return yield cached;\n      }\n      const localeFnPromise = (() => __async(this, null, function* () {\n        const localeModule = yield locales[locale]();\n        return createT(\n          {\n            localeContent: flattenLocale(localeModule.default),\n            fallbackLocale: config.fallbackLocale ? flattenLocale(config.fallbackLocale) : void 0,\n            locale\n          },\n          scope\n        );\n      }))();\n      localeCache.set(cacheKey, localeFnPromise);\n      return yield localeFnPromise;\n    });\n  };\n}\n\n// src/app/server/create-get-static-params.ts\nfunction createGetStaticParams(locales, config) {\n  return function getStaticParams() {\n    return Object.keys(locales).map((locale) => {\n      var _a;\n      return {\n        [(_a = config.segmentName) != null ? _a : DEFAULT_SEGMENT_NAME]: locale\n      };\n    });\n  };\n}\n\n// src/app/server/index.ts\nfunction createI18nServer(locales, config = {}) {\n  const getI18n = createGetI18n(locales, config);\n  const getScopedI18n = createGetScopedI18n(locales, config);\n  const getCurrentLocale = createGetCurrentLocale();\n  const getStaticParams = createGetStaticParams(locales, config);\n  return {\n    getI18n,\n    getScopedI18n,\n    getCurrentLocale,\n    getStaticParams\n  };\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-international/dist/app/server/index.js\n");

/***/ })

};
;