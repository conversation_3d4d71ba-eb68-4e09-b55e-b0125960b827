'use client'
import styles from './support.module.scss'
import Flex2ItemsBox from '../components/flex-2items-box'
import Pagination from '../components/pagination'
import DropdownWindow from '../components/dropdown-window'
import { useState } from 'react'
import { useSearchParams, useRouter, usePathname } from 'next/navigation'
import Image from 'next/image'
import { HelpData, Group } from '@/data/type'
import { formatDate } from '@/utils/utils'
import { useI18n, useCurrentLocale, I18nProviderClient } from '@/locales/client'

export default function Help({
  datas,
  groups,
}: {
  datas: HelpData[]
  groups: Group[]
}) {
  const [currentPage, setCurrentPage] = useState(1)

  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <div className={styles.help}>
        <Nav groups={groups} />
        <List datas={datas} />
        <ListPagination count={datas.length} currentPage={currentPage} />
      </div>
    </I18nProviderClient>
  )
}

function Nav({ groups }: { groups: Group[] }) {
  const locale = useCurrentLocale()
  const pathname = usePathname()
  const router = useRouter()
  const t = useI18n()
  const searchParams = useSearchParams()
  const nav = searchParams.get('nav')

  const navs = groups.map((item) => {
    return {
      id: item.id,
      text: locale === 'zh' ? item.name : item.nameEn,
    }
  })
  navs.unshift({
    id: '0',
    text: t('all'),
  })

  const [currentNav, setCurrentNav] = useState(nav ? nav : navs[0].id)

  const [isShowMenu, setShowMenu] = useState(false)

  const handleCloseMenu = () => {
    setShowMenu(false)
  }

  const handleShowMenu = () => {
    setShowMenu(!isShowMenu)
  }

  const handleClickNav = (id?: string) => {
    if (id) {
      setCurrentNav(id)
      setShowMenu(false)
    }
  }

  const handleClickNavNormal = (id?: string) => {
    if (id) {
      setCurrentNav(id)
      router.replace(`${pathname}?nav=${id}`)
    }
  }

  const Item = ({ text, id }: { text: string; id: string }) => {
    return (
      <button
        className={`${styles.help__nav__item} ${
          currentNav === id ? styles['help__nav__item--active'] : ''
        }`}
        onClick={() => {
          handleClickNavNormal(id)
        }}
      >
        {text}
      </button>
    )
  }

  return (
    <div style={{ display: 'flex' }}>
      <div className={`${styles.help__nav} hide-on-small`}>
        {navs.map((item, index) => (
          <Item key={index} {...item}></Item>
        ))}
      </div>
      <div
        className={`${styles.manuals__captain__menu} hide-on-medium hide-on-large`}
      >
        <DropdownWindow
          show={isShowMenu}
          list={navs}
          onClick={(id?: string) => {
            handleClickNav(id)
          }}
          onClickMask={handleCloseMenu}
        >
          <div
            onClick={handleShowMenu}
            className={styles.manuals__captain__menu__item}
          >
            {navs.find((i) => i.id === currentNav)?.text}
            <Image
              src={'/arrow-down.svg'}
              width={16}
              height={10}
              alt=""
            ></Image>
          </div>
        </DropdownWindow>
      </div>
    </div>
  )
}

function List({ datas }: { datas: HelpData[] }) {
  const locale = useCurrentLocale()

  const infos = datas.map((item) => {
    let content = item.content
    if (locale === 'en' && item.contentEn) content = item.contentEn
    const description = `${content[0]?.text}${content[1]?.text}`

    return {
      imageSrc: item.coverSrc,
      title:
        locale === 'zh' ? item.title : item.titleEn ? item.titleEn : item.title,
      description,
      time: formatDate(item.time, 'YYYY-MM-DD'),
      link: `/article/${item.id}`,
    }
  })
  return (
    <div className={styles.help__list}>
      <Flex2ItemsBox infos={infos}></Flex2ItemsBox>
    </div>
  )
}

function ListPagination({
  count,
  currentPage,
}: {
  count: number
  currentPage: number
}) {
  return (
    <div className={styles.help__pagination}>
      <Pagination count={count} currentPage={currentPage}></Pagination>
    </div>
  )
}
