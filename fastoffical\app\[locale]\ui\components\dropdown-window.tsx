import Image from 'next/image'
import Link from 'next/link'

type ListLink = {
  link?: string
  text: string
  id?: string
}

export default function DropdownWindow({
  children,
  list,
  show = false,
  onClick = () => {},
  onClickMask = () => {},
}: Readonly<{
  children: React.ReactNode
  list: Array<ListLink>
  show: boolean
  onClick: Function
  onClickMask?: Function
}>) {
  const handleClick = (id?: string) => {
    onClick(id)
  }

  return (
    <div style={{ position: 'relative' }}>
      {children}
      {show && (
        <>
          <div className="dropdown-window">
            <div className="dropdown-window__above">
              <Image
                src={'/dropdown-window-above.svg'}
                width={15}
                height={8}
                alt="drop"
              ></Image>
            </div>
            <div className="dropdown-window__list">
              {list.map((item, index) => {
                return (
                  <div key={index}>
                    {item.link ? (
                      <Link href={item.link}>
                        <div
                          className="dropdown-window__list__item"
                          onClick={() => {
                            handleClick(item.link)
                          }}
                        >
                          {item.text}
                        </div>
                      </Link>
                    ) : (
                      <div
                        className="dropdown-window__list__item"
                        onClick={() => {
                          handleClick(item?.id)
                        }}
                      >
                        {item.text}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
            <div className="dropdown-window__placeholder"></div>
          </div>
          <div
            className="dropdown-window__mask hide-on-medium hide-on-large"
            onClick={() => {
              onClickMask()
            }}
          ></div>
        </>
      )}
    </div>
  )
}
