[{"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx": "1", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx": "2", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\(overview)\\page.tsx": "3", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\Layout.tsx": "4", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\page.tsx": "5", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\article\\Layout.tsx": "6", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\article\\[articleId]\\articleIds.tsx": "7", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\article\\[articleId]\\page.tsx": "8", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx": "9", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\news\\Layout.tsx": "10", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\news\\page.tsx": "11", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx": "12", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\(overview)\\page.tsx": "13", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\c31\\page.tsx": "14", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\layout.tsx": "15", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\product-detail\\[productId]\\page.tsx": "16", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\layout.tsx": "17", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\page.tsx": "18", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\[type]\\page.tsx": "19", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\about\\about-content.tsx": "20", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\article\\article-content.tsx": "21", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\article\\article-hot.tsx": "22", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\article\\article-title.tsx": "23", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\back-to-top.tsx": "24", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\banner.tsx": "25", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\bread-crumbs.tsx": "26", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\certificates.tsx": "27", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\dropdown-window.tsx": "28", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\flex-2items-box.tsx": "29", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\flex-4items-box.tsx": "30", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\nav-list.tsx": "31", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\page-tabs.tsx": "32", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\pagination.tsx": "33", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\show-more.tsx": "34", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\about.tsx": "35", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\banner-slider.tsx": "36", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\footer.tsx": "37", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\hot-spot.tsx": "38", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\nav.tsx": "39", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\product\\product-info.tsx": "40", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\product\\product-list.tsx": "41", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\product\\product-preview.tsx": "42", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\downloads.tsx": "43", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\help.tsx": "44", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\manuals.tsx": "45", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\videos\\layout.tsx": "46", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\videos\\page.tsx": "47", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\[...rest]\\page.tsx": "48", "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\t1pro\\page.tsx": "49"}, {"size": 171, "mtime": 1754537956397, "results": "50", "hashOfConfig": "51"}, {"size": 374, "mtime": 1754537956397, "results": "52", "hashOfConfig": "51"}, {"size": 979, "mtime": 1754537956387, "results": "53", "hashOfConfig": "51"}, {"size": 133, "mtime": 1754537956387, "results": "54", "hashOfConfig": "51"}, {"size": 563, "mtime": 1754537956387, "results": "55", "hashOfConfig": "51"}, {"size": 133, "mtime": 1754537956388, "results": "56", "hashOfConfig": "51"}, {"size": 186, "mtime": 1754537956388, "results": "57", "hashOfConfig": "51"}, {"size": 6213, "mtime": 1754555782708, "results": "58", "hashOfConfig": "51"}, {"size": 1133, "mtime": 1754537956388, "results": "59", "hashOfConfig": "51"}, {"size": 133, "mtime": 1754537956388, "results": "60", "hashOfConfig": "51"}, {"size": 4684, "mtime": 1754537956389, "results": "61", "hashOfConfig": "51"}, {"size": 943, "mtime": 1754537956389, "results": "62", "hashOfConfig": "51"}, {"size": 877, "mtime": 1754552560718, "results": "63", "hashOfConfig": "51"}, {"size": 9926, "mtime": 1754555828304, "results": "64", "hashOfConfig": "51"}, {"size": 133, "mtime": 1754537956390, "results": "65", "hashOfConfig": "51"}, {"size": 1292, "mtime": 1754537956390, "results": "66", "hashOfConfig": "51"}, {"size": 569, "mtime": 1754537956391, "results": "67", "hashOfConfig": "51"}, {"size": 191, "mtime": 1754537956391, "results": "68", "hashOfConfig": "51"}, {"size": 1877, "mtime": 1754537956391, "results": "69", "hashOfConfig": "51"}, {"size": 14858, "mtime": 1754558714183, "results": "70", "hashOfConfig": "51"}, {"size": 2975, "mtime": 1754555803978, "results": "71", "hashOfConfig": "51"}, {"size": 1613, "mtime": 1754555815757, "results": "72", "hashOfConfig": "51"}, {"size": 279, "mtime": 1754537956392, "results": "73", "hashOfConfig": "51"}, {"size": 1755, "mtime": 1754537956392, "results": "74", "hashOfConfig": "51"}, {"size": 722, "mtime": 1754537956392, "results": "75", "hashOfConfig": "51"}, {"size": 1104, "mtime": 1754537956392, "results": "76", "hashOfConfig": "51"}, {"size": 2033, "mtime": 1754537956392, "results": "77", "hashOfConfig": "51"}, {"size": 2226, "mtime": 1754537956393, "results": "78", "hashOfConfig": "51"}, {"size": 1305, "mtime": 1754537956393, "results": "79", "hashOfConfig": "51"}, {"size": 3123, "mtime": 1754537956393, "results": "80", "hashOfConfig": "51"}, {"size": 4936, "mtime": 1754537956393, "results": "81", "hashOfConfig": "51"}, {"size": 4118, "mtime": 1754537956393, "results": "82", "hashOfConfig": "51"}, {"size": 1830, "mtime": 1754537956393, "results": "83", "hashOfConfig": "51"}, {"size": 283, "mtime": 1754537956393, "results": "84", "hashOfConfig": "51"}, {"size": 4905, "mtime": 1754537956394, "results": "85", "hashOfConfig": "51"}, {"size": 3141, "mtime": 1754560042289, "results": "86", "hashOfConfig": "51"}, {"size": 4591, "mtime": 1754551742734, "results": "87", "hashOfConfig": "51"}, {"size": 7318, "mtime": 1754560212626, "results": "88", "hashOfConfig": "51"}, {"size": 7284, "mtime": 1754556085672, "results": "89", "hashOfConfig": "51"}, {"size": 6752, "mtime": 1754537956395, "results": "90", "hashOfConfig": "51"}, {"size": 6213, "mtime": 1754553532724, "results": "91", "hashOfConfig": "51"}, {"size": 4413, "mtime": 1754537956395, "results": "92", "hashOfConfig": "51"}, {"size": 3042, "mtime": 1754555768491, "results": "93", "hashOfConfig": "51"}, {"size": 4271, "mtime": 1754554729123, "results": "94", "hashOfConfig": "51"}, {"size": 4756, "mtime": 1754537956396, "results": "95", "hashOfConfig": "51"}, {"size": 133, "mtime": 1754537956396, "results": "96", "hashOfConfig": "51"}, {"size": 2671, "mtime": 1754537956396, "results": "97", "hashOfConfig": "51"}, {"size": 582, "mtime": 1754537956387, "results": "98", "hashOfConfig": "51"}, {"size": 9926, "mtime": 1754555828304, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8arahf", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\(overview)\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\Layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\article\\Layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\article\\[articleId]\\articleIds.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\article\\[articleId]\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\news\\Layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\news\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\(overview)\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\c31\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\product-detail\\[productId]\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\[type]\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\about\\about-content.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\article\\article-content.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\article\\article-hot.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\article\\article-title.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\back-to-top.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\banner.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\bread-crumbs.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\certificates.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\dropdown-window.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\flex-2items-box.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\flex-4items-box.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\nav-list.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\page-tabs.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\pagination.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\components\\show-more.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\about.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\banner-slider.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\footer.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\hot-spot.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\home\\nav.tsx", [], ["247"], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\product\\product-info.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\product\\product-list.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\product\\product-preview.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\downloads.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\help.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\ui\\support\\manuals.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\videos\\layout.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\videos\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\[...rest]\\page.tsx", [], [], "D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\t1pro\\page.tsx", [], [], {"ruleId": "248", "severity": 2, "message": "249", "line": 25, "column": 35, "nodeType": "250", "endLine": 25, "endColumn": 51, "suppressions": "251"}, "react-hooks/rules-of-hooks", "React Hook \"useCurrentLocale\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", ["252"], {"kind": "253", "justification": "254"}, "directive", ""]