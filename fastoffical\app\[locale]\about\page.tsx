import AboutContent from '../ui/about/about-content'
import { PageProps } from '@/data/type'
import type { Metadata, ResolvingMetadata } from 'next'

export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    title: locale === 'zh' ? '关于我们' : 'About Us',
    description: '赛蓝科技 引领生活',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default function Page() {
  return <AboutContent />
}
