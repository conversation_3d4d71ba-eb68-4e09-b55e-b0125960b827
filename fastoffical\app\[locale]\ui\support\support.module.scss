.downloads {
  width: var(--width-content);
  margin: auto;
  padding-top: 34px;

  h2 {
    text-align: center;
  }

  p {
    color: var(--text-description);
    font-size: var(--font-medium);
    text-align: center;
    margin-top: 10px;
  }

  &__content {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin: auto;
    margin-top: 30px;
    flex-wrap: wrap;
    max-width: 800px;
    &__card {
      width: 240px;
      background-color: #fff;
      border-radius: 6px;
      padding: 30px;
      text-align: center;
      display: flex;
      flex-direction: column;
      gap: 10px;
      align-items: center;

      button {
        height: 44px;
        width: 160px;
        line-height: 44px;
        color: #fff;
        background-color: var(--color-theme);
        font-size: var(--font-medium);
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        border-radius: 6px;
      }
    }
  }
}

.manuals {
  padding-top: 34px;
  width: var(--width-content);
  margin: auto;

  &__captain {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__navs {
      &__button {
        padding: 0 20px;
        height: 33px;
        line-height: 33px;
        border-radius: 33px;
        color: var(--text-description);

        &--active {
          color: #fff;
          background-color: var(--color-theme);
        }
      }
    }

    &__languages {
      padding: 0 18px;
      height: 34px;
      display: flex;
      align-items: center;
      line-height: 34px;
      gap: 8px;
      color: var(--text-description);
      background-color: #fff;
      border-radius: 34px;

      span {
        color: var(--gray-4);

        &:hover {
          cursor: default;
        }
      }

      &__item {
        color: var(--text-description);

        &--active {
          font-size: var(--font-medium);
          color: var(--text-dark);
        }
      }
    }
  }

  &__list {
    margin-top: 30.5px;
    display: flex;
    gap: 25px;
    flex-wrap: wrap;

    &__item {
      display: flex;
      gap: 20px;
      width: 410px;
      background-color: #fff;
      align-items: center;
      border-radius: 6px;

      &__image {
        width: 60px;
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-right: 1px solid #f2f2f2;
      }

      span {
        width: 254px;
        font-size: var(--font-medium);
      }
    }
  }

  &__pagination {
    margin-top: 30px;
  }
}

.help {
  width: var(--width-content);
  padding-top: 30px;
  margin: auto;

  &__nav {
    display: flex;
    gap: 16px;

    &__item {
      height: 38px;
      padding: 0 26px;
      line-height: 38px;
      font-size: var(--font-medium);
      color: var(--text-description);
      border-radius: 38px;
      background-color: #fff;

      &--active {
        background-color: var(--color-theme);
        color: #fff;
      }
    }
  }

  &__list {
    margin-top: 30px;
  }

  &__pagination {
    margin-top: 30px;
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .manuals {
    padding: 34.5px 20px 0;

    &__list {
      margin-top: 30.5px;
      gap: 20px;

      &__item {
        flex: 1;
        width: auto;
        justify-content: space-between;
        gap: 20px;
        padding-right: 10px;

        span {
          width: auto;
          min-width: 160px;
          max-width: 254px;
          flex: 1;
        }
      }
    }
  }

  .help {
    padding: 34px 20px 0;
  }
}

@media (max-width: 450px) {
  .downloads {
    padding: 16px 16px 0;

    &__content {
      margin-top: 15px;
      gap: 15px;
      flex-direction: column;

      &__card {
        width: 100%;

        button {
          width: 263px;
        }
      }
    }
  }

  .manuals {
    padding: 16px 16px 0;

    &__captain {
      &__menu {
        &__item {
          height: 26px;
          display: flex;
          line-height: 26px;
          align-items: center;
          gap: 10px;
          font-size: var(--font-medium);
        }
      }
    }

    &__list {
      margin-top: 15px;
      gap: 10px;

      &__item {
        justify-content: space-between;
        width: 100%;
        gap: 20px;
        padding-right: 10px;

        span {
          flex: 1;
        }
      }
    }

    &__pagination {
      margin-top: 0;
    }
  }

  .help {
    padding: 20px 16px 0;

    &__list {
      margin-top: 15px;
    }

    &__pagination {
      margin-top: 0;
    }
  }
}