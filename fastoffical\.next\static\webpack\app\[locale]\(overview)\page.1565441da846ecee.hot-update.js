"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(overview)/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/home/<USER>":
/*!************************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BannerSlider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./home.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BannerSlider() {\n    _s();\n    const listRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isScrolling, setIsScrolling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleTouchStart = (e)=>{\n        setTouchStart({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY,\n            t: Date.now()\n        });\n        setTouchEnd(null);\n    };\n    const handleTouchMove = (e)=>{\n        if (!touchStart) return;\n        setTouchEnd({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY,\n            t: Date.now()\n        });\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const deltaX = touchEnd.x - touchStart.x;\n        const deltaY = touchEnd.y - touchStart.y;\n        const deltaT = touchEnd.t - touchStart.t;\n        // 判断是否为水平滑动\n        if (Math.abs(deltaX) > Math.abs(deltaY)) {\n            const speed = Math.abs(deltaX) / deltaT // 计算水平滑动速度\n            ;\n            const threshold = 0.3 // 设置滑动速度阈值,单位为像素/毫秒\n            ;\n            if (speed > threshold) {\n                handleSwitchClick(deltaX > 0 ? -1 : 1);\n            } else {\n                var _listRef_current;\n                // 如果滑动速度不够,则回到原来的位置\n                scrollTo(((_listRef_current = listRef.current) === null || _listRef_current === void 0 ? void 0 : _listRef_current.scrollLeft) || 0);\n            }\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n    };\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale)();\n    const images = [\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>",\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>"\n    ];\n    const scrollTo = (x)=>{\n        if (isScrolling) return;\n        if (listRef.current) {\n            var _listRef_current;\n            const currentLeft = currentIndex * ((_listRef_current = listRef.current) === null || _listRef_current === void 0 ? void 0 : _listRef_current.clientWidth);\n            const step = (x - currentLeft) / 60;\n            let scrollLeft = currentLeft;\n            setIsScrolling(true);\n            let interval;\n            interval = setInterval(()=>{\n                if (listRef.current) {\n                    scrollLeft += step;\n                    if (scrollLeft >= x && step > 0 || scrollLeft <= x && step < 0) {\n                        scrollLeft = x;\n                        listRef.current.scrollLeft = scrollLeft;\n                        setIsScrolling(false);\n                        clearInterval(interval);\n                    }\n                    listRef.current.scrollLeft = scrollLeft;\n                }\n            }, 1);\n        }\n    };\n    const handleSwitchClick = (count)=>{\n        var _listRef_current;\n        if (currentIndex === 0 && count === -1) return;\n        else if (currentIndex === images.length - 1 && count === +1) return;\n        else if (!listRef.current) return;\n        if (isScrolling) return;\n        scrollTo(((_listRef_current = listRef.current) === null || _listRef_current === void 0 ? void 0 : _listRef_current.clientWidth) * (currentIndex + count));\n        setCurrentIndex(currentIndex + count);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider\"]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: listRef,\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__list\"]),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: images.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: item,\n                            width: 1920,\n                            height: 500,\n                            alt: \"\",\n                            unoptimized: true,\n                            style: {\n                                width: \"100%\",\n                                height: \"100%\",\n                                objectFit: \"cover\",\n                                objectPosition: \"center\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Swicher, {\n                        position: \"left\",\n                        disabled: true,\n                        onClick: ()=>handleSwitchClick(-1)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Swicher, {\n                        position: \"right\",\n                        onClick: ()=>handleSwitchClick(1)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Indicator, {\n                        count: images.length,\n                        currentIndex: currentIndex\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(BannerSlider, \"SvMZ0xsECR6lvVlPNpNwjOX/rJY=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale\n    ];\n});\n_c = BannerSlider;\nfunction Swicher(param) {\n    let { position = \"left\", disabled = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: ()=>onClick(),\n        className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"banner-slider__switcher\".concat(position === \"left\" ? \"\" : \"--right\")], \" \").concat(disabled ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__switcher--disabled\"]) : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: \"/slider-\".concat(position === \"left\" ? \"left\" : \"right\", \".svg\"),\n            width: 44,\n            height: 44,\n            alt: \"\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Swicher;\nfunction Indicator(param) {\n    let { count, currentIndex } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator\"]),\n            children: Array(count).fill(\"\").map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item\"]), \" \").concat(index === currentIndex ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item--active\"]) : \"\")\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_c2 = Indicator;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"BannerSlider\");\n$RefreshReg$(_c1, \"Swicher\");\n$RefreshReg$(_c2, \"Indicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/home/<USER>"));

/***/ })

});