'use client'
import './components.scss'
import Image from 'next/image'
import Banner from './banner'
import React from 'react'
import Link from 'next/link'

type tab = {
  id: string
  text: string
}

export default function PageTabs({
  title = '',
  iconSrc = '',
  tabs = [],
  currentTab,
  bannerSrc = '/pic_shipinbg@2x (1).webp',
  bannerMobileSrc = '/pic_shipinbg@2x (1).webp',
  background = 'rgb(179, 220, 252)',
  onTabChange = () => {},
  showBanner = true,
  isSearch = false,
  isLink = false,
}: {
  title: string
  iconSrc: string
  tabs: Array<tab>
  currentTab: string
  bannerSrc?: string
  showBanner?: boolean
  background?: string
  onTabChange?: Function
  isSearch?: boolean
  bannerMobileSrc?: string
  isLink?: boolean
}) {
  const handleTabChange = (tab: string) => {
    onTabChange(tab)
  }

  const Title = () => (
    <div className={'page-tabs__content'}>
      <div className={`page-tabs__content__title`}>
        <Image src={iconSrc} width={34} height={34} alt=""></Image>
        <h1>{title}</h1>
      </div>
    </div>
  )

  const TabItems = ({ isLink = false }: { isLink?: boolean }) => {
    return (
      <div className={`page-tabs__content__items hide-on-small`}>
        {tabs.map((item, index) => (
          <React.Fragment key={index}>
            {isLink ? (
              <Link href={`${item.id}`}>
                <button
                  className={`${`page-tabs__content__items__item`} ${
                    currentTab === item.id
                      ? `page-tabs__content__items__item--active`
                      : ''
                  }`}
                  onClick={() => handleTabChange(item.id)}
                >
                  {item.text}
                </button>
              </Link>
            ) : (
              <button
                className={`${`page-tabs__content__items__item`} ${
                  currentTab === item.id
                    ? `page-tabs__content__items__item--active`
                    : ''
                }`}
                onClick={() => handleTabChange(item.id)}
              >
                {item.text}
              </button>
            )}
          </React.Fragment>
        ))}
      </div>
    )
  }

  return (
    <>
      <div className={'page-tabs'} style={background ? { background } : {}}>
        {showBanner && <Banner src={bannerSrc} mobileSrc={bannerMobileSrc} />}
        <Title />
        {/* {isSearch && <SearchArea />} */}
        {!isSearch && <TabItems isLink={isLink} />}
      </div>
      <SmallTabs
        onTabChange={onTabChange}
        tabs={tabs}
        currentTab={currentTab}
        isLink={isLink}
      />
    </>
  )
}

function SearchArea() {
  return (
    <div className={`page-tabs__search hide-on-small`}>
      <input
        type="text"
        placeholder="请输入关键字，例如：看家王C31、大屏摄像机"
      />
      <button>
        <Image
          src={'/search-icon-white.svg'}
          width={20}
          height={20}
          alt=""
        ></Image>
        搜索
      </button>
    </div>
  )
}

function SmallTabs({
  tabs,
  currentTab,
  onTabChange,
  isLink,
}: {
  tabs: Array<tab>
  currentTab: string
  onTabChange: Function
  isLink?: boolean
}) {
  const Tab = ({ id, text }: { id: string; text: string }) => {
    return (
      <div
        className={`${
          currentTab === id ? 'page-tabs__tabs-small__tab--active' : ''
        } page-tabs__tabs-small__tab`}
        onClick={() => {
          onTabChange(id)
        }}
      >
        {text}
      </div>
    )
  }

  return (
    <div className="page-tabs__tabs-small hide-on-medium hide-on-large">
      {tabs.map((item, index) => (
        <React.Fragment key={index}>
          {isLink ? (
            <Link href={item.id}>
              <Tab {...item} />
            </Link>
          ) : (
            <Tab {...item} />
          )}
        </React.Fragment>
      ))}
    </div>
  )
}
