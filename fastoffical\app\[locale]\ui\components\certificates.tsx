'use client'
import Image from 'next/image'
import { useI18n, useCurrentLocale, I18nProviderClient } from '@/locales/client'

export default function CylanCertificatesLayout() {
  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <CylanCertificates />
    </I18nProviderClient>
  )
}

function CylanCertificates() {
  const t = useI18n()

  const certificates = [
    {
      imageSrc: '/pride-image-1.jpg',
      title: t('pride1'),
      imageWidth: 120,
    },
    {
      imageSrc: '/pride-image-2.jpg',
      title: t('pride2'),
      imageWidth: 242,
    },
    {
      imageSrc: '/pride-image-3.jpg',
      title: t('pride3'),
      imageWidth: 120,
    },
    {
      imageSrc: '/pride-image-4.jpg',
      title: t('pride4'),
      imageWidth: 120,
    },
    {
      imageSrc: '/pride-image-5.jpg',
      title: t('pride5'),
      imageWidth: 240,
    },
    {
      imageSrc: '/pride-image-6.jpg',
      title: t('pride6'),
      imageWidth: 240,
    },
    {
      imageSrc: '/pride-image-7.jpg',
      title: t('pride7'),
      imageWidth: 240,
    },
    {
      imageSrc: '/pride-image-8.jpg',
      title: t('pride8'),
      imageWidth: 240,
    },
    {
      imageSrc: '/pride-image-9.jpg',
      title: t('pride9'),
      imageWidth: 240,
    },
  ]

  const CertificateItem = ({
    imageSrc,
    title,
    imageWidth,
  }: {
    imageSrc: string
    title: string
    imageWidth: number
  }) => {
    return (
      <div style={{ width: imageWidth }}>
        <div>
          <Image
            src={imageSrc}
            height={160}
            width={imageWidth}
            alt=""
            unoptimized
          ></Image>
        </div>
        <div>{title}</div>
      </div>
    )
  }

  return (
    <div className={`cylan-certificates hide-on-medium hide-on-large`}>
      {certificates.map((item, index) => (
        <CertificateItem key={index} {...item} />
      ))}
    </div>
  )
}
