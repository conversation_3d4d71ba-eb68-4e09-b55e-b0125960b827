'use client'

import { useState, useRef } from 'react'
import styles from './home.module.scss'
import Image from 'next/image'
import { useCurrentLocale } from '@/locales/client'

export default function BannerSlider() {
  const listRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)
  const [touchStart, setTouchStart] = useState<{
    x: number
    y: number
    t: number
  } | null>(null)
  const [touchEnd, setTouchEnd] = useState<{
    x: number
    y: number
    t: number
  } | null>(null)

  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
      t: Date.now(),
    })
    setTouchEnd(null)
  }

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (!touchStart) return
    setTouchEnd({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
      t: Date.now(),
    })
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const deltaX = touchEnd.x - touchStart.x
    const deltaY = touchEnd.y - touchStart.y
    const deltaT = touchEnd.t - touchStart.t

    // 判断是否为水平滑动
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      const speed = Math.abs(deltaX) / deltaT // 计算水平滑动速度
      const threshold = 0.3 // 设置滑动速度阈值,单位为像素/毫秒

      if (speed > threshold) {
        handleSwitchClick(deltaX > 0 ? -1 : 1)
      } else {
        // 如果滑动速度不够,则回到原来的位置
        scrollTo(listRef.current?.scrollLeft || 0)
      }
    }

    setTouchStart(null)
    setTouchEnd(null)
  }

  const locale = useCurrentLocale()
  const images = [
    locale === 'zh' ? '/home/<USER>' : '/home/<USER>',
    locale === 'zh' ? '/home/<USER>' : '/home/<USER>',
  ]

  const scrollTo = (x: number) => {
    if (isScrolling) return
    if (listRef.current) {
      const currentLeft = currentIndex * listRef.current?.clientWidth
      const step = (x - currentLeft) / 60

      let scrollLeft = currentLeft
      setIsScrolling(true)

      let interval: NodeJS.Timeout
      interval = setInterval(() => {
        if (listRef.current) {
          scrollLeft += step
          if ((scrollLeft >= x && step > 0) || (scrollLeft <= x && step < 0)) {
            scrollLeft = x
            listRef.current.scrollLeft = scrollLeft
            setIsScrolling(false)
            clearInterval(interval)
          }
          listRef.current.scrollLeft = scrollLeft
        }
      }, 1)
    }
  }

  const handleSwitchClick = (count: number) => {
    if (currentIndex === 0 && count === -1) return
    else if (currentIndex === images.length - 1 && count === +1) return
    else if (!listRef.current) return
    if (isScrolling) return
    scrollTo(listRef.current?.clientWidth * (currentIndex + count))
    setCurrentIndex(currentIndex + count)
  }

  return (
    <div className={styles['banner-slider']}>
      <div
        ref={listRef}
        className={styles['banner-slider__list']}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {images.map((item, index) => (
          <div key={index}>
            <Image
              src={item}
              width={1920}
              height={500}
              alt=""
              unoptimized
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                objectPosition: 'center',
              }}
            ></Image>
          </div>
        ))}
      </div>

      {images.length > 1 && (
        <>
          {/* 左右翻页按钮 */}
          <Swicher
            position="left"
            disabled
            onClick={() => handleSwitchClick(-1)}
          />
          <Swicher position="right" onClick={() => handleSwitchClick(1)} />

          {/* 翻页器 */}
          <Indicator count={images.length} currentIndex={currentIndex} />
        </>
      )}
    </div>
  )
}

function Swicher({
  position = 'left',
  disabled = false,
  onClick,
}: {
  position: string
  disabled?: boolean
  onClick: Function
}) {
  return (
    <div
      onClick={() => onClick()}
      className={`${
        styles[`banner-slider__switcher${position === 'left' ? '' : '--right'}`]
      } ${disabled ? styles[`banner-slider__switcher--disabled`] : ``}`}
    >
      <Image
        src={`/slider-${position === 'left' ? 'left' : 'right'}.svg`}
        width={44}
        height={44}
        alt=""
      ></Image>
    </div>
  )
}

function Indicator({
  count,
  currentIndex,
}: {
  count: number
  currentIndex: number
}) {
  return (
    <>
      <div className={styles[`banner-slider__indicator`]}>
        {Array(count)
          .fill('')
          .map((item, index) => (
            <div
              key={index}
              className={`${styles[`banner-slider__indicator__item`]} ${
                index === currentIndex
                  ? styles[`banner-slider__indicator__item--active`]
                  : ''
              }`}
            ></div>
          ))}
      </div>
    </>
  )
}
