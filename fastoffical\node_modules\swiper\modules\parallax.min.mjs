import{e as elementChildren}from"../shared/utils.min.mjs";function Parallax(a){let{swiper:e,extendParams:t,on:l}=a;t({parallax:{enabled:!1}});const r="[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]",s=(a,t)=>{const{rtl:l}=e,r=l?-1:1,s=a.getAttribute("data-swiper-parallax")||"0";let i=a.getAttribute("data-swiper-parallax-x"),p=a.getAttribute("data-swiper-parallax-y");const n=a.getAttribute("data-swiper-parallax-scale"),o=a.getAttribute("data-swiper-parallax-opacity"),d=a.getAttribute("data-swiper-parallax-rotate");if(i||p?(i=i||"0",p=p||"0"):e.isHorizontal()?(i=s,p="0"):(p=s,i="0"),i=i.indexOf("%")>=0?parseInt(i,10)*t*r+"%":i*t*r+"px",p=p.indexOf("%")>=0?parseInt(p,10)*t+"%":p*t+"px",null!=o){const e=o-(o-1)*(1-Math.abs(t));a.style.opacity=e}let x=`translate3d(${i}, ${p}, 0px)`;if(null!=n){x+=` scale(${n-(n-1)*(1-Math.abs(t))})`}if(d&&null!=d){x+=` rotate(${d*t*-1}deg)`}a.style.transform=x},i=()=>{const{el:a,slides:t,progress:l,snapGrid:i,isElement:p}=e,n=elementChildren(a,r);e.isElement&&n.push(...elementChildren(e.hostEl,r)),n.forEach((a=>{s(a,l)})),t.forEach(((a,t)=>{let p=a.progress;e.params.slidesPerGroup>1&&"auto"!==e.params.slidesPerView&&(p+=Math.ceil(t/2)-l*(i.length-1)),p=Math.min(Math.max(p,-1),1),a.querySelectorAll(`${r}, [data-swiper-parallax-rotate]`).forEach((a=>{s(a,p)}))}))};l("beforeInit",(()=>{e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)})),l("init",(()=>{e.params.parallax.enabled&&i()})),l("setTranslate",(()=>{e.params.parallax.enabled&&i()})),l("setTransition",((a,t)=>{e.params.parallax.enabled&&function(a){void 0===a&&(a=e.params.speed);const{el:t,hostEl:l}=e,s=[...t.querySelectorAll(r)];e.isElement&&s.push(...l.querySelectorAll(r)),s.forEach((e=>{let t=parseInt(e.getAttribute("data-swiper-parallax-duration"),10)||a;0===a&&(t=0),e.style.transitionDuration=`${t}ms`}))}(t)}))}export{Parallax as default};
//# sourceMappingURL=parallax.min.mjs.map