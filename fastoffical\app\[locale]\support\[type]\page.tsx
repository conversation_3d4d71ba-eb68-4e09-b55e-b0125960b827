import styles from '@/app/[locale]/ui/support/support.module.scss'
import PageTabs from '@/app/[locale]/ui/components/page-tabs'
import Downloads from '@/app/[locale]/ui/support/downloads'
import Manuals from '@/app/[locale]/ui/support/manuals'
import Help from '@/app/[locale]/ui/support/help'
import { getI18n, getCurrentLocale } from '@/locales/server'
import { getHelpDatas, getHelpGroups } from '@/data/helps'
import { HelpData, Group, PageProps } from '@/data/type'

export default async function Page({ params }: { params: { type: string } }) {
  const t = await getI18n()

  const tabs = [
    {
      id: 'download_client',
      text: t('downloadClient'),
    },
    // {
    //   id: '1',
    //   text: '产品说明书',
    // },
    {
      id: 'help',
      text: t('help'),
    },
  ]
  // const [currentTab, setTab] = useState(tabs[0].id)
  let currentTab: string = params.type

  if (!tabs.find((i) => i.id === currentTab)) currentTab = tabs[0].id
  let helpDatas: HelpData[] = []
  let helpGroups: Group[] = []

  if (currentTab === tabs[1].id) {
    helpDatas = await getHelpDatas()
    helpGroups = await getHelpGroups()
  }
  return (
    <div className={styles.support}>
      <PageTabs
        iconSrc="/support-icon.svg"
        title={t('support')}
        currentTab={currentTab}
        tabs={tabs}
        bannerSrc="/support/banner-pc.jpg"
        bannerMobileSrc="/support/banner-mobile.png"
        background="rgb(11,106,151)"
        isLink
        // onTabChange={handelTabChange}
      />
      <div className={styles.support__content}>
        {currentTab === tabs[0].id && <Downloads />}
        {/* {currentTab === '1' && <Manuals />} */}
        {currentTab === tabs[1].id && (
          <Help datas={helpDatas} groups={helpGroups} />
        )}
      </div>
    </div>
  )
}
