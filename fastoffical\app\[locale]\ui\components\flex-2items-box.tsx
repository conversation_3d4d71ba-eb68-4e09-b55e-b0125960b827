import Image from 'next/image'
import React from 'react'

type CardInfo = {
  imageSrc: string
  title: string
  description: string
  time: string
  link?: string
}

export default async function Flex2ItemsBox({
  infos,
  isProductDetail = false,
}: {
  infos: Array<CardInfo>
  isProductDetail?: boolean
}) {
  const Item = ({
    imageSrc,
    title,
    description,
    time,
    link = '/',
  }: CardInfo) => {
    return (
      <a href={link} className={`flex-box-with-2items__item`}>
        <div>
          <Image
            src={imageSrc}
            width={240}
            height={160}
            style={{ width: '100%', height: '100%' }}
            alt=""
          ></Image>
        </div>

        <div>
          <h6>{title}</h6>
          <p className="hide-on-small">{description}</p>
          <div style={{ flex: 1 }}></div>
          <span>{time}</span>
        </div>
      </a>
    )
  }

  return (
    <div
      className={`flex-box-with-2items ${
        isProductDetail ? 'flex-box-with-2items--product-detail' : ''
      }`}
    >
      {infos.map((item, index) => (
        <React.Fragment key={`${index}-${item.title}`}>
          <Item {...item}></Item>
        </React.Fragment>
      ))}
    </div>
  )
}
