(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[853],{6463:function(e,t,_){Promise.resolve().then(_.bind(_,2823))},2823:function(e,t,_){"use strict";_.r(t),_.d(t,{default:function(){return m}});var n=_(7437),i=_(8291),a=_.n(i),o=_(703),s=_(477),r=_(2505),c=_(7212),l=_(2265),d=_(7907),u=_(5421);function m(){return(0,n.jsx)(u.e4,{locale:(0,u.eV)(),children:(0,n.jsx)(h,{})})}function h(){let e=(0,u.QT)(),[t,_]=(0,l.useState)([{id:"0",text:e("about<PERSON>ylan")},{id:"1",text:e("cylanPrides")},{id:"2",text:e("contactUs")}]),[i,r]=(0,l.useState)(t[0].id),[c,d]=(0,l.useState)(i),m=(0,l.useRef)(null),h=e=>{d(e)};return(0,n.jsxs)("div",{className:a().about,children:[(0,n.jsx)("div",{className:"".concat(a().about__image," hide-on-small"),children:(0,n.jsx)(o.default,{src:"/about-banner.webp",width:1920,height:900,alt:"",style:{width:"100%",height:"100%",objectFit:"fill",objectPosition:"center"},unoptimized:!0})}),(0,n.jsx)("h6",{id:"about-cylan"}),(0,n.jsx)("div",{className:"hide-on-small",children:(0,n.jsx)(s.default,{title:e("aboutUs"),iconSrc:"/about-icon.svg",currentTab:i,tabs:t,showBanner:!1,background:"transparent",onTabChange:e=>{h(e)}})}),(0,n.jsx)("div",{className:"hide-on-medium hide-on-large",children:(0,n.jsx)(s.default,{title:e("aboutUs"),iconSrc:"/about-icon.svg",currentTab:i,tabs:t,bannerMobileSrc:"/about-banner-mobile.jpg",background:"rgb(214,218,211)",onTabChange:e=>{h(e)}})}),(0,n.jsx)(l.Suspense,{children:(0,n.jsx)(p,{contentRef:m,tabs:t,setCurrentTab:r,trigger:c})})]})}function p(e){let{tabs:t,setCurrentTab:_=()=>{},contentRef:i,trigger:o}=e,s=(0,l.useRef)(null),c=(0,l.useRef)(null),m=(0,l.useRef)(null),h=(0,l.useRef)(!0),p=(0,d.useRouter)(),x=(0,d.usePathname)(),j=(0,l.useCallback)(e=>{let n;n=e===t[0].id?"about-cylan":e===t[1].id?"prides":"contacts",_(e),p.replace("".concat(x,"/#").concat(n))},[t,_,p,x]);return(0,l.useEffect)(()=>{let e=()=>{var e;let n=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop,i=null===(e=s.current)||void 0===e?void 0:e.getBoundingClientRect().top;i&&n<i&&_(t[0].id)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[j,t,_]),(0,l.useEffect)(()=>{h.current?h.current=!1:j(o)},[o,j]),(0,n.jsxs)(u.e4,{locale:(0,u.eV)(),children:[(0,n.jsxs)("div",{ref:i,className:a().about__content,children:[(0,n.jsx)("div",{ref:s,children:(0,n.jsx)(b,{})}),(0,n.jsx)(g,{}),(0,n.jsx)("div",{ref:c,children:(0,n.jsx)(f,{})}),(0,n.jsx)("h6",{id:"contacts"}),(0,n.jsx)("div",{ref:m,className:"".concat(a().about__content__contacts," hide-on-small"),children:(0,n.jsx)(r.Contacts,{isAboutPage:!0})})]}),(0,n.jsx)("div",{className:"".concat(a().about__content__contacts," hide-on-medium hide-on-large "),children:(0,n.jsx)(r.Contacts,{isAboutPage:!0})})]})}function b(){let e=(0,u.QT)(),t=[{imageSrc:"/hotspot-image-1.png",title:e("aboutCylanQuotesTitle1"),text:e("aboutCylanQuotesText1")},{imageSrc:"/hotspot-image-1.png",title:e("aboutCylanQuotesTitle2"),text:e("aboutCylanQuotesText2")},{imageSrc:"/hotspot-image-1.png",title:e("aboutCylanQuotesTitle3"),text:e("aboutCylanQuotesText3")}],_=e=>{let{imageSrc:t,title:_,text:i}=e;return(0,n.jsx)("div",{className:a().about__content__main__quotes__item,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("h5",{children:_}),(0,n.jsx)("span",{children:i})]})})};return(0,n.jsxs)("div",{className:a().about__content__main,children:[(0,n.jsx)("h1",{children:e("aboutCylan")}),(0,n.jsx)("p",{children:e("aboutCylanDescription")}),(0,n.jsx)("div",{className:a().about__content__main__quotes,children:t.map((e,t)=>(0,n.jsx)(_,{...e},t))})]})}function g(){var e;let t;let _=(0,u.QT)();return(e=t||(t={}))[e.first=0]="first",e[e.normal=1]="normal",e[e.last=2]="last",(0,n.jsxs)("div",{className:a().about__content__career,children:[(0,n.jsx)("h1",{children:_("career")}),(0,n.jsx)("p",{children:_("careerDescription")}),(0,n.jsx)("div",{className:a().about__content__career__companytime,children:(0,n.jsx)(r.CompanyTime,{isAboutPage:!0})}),(0,n.jsx)("h6",{id:"prides"})]})}function f(){var e;let t;let _=(0,u.QT)();(e=t||(t={}))[e.small=0]="small",e[e.normal=1]="normal",e[e.large=2]="large";let[i,s]=(0,l.useState)([{text:_("pride1"),show:!1},{text:_("pride2"),show:!1},{text:_("pride3"),show:!1},{text:_("pride4"),show:!1},{text:_("pride5"),show:!1},{text:_("pride6"),show:!1},{text:_("pride7"),show:!1},{text:_("pride8"),show:!1},{text:_("pride9"),show:!1}]),r=e=>{let{src:t,size:_=1,index:r}=e;return(0,n.jsxs)("div",{onClick:()=>{s(i.map((e,t)=>(t===r&&(e.show=!e.show),e)))},className:"".concat(a().about__content__prides__list__item," ").concat(2===_?a()["about__content__prides__list__item--large"]:1===_?a()["about__content__prides__list__item--normal"]:a()["about__content__prides__list__item--small"]),children:[(0,n.jsx)(o.default,{src:t,width:2===_?478:231,height:0===_?154:326,alt:"",style:{height:"100%",width:"100%",objectFit:"fill"},unoptimized:!0}),i[r].show&&(0,n.jsx)("div",{className:a().about__content__prides__list__item__cover,children:i[r].text})]})};return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"".concat(a().about__content__prides),children:[(0,n.jsx)("h1",{children:_("cylanPrides")}),(0,n.jsxs)("div",{className:"hide-on-small",children:[(0,n.jsxs)("div",{className:a().about__content__prides__list,children:[(0,n.jsx)(r,{src:"/pride-image-1.jpg",index:0}),(0,n.jsx)(r,{src:"/pride-image-2.jpg",size:2,index:1}),(0,n.jsx)(r,{src:"/pride-image-3.jpg",index:2}),(0,n.jsx)(r,{src:"/pride-image-4.jpg",index:3})]}),(0,n.jsxs)("div",{className:a().about__content__prides__list,children:[(0,n.jsx)(r,{src:"/pride-image-5.jpg",size:0,index:4}),(0,n.jsx)(r,{src:"/pride-image-6.jpg",size:0,index:5}),(0,n.jsx)(r,{src:"/pride-image-7.jpg",size:0,index:6}),(0,n.jsx)(r,{src:"/pride-image-8.jpg",size:0,index:7}),(0,n.jsx)(r,{src:"/pride-image-9.jpg",size:0,index:8})]})]}),(0,n.jsx)(c.Z,{})]})})}},7212:function(e,t,_){"use strict";_.d(t,{Z:function(){return o}});var n=_(7437),i=_(703),a=_(5421);function o(){return(0,n.jsx)(a.e4,{locale:(0,a.eV)(),children:(0,n.jsx)(s,{})})}function s(){let e=(0,a.QT)(),t=[{imageSrc:"/pride-image-1.jpg",title:e("pride1"),imageWidth:120},{imageSrc:"/pride-image-2.jpg",title:e("pride2"),imageWidth:242},{imageSrc:"/pride-image-3.jpg",title:e("pride3"),imageWidth:120},{imageSrc:"/pride-image-4.jpg",title:e("pride4"),imageWidth:120},{imageSrc:"/pride-image-5.jpg",title:e("pride5"),imageWidth:240},{imageSrc:"/pride-image-6.jpg",title:e("pride6"),imageWidth:240},{imageSrc:"/pride-image-7.jpg",title:e("pride7"),imageWidth:240},{imageSrc:"/pride-image-8.jpg",title:e("pride8"),imageWidth:240},{imageSrc:"/pride-image-9.jpg",title:e("pride9"),imageWidth:240}],_=e=>{let{imageSrc:t,title:_,imageWidth:a}=e;return(0,n.jsxs)("div",{style:{width:a},children:[(0,n.jsx)("div",{children:(0,n.jsx)(i.default,{src:t,height:160,width:a,alt:"",unoptimized:!0})}),(0,n.jsx)("div",{children:_})]})};return(0,n.jsx)("div",{className:"cylan-certificates hide-on-medium hide-on-large",children:t.map((e,t)=>(0,n.jsx)(_,{...e},t))})}},5738:function(e,t,_){"use strict";_.d(t,{H:function(){return i},Z:function(){return c}});var n,i,a=_(7437),o=_(703),s=_(2265);_(2135);var r=_(8792);function c(e){let{infos:t,imageSize:_,imageBox:n,mode:i="",gap:c=0,isDetail:l=!1}=e,d=e=>{let{imageSrc:t,title:s,tip:c="",link:l="",videoSrc:d=""}=e,u=!!l,m=!!d,h="flex-box-with-4items__card ".concat("product"===i?"flex-box-with-4items__card--product":""," ").concat(u?"flex-box-with-4items__card--link":""," ").concat(m?"flex-box-with-4items__card--video":""),p=(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex-box-with-4items__card__image",style:{aspectRatio:n.width/n.height},children:m?(0,a.jsxs)("video",{width:_.width,height:_.height,controls:!0,poster:t,children:[(0,a.jsx)("source",{src:d,type:"video/mp4"}),"Your browser does not support the video tag."]}):(0,a.jsx)(o.default,{unoptimized:!0,src:t,width:_.width,height:_.height,alt:"image",style:{objectFit:"fill",width:"100%",height:"auto"}})}),(0,a.jsxs)("div",{className:"".concat(""===i?"":"flex-box-with-4items__card__info--".concat(i)," flex-box-with-4items__card__info"),children:[(0,a.jsx)("div",{children:s}),"pride"===i?"":c?(0,a.jsx)("span",{children:c}):""]})]});return u?(0,a.jsx)(r.default,{href:l,className:h,children:p}):(0,a.jsx)("div",{className:h,children:p})};return(0,a.jsx)("div",{className:"flex-box-with-4items ".concat("product"===i?"flex-box-with-4items--product":""," ").concat(l?"flex-box-with-4items--detail":""),style:c?{gap:c}:{},children:t.map((e,t)=>(0,a.jsx)(s.Fragment,{children:d(e)},"".concat(e.link,"-").concat(t)))})}(n=i||(i={})).normal="",n.product="product",n.pride="pride"},477:function(e,t,_){"use strict";_.r(t),_.d(t,{default:function(){return r}});var n=_(7437);_(2135);var i=_(703);function a(e){let{src:t="/search-banner.webp",mobileSrc:_="/search-banner.webp"}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"banner hide-on-small",children:(0,n.jsx)(i.default,{src:t,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})}),(0,n.jsx)("div",{className:"banner hide-on-medium hide-on-large",children:(0,n.jsx)(i.default,{src:_,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})})]})}var o=_(2265),s=_(8792);function r(e){let{title:t="",iconSrc:_="",tabs:r=[],currentTab:l,bannerSrc:d="/pic_shipinbg@2x (1).webp",bannerMobileSrc:u="/pic_shipinbg@2x (1).webp",background:m="rgb(179, 220, 252)",onTabChange:h=()=>{},showBanner:p=!0,isSearch:b=!1,isLink:g=!1}=e,f=e=>{h(e)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"page-tabs",style:m?{background:m}:{},children:[p&&(0,n.jsx)(a,{src:d,mobileSrc:u}),(0,n.jsx)(()=>(0,n.jsx)("div",{className:"page-tabs__content",children:(0,n.jsxs)("div",{className:"page-tabs__content__title",children:[(0,n.jsx)(i.default,{src:_,width:34,height:34,alt:""}),(0,n.jsx)("h1",{children:t})]})}),{}),!b&&(0,n.jsx)(e=>{let{isLink:t=!1}=e;return(0,n.jsx)("div",{className:"page-tabs__content__items hide-on-small",children:r.map((e,_)=>(0,n.jsx)(o.Fragment,{children:t?(0,n.jsx)(s.default,{href:"".concat(e.id),children:(0,n.jsx)("button",{className:"page-tabs__content__items__item ".concat(l===e.id?"page-tabs__content__items__item--active":""),onClick:()=>f(e.id),children:e.text})}):(0,n.jsx)("button",{className:"page-tabs__content__items__item ".concat(l===e.id?"page-tabs__content__items__item--active":""),onClick:()=>f(e.id),children:e.text})},_))})},{isLink:g})]}),(0,n.jsx)(c,{onTabChange:h,tabs:r,currentTab:l,isLink:g})]})}function c(e){let{tabs:t,currentTab:_,onTabChange:i,isLink:a}=e,r=e=>{let{id:t,text:a}=e;return(0,n.jsx)("div",{className:"".concat(_===t?"page-tabs__tabs-small__tab--active":""," page-tabs__tabs-small__tab"),onClick:()=>{i(t)},children:a})};return(0,n.jsx)("div",{className:"page-tabs__tabs-small hide-on-medium hide-on-large",children:t.map((e,t)=>(0,n.jsx)(o.Fragment,{children:a?(0,n.jsx)(s.default,{href:e.id,children:(0,n.jsx)(r,{...e})}):(0,n.jsx)(r,{...e})},t))})}},2505:function(e,t,_){"use strict";_.r(t),_.d(t,{CompanyTime:function(){return m},Contacts:function(){return p},default:function(){return d}});var n=_(7437),i=_(1809),a=_.n(i),o=_(8792),s=_(703),r=_(5738),c=_(7212),l=_(5421);function d(){return(0,n.jsx)(l.e4,{locale:(0,l.eV)(),children:(0,n.jsx)(u,{})})}function u(){let e=(0,l.QT)();return(0,n.jsxs)("div",{className:a().about,children:[(0,n.jsx)("div",{className:a().about__cover}),(0,n.jsxs)("div",{className:a().about__content,children:[(0,n.jsx)("h3",{children:e("aboutUs")}),(0,n.jsx)(m,{}),(0,n.jsx)(h,{}),(0,n.jsx)(p,{})]})]})}function m(e){let{isAboutPage:t=!1}=e,_=(0,l.QT)(),i=(0,l.eV)(),o=e=>{let{num:t,unit:_,text:i}=e;return(0,n.jsxs)("div",{className:a().about__content__time__item,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{children:t}),(0,n.jsx)("span",{children:_})]}),(0,n.jsx)("div",{children:i})]})},s=()=>(0,n.jsx)("div",{style:{height:46,width:0,opacity:.1,border:"1px solid ".concat(t?"var(--gray)":"#fff")}});return(0,n.jsxs)("div",{className:"".concat(a().about__content__time," ").concat(t?a()["about__content__time--page"]:""),children:[(0,n.jsx)(o,{num:2005,unit:_("year"),text:_("companiyTime")}),(0,n.jsx)(s,{}),(0,n.jsx)(o,{num:"zh"===i?45:450,unit:"zh"===i?"万+":"K+",text:_("deviceSells")}),(0,n.jsx)(s,{}),(0,n.jsx)(o,{num:"zh"===i?90:900,unit:"zh"===i?"万+":"K+",text:_("activeUsers")})]})}function h(){let e=(0,l.QT)(),t={infos:[{imageSrc:"/pride-image-6.jpg",title:e("pride6")},{imageSrc:"/pride-image-9.jpg",title:e("pride9")},{imageSrc:"/pride-image-5.jpg",title:e("pride5")},{imageSrc:"/pride-image-2.jpg",title:e("pride2")}],imageSize:{width:300,height:200},imageBox:{width:300,height:200},mode:r.H.pride};return(0,n.jsxs)("div",{className:a().about__prides,children:[(0,n.jsx)("div",{className:"hide-on-small",style:{marginTop:30},children:(0,n.jsx)(r.Z,{...t})}),(0,n.jsx)(c.Z,{})]})}function p(e){let{isAboutPage:t=!1}=e,_=(0,l.QT)(),i=e=>{let{iconUrl:t,text:_}=e;return(0,n.jsxs)("div",{className:a().contacts__info__item,children:[(0,n.jsx)(s.default,{src:t,width:18,height:20,alt:"icon"}),(0,n.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:4},children:_.split("/n").map((e,t)=>(0,n.jsx)("div",{children:e},t))})]})};return(0,n.jsxs)("div",{className:"".concat(a().contacts," ").concat(t?a()["contacts--page"]:""),children:[(0,n.jsxs)("div",{className:a().contacts__info,children:[(0,n.jsx)("div",{className:a().contacts__info__title,children:_("contactUs")}),(0,n.jsxs)("div",{className:a().contacts__info__items,children:[(0,n.jsx)(i,{iconUrl:"/contact-position.svg",text:_("cylanAddress")}),(0,n.jsx)(i,{iconUrl:"/contact-email.svg",text:"<EMAIL>"}),(0,n.jsx)(i,{iconUrl:"/contact-phone.svg",text:"+86-0755-83073491"})]})]}),(0,n.jsx)("div",{className:a().contacts__address,children:(0,n.jsx)(o.default,{href:"https://map.baidu.com/poi/%E5%90%88%E6%88%90%E5%8F%B7%E6%B7%B1%E5%9C%B3%E6%B0%91%E4%BF%97%E6%96%87%E5%8C%96%E4%BA%A7%E4%B8%9A%E5%9B%AD/@12682764.738888016,2566016.**********,14z?uid=eb63e5cd850d1ef4a3acc4a1&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo",children:(0,n.jsx)(s.default,{src:"/address-image.webp",width:625,height:249,alt:"address"})})})]})}},5421:function(e,t,_){"use strict";_.d(t,{QT:function(){return n},Zt:function(){return s},e4:function(){return a},eV:function(){return o}});let{useI18n:n,useScopedI18n:i,I18nProviderClient:a,useCurrentLocale:o,useChangeLocale:s}=(0,_(8333).createI18nClient)({en:()=>_.e(673).then(_.bind(_,6673)),zh:()=>_.e(105).then(_.bind(_,6105))})},8333:function(e,t,_){"use strict";var n,i,a=Object.create,o=Object.defineProperty,s=Object.getOwnPropertyDescriptor,r=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,l=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable,m=(e,t,_)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:_}):e[t]=_,h=(e,t)=>{for(var _ in t||(t={}))d.call(t,_)&&m(e,_,t[_]);if(c)for(var _ of c(t))u.call(t,_)&&m(e,_,t[_]);return e},p=(e,t,_,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of r(t))d.call(e,i)||i===_||o(e,i,{get:()=>t[i],enumerable:!(n=s(t,i))||n.enumerable});return e},b={};((e,t)=>{for(var _ in t)o(e,_,{get:t[_],enumerable:!0})})(b,{createI18nClient:()=>O}),e.exports=p(o({},"__esModule",{value:!0}),b),_(2219);var g=_(7907),f=(i=null!=(n=_(2265))?a(l(n)):{},p(n&&n.__esModule?i:o(i,"default",{value:n,enumerable:!0}),n)),x=(e,t="")=>Object.entries(e).reduce((e,[_,n])=>h(h({},e),"string"==typeof n?{[t+_]:n}:x(n,`${t}${_}.`)),{}),j=e=>null,v=e=>null,w=new Map,y=_(2265),N=_(2265),S=_(2265);function C(e,t){let{localeContent:_,fallbackLocale:n}=e,i=n&&"string"==typeof _?n:Object.assign(null!=n?n:{},_),a=new Set(Object.keys(i).filter(e=>e.includes("#")).map(e=>e.split("#",1)[0])),o=new Intl.PluralRules(e.locale);return function(e,..._){var n,s,r;let c=_[0],l=!1;c&&"count"in c&&(t?a.has(`${t}.${e}`):a.has(e))&&(e=`${e}#${0===(r=c.count)?"zero":o.select(r)}`,l=!0);let d=t?i[`${t}.${e}`]:i[e];if(!d&&l){let t=e.split("#",1)[0];d=null==(n=i[`${t}#other`]||e)?void 0:n.toString()}else d=null==(s=d||e)?void 0:s.toString();if(!c)return d;let u=!0,m=null==d?void 0:d.split(/({[^}]*})/).map((e,t)=>{let _=e.match(/{(.*)}/);if(_){let e=_[1],n=c[e];return(0,S.isValidElement)(n)?(u=!1,(0,S.cloneElement)(n,{key:`${String(e)}-${t}`})):n}return e});return u?null==m?void 0:m.join(""):m}}var k=_(2265),P=_(7907),T=_(7907),E=_(2265);function O(e,t={}){let _=Object.keys(e),n=(0,y.createContext)(null),i=function(){var e;let n=(0,T.useParams)()[null!=(e=t.segmentName)?e:"locale"];return(0,E.useMemo)(()=>{for(let e of _)if(n===e)return e;v(`Locale "${n}" not found in locales (${_.join(", ")}), returning "notFound()"`),(0,T.notFound)()},[n])};return{useI18n:function(){let e=(0,N.useContext)(n);if(!e)throw Error("`useI18n` must be used inside `I18nProvider`");return(0,N.useMemo)(()=>C(e,void 0),[e])},useScopedI18n:function(e){let t=(0,k.useContext)(n);if(!t)throw Error("`useI18n` must be used inside `I18nProvider`");return(0,k.useMemo)(()=>C(t,e),[t,e])},I18nProviderClient:function(e,t,_){function n({locale:t,importLocale:n,children:i}){var a;let o=null!=(a=w.get(t))?a:(0,f.use)(n).default;w.has(t)||w.set(t,o);let s=(0,f.useMemo)(()=>({localeContent:x(o),fallbackLocale:_?x(_):void 0,locale:t}),[o,t]);return f.default.createElement(e.Provider,{value:s},i)}return function({locale:e,fallback:_,children:i}){let a=t[e];return a||(v(`The locale '${e}' is not supported. Defined locales are: [${Object.keys(t).join(", ")}].`),(0,g.notFound)()),f.default.createElement(f.Suspense,{fallback:_},f.default.createElement(n,{locale:e,importLocale:a()},i))}}(n,e,t.fallbackLocale),I18nClientContext:n,useChangeLocale:function(_){let{push:n,refresh:a}=(0,P.useRouter)(),o=i(),s=(0,P.usePathname)(),r=(null==_?void 0:_.preserveSearchParams)?(0,P.useSearchParams)().toString():void 0,c=r?`?${r}`:"",l=s;return t.basePath&&(l=l.replace(t.basePath,"")),l.startsWith(`/${o}/`)?l=l.replace(`/${o}/`,"/"):l===`/${o}`&&(l="/"),function(t){if(t===o)return;let _=e[t];if(!_){j(`The locale '${t}' is not supported. Defined locales are: [${Object.keys(e).join(", ")}].`);return}_().then(e=>{w.set(t,e.default),n(`/${t}${l}${c}`),a()})}},defineLocale:function(e){return e},useCurrentLocale:i}}},703:function(e,t,_){"use strict";_.d(t,{default:function(){return i.a}});var n=_(7447),i=_.n(n)},8792:function(e,t,_){"use strict";_.d(t,{default:function(){return i.a}});var n=_(5250),i=_.n(n)},7907:function(e,t,_){"use strict";_.r(t);var n=_(5313),i={};for(var a in n)"default"!==a&&(i[a]=(function(e){return n[e]}).bind(0,a));_.d(t,i)},2219:function(){},7447:function(e,t,_){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var _ in t)Object.defineProperty(e,_,{enumerable:!0,get:t[_]})}(t,{getImageProps:function(){return s},default:function(){return r}});let n=_(6921),i=_(8630),a=_(1749),o=n._(_(536)),s=e=>{let{props:t}=(0,i.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,_]of Object.entries(t))void 0===_&&delete t[e];return{props:t}},r=a.Image},2135:function(){},8291:function(e){e.exports={about:"about_about__wvePq",about__image:"about_about__image__KxBNO",about__content:"about_about__content__LS1_A",about__content__main__quotes:"about_about__content__main__quotes__gj59l",about__content__main__quotes__item:"about_about__content__main__quotes__item__fN1Rr",about__content__career__companytime:"about_about__content__career__companytime__HUZ14",about__content__career__timeline:"about_about__content__career__timeline__hCaJK",about__content__career__timeline__item:"about_about__content__career__timeline__item__r5rr0","about__content__career__timeline__item--reverse":"about_about__content__career__timeline__item--reverse__s8Qhz",about__content__prides:"about_about__content__prides__0DhgW",about__content__prides__list:"about_about__content__prides__list__LSiiG",about__content__prides__list__item:"about_about__content__prides__list__item__BPyny","about__content__prides__list__item--normal":"about_about__content__prides__list__item--normal__tpjHv","about__content__prides__list__item--large":"about_about__content__prides__list__item--large__VfdUa","about__content__prides__list__item--small":"about_about__content__prides__list__item--small__fRQpL",about__content__prides__list__item__cover:"about_about__content__prides__list__item__cover__GN83j",about__content__contacts:"about_about__content__contacts__S9y1Y"}},1809:function(e){e.exports={"nav--scrolled":"home_nav--scrolled__f5oaX",nav:"home_nav__gr65i",nav__placeholder:"home_nav__placeholder__R_bDj",nav__content:"home_nav__content__gXoig",nav__list:"home_nav__list__dmRBz",nav__list__item:"home_nav__list__item__Ti9E4","nav__list__item--link":"home_nav__list__item--link__dx88I","nav__list__item--active":"home_nav__list__item--active__oPRJX",nav__right:"home_nav__right__4GRXj",nav__right__language:"home_nav__right__language__YzU4O",nav__right__language__text:"home_nav__right__language__text__yUNmB","nav__right__language__text--active":"home_nav__right__language__text--active__e4h1y",nav__right__search:"home_nav__right__search__QAvd_",nav__right__menu:"home_nav__right__menu__tMG4s",nav__drop:"home_nav__drop__RNd3y",nav__mask:"home_nav__mask__YVj5E","banner-slider":"home_banner-slider__UBj9I","banner-slider__swiper":"home_banner-slider__swiper__9Bl8q","banner-slider__slide":"home_banner-slider__slide__2U7Uu","banner-slider__button":"home_banner-slider__button__GKjGy","swiper-button-disabled":"home_swiper-button-disabled__siaDk","banner-slider__button-prev":"home_banner-slider__button-prev__VeikB","banner-slider__button-next":"home_banner-slider__button-next__UC5d2","banner-slider__pagination":"home_banner-slider__pagination__J58et","banner-slider__bullet":"home_banner-slider__bullet__c3a9X","banner-slider__bullet--active":"home_banner-slider__bullet--active__5BpSZ","banner-slider__switcher":"home_banner-slider__switcher__SoaxS","banner-slider__switcher--right":"home_banner-slider__switcher--right___84yN","banner-slider__indicator":"home_banner-slider__indicator__0OOU4","banner-slider__indicator__item":"home_banner-slider__indicator__item__f8vBh","hot-spot":"home_hot-spot__HmXBc","hot-spot__captain":"home_hot-spot__captain__P7sAg","hot-spot__captain__more":"home_hot-spot__captain__more__hoe30","hot-spot__news":"home_hot-spot__news__mFPbX","hot-spot__news__left":"home_hot-spot__news__left__bYNbF","hot-spot__news__right":"home_hot-spot__news__right__IYxxG","hot-spot__news__item":"home_hot-spot__news__item__i6svw","hot-spot__news__item__info":"home_hot-spot__news__item__info__GSDkz","hot-spot__news__item__image":"home_hot-spot__news__item__image__0Dj0A","hot-spot__news__item__image--right":"home_hot-spot__news__item__image--right__scey9","hot-spot__news__item--left":"home_hot-spot__news__item--left__W7YL9",about:"home_about__vPbFi",about__cover:"home_about__cover__SPvuD",about__content:"home_about__content__EA9EW",about__content__time:"home_about__content__time__HcHq6",about__content__time__item:"home_about__content__time__item__n4W8C","about__content__time--page":"home_about__content__time--page__Azkeq",about__content__prides:"home_about__content__prides__zHCpT",contacts:"home_contacts__TRH4N","contacts--page":"home_contacts--page__0BV0w",contacts__info:"home_contacts__info__pIGy0",contacts__info__items:"home_contacts__info__items__qUSi9",contacts__info__title:"home_contacts__info__title__3_UHT",contacts__info__item:"home_contacts__info__item__eDIm0",contacts__address:"home_contacts__address___ZQdr",footer:"home_footer__qefFZ",footer__logo:"home_footer__logo__jG71u",footer__links:"home_footer__links__q5uiZ",footer__links__item:"home_footer__links__item__gB0TO",footer__links__follow:"home_footer__links__follow__jv8nP",footer__links__follow__weixin:"home_footer__links__follow__weixin__yeCNp",footer__copyright:"home_footer__copyright__M6lua",footer__copyright__link:"home_footer__copyright__link__PBT0B","banner-slider__indicator__item--active":"home_banner-slider__indicator__item--active__Pkcak"}}},function(e){e.O(0,[647,912,971,69,744],function(){return e(e.s=6463)}),_N_E=e.O()}]);