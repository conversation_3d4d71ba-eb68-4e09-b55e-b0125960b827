'use client'
import styles from './home.module.scss'
import Link from 'next/link'
import Image from 'next/image'
import Flex4ItemsBox, {
  itemsMode,
  Flex4ItemsInfo,
} from '../components/flex-4items-box'
import CylanCertificates from '../components/certificates'
import { useI18n, useCurrentLocale, I18nProviderClient } from '@/locales/client'

export default function ABoutLayout() {
  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <About></About>
    </I18nProviderClient>
  )
}

function About() {
  const t = useI18n()

  return (
    <div className={styles.about}>
      <div className={styles.about__cover}></div>
      <div className={styles.about__content}>
        <h3>{t('aboutUs')}</h3>
        <CompanyTime />
        <Prides />
        <Contacts />
      </div>
    </div>
  )
}

export function CompanyTime({
  isAboutPage = false,
}: {
  isAboutPage?: boolean
}) {
  const t = useI18n()
  const locale = useCurrentLocale()

  const TimeItem = ({
    num,
    unit,
    text,
  }: {
    num: number
    unit: string
    text: string
  }) => (
    <div className={styles.about__content__time__item}>
      <div>
        <span>{num}</span>
        <span>{unit}</span>
      </div>
      <div>{text}</div>
    </div>
  )

  const Line = () => (
    <div
      style={{
        height: 46,
        width: 0,
        opacity: 0.1,
        border: `1px solid ${isAboutPage ? 'var(--gray)' : '#fff'}`,
      }}
    ></div>
  )

  return (
    <div
      className={`${styles.about__content__time} ${
        isAboutPage ? styles['about__content__time--page'] : ''
      }`}
    >
      <TimeItem num={2005} unit={t('year')} text={t('companiyTime')} />
      <Line />
      <TimeItem
        num={locale === 'zh' ? 45 : 450}
        unit={locale === 'zh' ? '万+' : 'K+'}
        text={t('deviceSells')}
      />
      <Line />
      <TimeItem
        num={locale === 'zh' ? 90 : 900}
        unit={locale === 'zh' ? '万+' : 'K+'}
        text={t('activeUsers')}
      />
    </div>
  )
}

function Prides() {
  const t = useI18n()

  const PridesInfo: {
    infos: Flex4ItemsInfo
    imageSize: {
      width: number
      height: number
    }
    imageBox: {
      width: number
      height: number
    }
    mode: itemsMode
  } = {
    infos: [
      {
        imageSrc: '/pride-image-6.jpg',
        title: t('pride6'),
      },
      {
        imageSrc: '/pride-image-9.jpg',
        title: t('pride9'),
      },
      {
        imageSrc: '/pride-image-5.jpg',
        title: t('pride5'),
      },
      {
        imageSrc: '/pride-image-2.jpg',
        title: t('pride2'),
      },
    ],
    imageSize: {
      width: 300,
      height: 200,
    },
    imageBox: {
      width: 300,
      height: 200,
    },
    mode: itemsMode.pride,
  }

  return (
    <div className={styles.about__prides}>
      <div className="hide-on-small" style={{ marginTop: 30 }}>
        <Flex4ItemsBox {...PridesInfo} />
      </div>

      <CylanCertificates />
    </div>
  )
}

export function Contacts({ isAboutPage = false }) {
  const t = useI18n()

  const ContactItem = ({
    iconUrl,
    text,
  }: {
    iconUrl: string
    text: string
  }) => {
    return (
      <div className={styles.contacts__info__item}>
        <Image src={iconUrl} width={18} height={20} alt="icon"></Image>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          {text.split(`/n`).map((t, index) => (
            <div key={index}>{t}</div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div
      className={`${styles.contacts} ${
        isAboutPage ? styles['contacts--page'] : ''
      }`}
    >
      <div className={styles.contacts__info}>
        <div className={styles.contacts__info__title}>{t('contactUs')}</div>
        <div className={styles.contacts__info__items}>
          <ContactItem
            iconUrl="/contact-position.svg"
            text={t('cylanAddress')}
          />
          <ContactItem iconUrl="/contact-email.svg" text="<EMAIL>" />
          <ContactItem iconUrl="/contact-phone.svg" text="+86-0755-83073491" />
        </div>
      </div>
      <div className={styles.contacts__address}>
        <Link href="https://map.baidu.com/poi/%E5%90%88%E6%88%90%E5%8F%B7%E6%B7%B1%E5%9C%B3%E6%B0%91%E4%BF%97%E6%96%87%E5%8C%96%E4%BA%A7%E4%B8%9A%E5%9B%AD/@12682764.738888016,2566016.5718568345,14z?uid=eb63e5cd850d1ef4a3acc4a1&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo">
          <Image
            src={'/address-image.webp'}
            width={625}
            height={249}
            alt="address"
          ></Image>
        </Link>
      </div>
    </div>
  )
}
