(()=>{var e={};e.id=485,e.ids=[485],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},6032:(e,n,i)=>{"use strict";i.r(n),i.d(n,{GlobalError:()=>c.a,__next_app__:()=>p,originalPathname:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>o});var t=i(482),a=i(9108),s=i(2563),c=i.n(s),r=i(8300),l={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>r[e]);i.d(n,l);let o=["",{children:["[locale]",{children:["product",{children:["c31",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,8109)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\c31\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,3931)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(i.bind(i,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\product\\c31\\page.tsx"],h="/[locale]/product/c31/page",p={require:i,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/product/c31/page",pathname:"/[locale]/product/c31",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},8269:(e,n,i)=>{Promise.resolve().then(i.t.bind(i,1900,23)),Promise.resolve().then(i.t.bind(i,1476,23))},8109:(e,n,i)=>{"use strict";i.r(n),i.d(n,{default:()=>l,generateMetadata:()=>r});var t=i(5036);i(328);var a=i(2813),s=i(6274),c=i(6904);async function r({params:e,searchParams:n},i){return{title:"zh"===e.locale?"看家王双向通话智能摄像机C31":"IM CAM C31",description:"赛蓝科技 引领生活 看家王双向通话智能摄像机C31 看家王带屏摄像机",icons:{icon:"/favicon.ico"}}}async function l({params:e,searchParams:n}){await (0,c.nI)();let i=await (0,c.BH)(),r=({src:e})=>t.jsx("div",{className:"c31__banner__cover",children:t.jsx(a.default,{width:1920,height:664,alt:"",src:e,style:{height:"100%",width:"100%"},unoptimized:!0})});return t.jsx(t.Fragment,{children:t.jsx("div",{className:"c31-outter",children:(0,t.jsxs)("div",{className:"c31",children:[(0,t.jsxs)("div",{className:"c31__banner1 c31__banner",children:[t.jsx(r,{src:"/c31/pic-01bg.jpg"}),(0,t.jsxs)("div",{className:"c31__banner__main",children:[t.jsx("div",{className:"c31__banner1__product",children:t.jsx(a.default,{src:"/c31/pic-01-ys.png",width:445,height:664,alt:"",unoptimized:!0})}),(0,t.jsxs)("div",{className:"c31__banner1__description",style:"en"===i?{marginLeft:20}:{marginLeft:100},children:[t.jsx("div",{className:"c31__banner1__icon",children:t.jsx(a.default,{src:"/c31/imcam-icon.png",width:74,height:74,alt:"",unoptimized:!0})}),"en"===i?t.jsx("p",{children:"Communicate and Protect Anytime, Anywhere!"}):t.jsx("p",{children:"随时随地沟通和守护"}),t.jsx("span",{children:"zh"===i?"看家王C31摄像机":"IM CAM C31"}),(0,t.jsxs)("div",{className:"c31__banner1__qrcode",children:[t.jsx(s.default,{href:"/support/download_client",children:t.jsx("button",{children:"zh"===i?"下载APP":"Download App"})}),t.jsx("div",{className:"c31__banner1",children:t.jsx(a.default,{src:"/c31/qrcode.png",width:156,height:166,alt:""})})]})]})]})]}),(0,t.jsxs)("div",{className:"c31__banner2 c31__banner",children:[t.jsx(r,{src:"/c31/pic-bg02-4.jpg"}),(0,t.jsxs)("div",{className:"c31__banner__main",children:[(0,t.jsxs)("div",{className:"c31__banner__content",children:[t.jsx("h2",{children:"zh"===i?"安防监控，报警推送":"Security Monitoring，Alarm Push"}),t.jsx("p",{children:"zh"===i?"智能人形检测，高清红外夜视":"Humanoid Detection, High Definition Night Vision."}),(0,t.jsxs)("div",{children:[t.jsx(a.default,{src:"zh"===i?"/c31/banner2-icon1.png":"/c31/banner2-en-icon1.png",width:62,height:62,alt:"",unoptimized:!0}),t.jsx(a.default,{src:"zh"===i?"/c31/banner2-icon2.png":"/c31/banner2-en-icon2.png",width:62,height:62,alt:"",unoptimized:!0}),t.jsx(a.default,{src:"zh"===i?"/c31/banner2-icon3.png":"/c31/banner2-en-icon3.png",width:62,height:62,alt:"",unoptimized:!0})]})]}),t.jsx("div",{className:"c31__banner__image",children:t.jsx(a.default,{src:"zh"===i?"/c31/pic-02-ys-cn.png":"/c31/pic_02_en.png",width:945,height:484,alt:"",unoptimized:!0})})]})]}),(0,t.jsxs)("div",{className:"c31__banner3 c31__banner",children:[t.jsx(r,{src:"/c31/pic-01bg.jpg"}),(0,t.jsxs)("div",{className:"c31__banner__main",children:[(0,t.jsxs)("div",{className:"c31__banner__content",children:[t.jsx("h2",{children:"zh"===i?"一键呼叫，双向视频通话":"One-Touch Call, Two-Way Video Calling"}),(0,t.jsxs)("p",{children:["zh"===i?"1. 设备呼叫设备，设备呼叫手机，一键轻松操作。":"1. Device-to-Device Calling, Device-to-Mobile Calling, Easy One-Click Operation.",t.jsx("br",{}),"zh"===i?"2. 自定义设置呼叫按钮，随心指定呼叫爸爸或妈妈。":"2. Customizable Call Buttons, Personalize Calling for Dad or Mom"]}),(0,t.jsxs)("div",{children:[t.jsx(a.default,{src:"zh"===i?"/c31/banner3-icon1.png":"/c31/banner3-en-icon1.png",width:62,height:62,alt:"",unoptimized:!0}),t.jsx(a.default,{src:"zh"===i?"/c31/banner3-icon2.png":"/c31/banner3-en-icon2.png",width:62,height:62,alt:"",unoptimized:!0}),t.jsx(a.default,{src:"zh"===i?"/c31/banner3-icon3.png":"/c31/banner3-en-icon3.png",width:62,height:62,alt:"",unoptimized:!0})]})]}),t.jsx("div",{className:"c31__banner__image",children:t.jsx(a.default,{src:"/c31/pic_03_ys.png",width:1080,height:480,alt:"",unoptimized:!0})})]})]}),(0,t.jsxs)("div",{className:"c31__banner4 c31__banner",children:[t.jsx(r,{src:"/c31/pic-bg02-4.jpg"}),(0,t.jsxs)("div",{className:"c31__banner__main",children:[(0,t.jsxs)("div",{className:"c31__banner__content",children:[t.jsx("h2",{children:"zh"===i?"视频通话 ，移动跟随":"Video Calling with Move Following"}),(0,t.jsxs)("div",{children:[t.jsx(a.default,{src:"/c31/banner4-icon1.png",width:62,height:62,alt:"",unoptimized:!0}),t.jsx(a.default,{src:"zh"===i?"/c31/banner4-icon2.png":"/c31/banner4-en-icon2.png",width:62,height:62,alt:"",unoptimized:!0}),t.jsx(a.default,{src:"zh"===i?"/c31/banner4-icon3.png":"/c31/banner4-en-icon3.png",width:62,height:62,alt:"",unoptimized:!0})]})]}),t.jsx("div",{className:"c31__banner__image",children:t.jsx(a.default,{src:"zh"===i?"/c31/pic-04-ys-cn.png":"/c31/pic-04-ys-en.png",width:1165,height:514,alt:"",unoptimized:!0})})]})]})]})})})}},3931:(e,n,i)=>{"use strict";i.r(n),i.d(n,{default:()=>a});var t=i(5036);function a({children:e}){return t.jsx("div",{children:e})}},328:()=>{}};var n=require("../../../../webpack-runtime.js");n.C(e);var i=e=>n(n.s=e),t=n.X(0,[638,47,563,613],()=>i(6032));module.exports=t})();