(()=>{var e={};e.id=862,e.ids=[862],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},8125:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>_});var n=a(482),i=a(9108),s=a(2563),r=a.n(s),p=a(8300),o={};for(let e in p)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>p[e]);a.d(t,o);let _=["",{children:["[locale]",{children:["support",{children:["[type]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,3002)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\[type]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,5492)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\[type]\\page.tsx"],c="/[locale]/support/[type]/page",d={require:a,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/support/[type]/page",pathname:"/[locale]/support/[type]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:_}})},8297:(e,t,a)=>{Promise.resolve().then(a.bind(a,355)),Promise.resolve().then(a.bind(a,3338)),Promise.resolve().then(a.t.bind(a,1900,23)),Promise.resolve().then(a.t.bind(a,1476,23))},355:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>_});var n=a(5344);a(4507);var i=a(9410);function s({src:e="/search-banner.webp",mobileSrc:t="/search-banner.webp"}){return(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"banner hide-on-small",children:n.jsx(i.default,{src:e,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})}),n.jsx("div",{className:"banner hide-on-medium hide-on-large",children:n.jsx(i.default,{src:t,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})})]})}var r=a(3729),p=a.n(r),o=a(6506);function _({title:e="",iconSrc:t="",tabs:a=[],currentTab:r,bannerSrc:_="/pic_shipinbg@2x (1).webp",bannerMobileSrc:c="/pic_shipinbg@2x (1).webp",background:d="rgb(179, 220, 252)",onTabChange:u=()=>{},showBanner:m=!0,isSearch:h=!1,isLink:g=!1}){let x=e=>{u(e)};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"page-tabs",style:d?{background:d}:{},children:[m&&n.jsx(s,{src:_,mobileSrc:c}),n.jsx(()=>n.jsx("div",{className:"page-tabs__content",children:(0,n.jsxs)("div",{className:"page-tabs__content__title",children:[n.jsx(i.default,{src:t,width:34,height:34,alt:""}),n.jsx("h1",{children:e})]})}),{}),!h&&n.jsx(({isLink:e=!1})=>n.jsx("div",{className:"page-tabs__content__items hide-on-small",children:a.map((t,a)=>n.jsx(p().Fragment,{children:e?n.jsx(o.default,{href:`${t.id}`,children:n.jsx("button",{className:`page-tabs__content__items__item ${r===t.id?"page-tabs__content__items__item--active":""}`,onClick:()=>x(t.id),children:t.text})}):n.jsx("button",{className:`page-tabs__content__items__item ${r===t.id?"page-tabs__content__items__item--active":""}`,onClick:()=>x(t.id),children:t.text})},a))}),{isLink:g})]}),n.jsx(l,{onTabChange:u,tabs:a,currentTab:r,isLink:g})]})}function l({tabs:e,currentTab:t,onTabChange:a,isLink:i}){let s=({id:e,text:i})=>n.jsx("div",{className:`${t===e?"page-tabs__tabs-small__tab--active":""} page-tabs__tabs-small__tab`,onClick:()=>{a(e)},children:i});return n.jsx("div",{className:"page-tabs__tabs-small hide-on-medium hide-on-large",children:e.map((e,t)=>n.jsx(p().Fragment,{children:i?n.jsx(o.default,{href:e.id,children:n.jsx(s,{...e})}):n.jsx(s,{...e})},t))})}},9156:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var n=a(5344);function i({currentPage:e=1,pageSize:t=8,count:a=0,onChange:i=()=>{}}){var s;let r;(s=r||(r={}))[s.first=0]="first",s[s.last=1]="last";let p=Math.ceil(a/t),o=({type:t=0})=>n.jsx("button",{onClick:()=>{1===e&&0===t||e===p&&1===t||i(0===t?1:p)},className:"pagination__pagejumper",disabled:0===t&&1===e||1===t&&e===p,children:0===t?"首页":"尾页"});return n.jsx(n.Fragment,{children:a>8&&(0,n.jsxs)("div",{className:"pagination hide-on-small",children:[n.jsx(o,{type:0}),Array(p).fill(0).map((t,a)=>n.jsx("button",{onClick:()=>{e!==a+1&&i(a+1)},className:`pagination__page ${e===a+1?"pagination__page--active":""}`,children:a+1},a)),p>5&&n.jsx("button",{className:"pagination__page",children:"..."}),n.jsx(o,{type:1})]})})}},3338:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var n=a(5344),i=a(4103),s=a.n(i),r=a(9410),p=a(3729),o=a.n(p);async function _({infos:e,isProductDetail:t=!1}){let a=({imageSrc:e,title:t,description:a,time:i,link:s="/"})=>(0,n.jsxs)("a",{href:s,className:"flex-box-with-2items__item",children:[n.jsx("div",{children:n.jsx(r.default,{src:e,width:240,height:160,style:{width:"100%",height:"100%"},alt:""})}),(0,n.jsxs)("div",{children:[n.jsx("h6",{children:t}),n.jsx("p",{className:"hide-on-small",children:a}),n.jsx("div",{style:{flex:1}}),n.jsx("span",{children:i})]})]});return n.jsx("div",{className:`flex-box-with-2items ${t?"flex-box-with-2items--product-detail":""}`,children:e.map((e,t)=>n.jsx(o().Fragment,{children:n.jsx(a,{...e})},`${t}-${e.title}`))})}var l=a(9156),c=a(2326),d=a(8428),u=a(2716);function m({datas:e,groups:t}){let[a,i]=(0,p.useState)(1);return n.jsx(u.e4,{locale:(0,u.eV)(),children:(0,n.jsxs)("div",{className:s().help,children:[n.jsx(h,{groups:t}),n.jsx(g,{datas:e}),n.jsx(x,{count:e.length,currentPage:a})]})})}function h({groups:e}){let t=(0,u.eV)(),a=(0,d.usePathname)(),i=(0,d.useRouter)(),o=(0,u.QT)(),_=(0,d.useSearchParams)().get("nav"),l=e.map(e=>({id:e.id,text:"zh"===t?e.name:e.nameEn}));l.unshift({id:"0",text:o("all")});let[m,h]=(0,p.useState)(_||l[0].id),[g,x]=(0,p.useState)(!1),v=e=>{e&&(h(e),x(!1))},f=e=>{e&&(h(e),i.replace(`${a}?nav=${e}`))},y=({text:e,id:t})=>n.jsx("button",{className:`${s().help__nav__item} ${m===t?s()["help__nav__item--active"]:""}`,onClick:()=>{f(t)},children:e});return(0,n.jsxs)("div",{style:{display:"flex"},children:[n.jsx("div",{className:`${s().help__nav} hide-on-small`,children:l.map((e,t)=>n.jsx(y,{...e},t))}),n.jsx("div",{className:`${s().manuals__captain__menu} hide-on-medium hide-on-large`,children:n.jsx(c.Z,{show:g,list:l,onClick:e=>{v(e)},onClickMask:()=>{x(!1)},children:(0,n.jsxs)("div",{onClick:()=>{x(!g)},className:s().manuals__captain__menu__item,children:[l.find(e=>e.id===m)?.text,n.jsx(r.default,{src:"/arrow-down.svg",width:16,height:10,alt:""})]})})})]})}function g({datas:e}){let t=(0,u.eV)(),a=e.map(e=>{let a=e.content;"en"===t&&e.contentEn&&(a=e.contentEn);let n=`${a[0]?.text}${a[1]?.text}`;return{imageSrc:e.coverSrc,title:"zh"===t?e.title:e.titleEn?e.titleEn:e.title,description:n,time:function(e,t){let a=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0"),s=String(e.getHours()).padStart(2,"0"),r=String(e.getMinutes()).padStart(2,"0"),p=String(e.getSeconds()).padStart(2,"0");return t.replace(/YYYY/g,String(a)).replace(/MM/g,n).replace(/DD/g,i).replace(/HH/g,s).replace(/mm/g,r).replace(/ss/g,p)}(e.time,"YYYY-MM-DD"),link:`/article/${e.id}`}});return n.jsx("div",{className:s().help__list,children:n.jsx(_,{infos:a})})}function x({count:e,currentPage:t}){return n.jsx("div",{className:s().help__pagination,children:n.jsx(l.default,{count:e,currentPage:t})})}},7703:e=>{e.exports={downloads:"support_downloads___WU_3",downloads__content:"support_downloads__content__adctV",downloads__content__card:"support_downloads__content__card__8k3mb",manuals:"support_manuals__NEn7J",manuals__captain:"support_manuals__captain__cEP0z",manuals__captain__navs__button:"support_manuals__captain__navs__button__g3bbO","manuals__captain__navs__button--active":"support_manuals__captain__navs__button--active__vPDNu",manuals__captain__languages:"support_manuals__captain__languages__VlDtT",manuals__captain__languages__item:"support_manuals__captain__languages__item__YhoHX","manuals__captain__languages__item--active":"support_manuals__captain__languages__item--active__L9vmB",manuals__list:"support_manuals__list__UUsyR",manuals__list__item:"support_manuals__list__item__SGCXz",manuals__list__item__image:"support_manuals__list__item__image__w84lm",manuals__pagination:"support_manuals__pagination__mn4kp",help:"support_help__JADd0",help__nav:"support_help__nav__oL9P3",help__nav__item:"support_help__nav__item__RRqPO","help__nav__item--active":"support_help__nav__item--active__TqKwA",help__list:"support_help__list__7uY42",help__pagination:"support_help__pagination__50GG4",manuals__captain__menu__item:"support_manuals__captain__menu__item___M8XD"}},4103:e=>{e.exports={downloads:"support_downloads___WU_3",downloads__content:"support_downloads__content__adctV",downloads__content__card:"support_downloads__content__card__8k3mb",manuals:"support_manuals__NEn7J",manuals__captain:"support_manuals__captain__cEP0z",manuals__captain__navs__button:"support_manuals__captain__navs__button__g3bbO","manuals__captain__navs__button--active":"support_manuals__captain__navs__button--active__vPDNu",manuals__captain__languages:"support_manuals__captain__languages__VlDtT",manuals__captain__languages__item:"support_manuals__captain__languages__item__YhoHX","manuals__captain__languages__item--active":"support_manuals__captain__languages__item--active__L9vmB",manuals__list:"support_manuals__list__UUsyR",manuals__list__item:"support_manuals__list__item__SGCXz",manuals__list__item__image:"support_manuals__list__item__image__w84lm",manuals__pagination:"support_manuals__pagination__mn4kp",help:"support_help__JADd0",help__nav:"support_help__nav__oL9P3",help__nav__item:"support_help__nav__item__RRqPO","help__nav__item--active":"support_help__nav__item--active__TqKwA",help__list:"support_help__list__7uY42",help__pagination:"support_help__pagination__50GG4",manuals__captain__menu__item:"support_manuals__captain__menu__item___M8XD"}},3002:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var n,i=a(5036),s=a(7703),r=a.n(s),p=a(964),o=a(2813),_=a(6274),l=a(6904);async function c(){let e=await (0,l.nI)(),t=await (0,l.BH)();return(0,i.jsxs)("div",{className:r().downloads,children:[i.jsx("h2",{children:e("imcamApp")}),i.jsx("p",{children:e("imcamAppTip")}),(0,i.jsxs)("div",{className:r().downloads__content,children:[i.jsx(d,{type:"Android",version:"1.2.333"}),i.jsx(d,{type:"iOS",version:"1.2.333"}),"zh"===t&&i.jsx(d,{type:"小程序",version:"1.1.0"}),i.jsx(d,{type:"悦办Android",version:"1.2.333"}),i.jsx(d,{type:"悦办iOS",version:"1.2.333"})]})]})}async function d({type:e="Android",version:t=""}){let a=await (0,l.nI)();return(0,i.jsxs)("div",{className:r().downloads__content__card,children:[i.jsx("h5",{children:a("小程序"===e?"dangdang":"悦办Android"===e||"悦办iOS"===e?"imMate":"imcam")}),i.jsx("h5",{children:a("小程序"===e?"miniProgram":"Android"===e||"悦办Android"===e?"androidApp":"iosApp")}),i.jsx(o.default,{src:"小程序"===e?"/miniprogram-qrcode.jpg":"/support/download-code.webp",width:160,height:160,alt:"qrcode"}),("Android"===e||"悦办Android"===e)&&i.jsx(_.default,{href:"Android"===e?"https://cn-pub.oss-cn-hangzhou.aliyuncs.com/forever/app/android/IM_Cam.apk":"https://cn-pub.oss-cn-hangzhou.aliyuncs.com/forever/app/android/ImMate.apk",children:(0,i.jsxs)("button",{children:[i.jsx(o.default,{src:"/download-white.svg",width:20,height:20,alt:""}),a("download")]})}),("iOS"===e||"悦办iOS"===e)&&i.jsx(i.Fragment,{children:i.jsx(_.default,{href:"iOS"===e?"https://apps.apple.com/app/im-cam/id6447252954":"https://apps.apple.com/cn/app/%E6%82%A6%E5%8A%9E/id6743839622",children:i.jsx("button",{children:a("goToAppstore")})})})]})}!function(e){e.android="Android",e.ios="iOS",e.miniprogram="小程序",e.yuebanAndroid="悦办Android",e.yuebaniOS="悦办iOS"}(n||(n={}));let u=(0,a(6843).createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\support\help.tsx`),{__esModule:m,$$typeof:h}=u,g=u.default;var x=a(4850);async function v({params:e}){let t=await (0,l.nI)(),a=[{id:"download_client",text:t("downloadClient")},{id:"help",text:t("help")}],n=e.type;a.find(e=>e.id===n)||(n=a[0].id);let s=[],o=[];return n===a[1].id&&(s=await (0,x.Gv)(),o=await (0,x.X5)()),(0,i.jsxs)("div",{className:r().support,children:[i.jsx(p.ZP,{iconSrc:"/support-icon.svg",title:t("support"),currentTab:n,tabs:a,bannerSrc:"/support/banner-pc.jpg",bannerMobileSrc:"/support/banner-mobile.png",background:"rgb(11,106,151)",isLink:!0}),(0,i.jsxs)("div",{className:r().support__content,children:[n===a[0].id&&i.jsx(c,{}),n===a[1].id&&i.jsx(g,{datas:s,groups:o})]})]})}},5492:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s,generateMetadata:()=>i});var n=a(5036);async function i({params:e},t){return{title:"zh"===e.locale?"服务支持":"Support",description:"赛蓝科技 引领生活",icons:{icon:"/favicon.ico"}}}function s({children:e}){return n.jsx("div",{children:e})}},964:(e,t,a)=>{"use strict";a.d(t,{ZP:()=>r});let n=(0,a(6843).createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\components\page-tabs.tsx`),{__esModule:i,$$typeof:s}=n,r=n.default},4850:(e,t,a)=>{"use strict";a.d(t,{Gv:()=>r,IH:()=>p,Vp:()=>l,X5:()=>_,mV:()=>o});var n=a(5008);let i=[{type:n.Iq.support,title:"设备绑定失败怎么办",titleEn:"Device binding failed",tip:"",time:new Date("2024-04-25"),content:[{type:n.tv.paragraph,text:"设备绑定失败，可尝试以下操作："},{type:n.tv.paragraph,text:"1. 配置Wi-Fi密码时，选择可用网络并输入正确的Wi-Fi密码。"},{type:n.tv.paragraph,text:"2. 设备只支持2.4Ghz的网络。选择Wi-Fi时，请勿选择5Ghz的网络。"},{type:n.tv.paragraph,text:"3. 确认您所配置的Wi-Fi可以正常访问互联网。"}],contentEn:[{type:n.tv.paragraph,text:"Device binding failed. Please try the following:"},{type:n.tv.paragraph,text:"1. When configuring the Wi-Fi password, select an available network and enter the correct Wi-Fi password."},{type:n.tv.paragraph,text:"2. The device only supports 2.4GHz networks. When selecting Wi-Fi, make sure not to choose a 5GHz network."},{type:n.tv.paragraph,text:"3. Verify that the Wi-Fi you configured has internet access."}],id:"help__01",coverSrc:"/support/help/help__01.jpg",groupId:"01",groupName:"App使用帮助",groupNameEn:"App FAQ",categoryName:"使用帮助",categoryNameEn:"Help"},{type:n.Iq.support,title:"蓝牙搜索不到设备怎么办",titleEn:"Bluetooth device not found",tip:"",time:new Date("2024-04-25"),content:[{type:n.tv.paragraph,text:"1. 请确保手机已经打开蓝牙。"},{type:n.tv.paragraph,text:"2. 请确保设备画面处于待配网状态。如下图所示。"},{type:n.tv.image,src:"/support/help/help__02__image1.png"}],contentEn:[{type:n.tv.paragraph,text:"1. Please ensure that your phone's Bluetooth is turned on."},{type:n.tv.paragraph,text:"2. Make sure that the device screen is in pairing mode. Refer to the illustration below."},{type:n.tv.image,src:"/support/help/help__02__image1.png"}],id:"help__02",coverSrc:"/support/help/help__02.jpg",groupId:"01",groupName:"App使用帮助",groupNameEn:"App FAQ",categoryName:"使用帮助",categoryNameEn:"Help"},{type:n.Iq.support,title:"设备告警消息没有推送怎么办",titleEn:"Device alarm messages are not being pushed",tip:"",time:new Date("2024-04-25"),content:[{type:n.tv.paragraph,text:"1.如果您想收到移动报警消息，确认APP内设备【设置】-【侦测报警设置】-【移动侦测开关】是否开启，开启了才会推给您移动报警消息。"},{type:n.tv.paragraph,text:"2.如果已授权APP消息通知权限，确认APP内【消息】-右上角【设置】是否开启了【设备告警通知】，如果关闭了设备告警通知则不会推送消息。"},{type:n.tv.paragraph,text:"3.如果您是安卓手机，建议您到APP【我的】-【视频来电设置】中查看是否授权APP有通知权限。如果您是IOS手机，建议您授权APP【通知管理】权限。"}],contentEn:[{type:n.tv.paragraph,text:"1.If you want to receive motion alarm messages, ensure in the app that the device [Settings] - [Motion Detection Settings] - [Motion Detection Switch] is enabled. Motion alarm messages are only pushed when this setting is turned on."},{type:n.tv.paragraph,text:"2.If you have granted app notification permissions, verify in the app's [Messages] - [Settings] (top right corner) that [Device Alarm Notifications] are enabled. If disabled, alarm messages won't be pushed."},{type:n.tv.paragraph,text:"3.If you are using an Android phone, check if you have granted notification permissions in [My] - [Video Call Settings] within the app. If you are using an iOS phone, ensure the app has notification management permissions."}],id:"help__03",coverSrc:"/support/help/help__03.jpg",groupId:"01",groupName:"App使用帮助",groupNameEn:"App FAQ",categoryName:"使用帮助",categoryNameEn:"Help"},{type:n.Iq.support,title:"设备掉线怎么办",titleEn:"Device disconnected",tip:"",time:new Date("2024-04-25"),content:[{type:n.tv.paragraph,text:"1. 请检查设备的wifi信号是否为较好。如果wifi信号较差，请将设备尽量放在距离路由器近的地方。"},{type:n.tv.image,src:"/support/help/help__04__image1.jpg"},{type:n.tv.paragraph,text:"2. 请检查设备所连接的wifi网络是否能够流畅上网。"},{type:n.tv.paragraph,text:"2. 设备只支持2.4Ghz的网络。选择Wi-Fi时，请勿选择5Ghz的网络。"}],contentEn:[{type:n.tv.paragraph,text:"1. Please check if the device has a strong Wi-Fi signal. If the Wi-Fi signal is weak, try placing the device closer to the router if possible."},{type:n.tv.image,src:"/support/help/help__04__image1.jpg"},{type:n.tv.paragraph,text:"2. Verify that the Wi-Fi network the device is connected to has a stable internet connection and can access the internet smoothly."},{type:n.tv.paragraph,text:"3. Check the power cord and adapter; if they don't match, frequent power shortages and disconnections may occur."}],id:"help__04",coverSrc:"/support/help/help__04.jpg",groupId:"01",groupName:"App使用帮助",groupNameEn:"App FAQ",categoryName:"使用帮助",categoryNameEn:"Help"}],s=[{id:"01",name:"App使用帮助",nameEn:"App FAQ",categoryName:"使用帮助",categoryNameEn:"Help"}];async function r(){return new Promise(e=>{e(i)})}async function p(e){return new Promise(t=>{t(i.find(t=>t.id===e))})}async function o(){return new Promise(e=>{e(i.filter((e,t)=>t<4))})}async function _(){return new Promise(e=>{e(s)})}async function l(e){return new Promise(t=>{let a=-1;i.find((t,n)=>{if(t.id===e)return a=n,!0});let n={prev:-1,prevLink:"",prevTitle:"",prevTitleEn:"",next:-1,nextLink:"",nextTitle:"",nextTitleEn:""};if(-1===a){t(n);return}i.find((e,t)=>{if(t===a-1)return n.prev=t,n.prevLink="/article/"+e.id,n.prevTitle=e.title,n.prevTitleEn=e.titleEn?e.titleEn:"",!0}),i.find((e,t)=>{if(t===a+1)return n.next=t,n.nextLink="/article/"+e.id,n.nextTitle=e.title,n.nextTitleEn=e.titleEn?e.titleEn:"",!0}),t(n)})}},5008:(e,t,a)=>{"use strict";var n,i,s;a.d(t,{Iq:()=>i,pJ:()=>s,tv:()=>n}),function(e){e[e.paragraph=0]="paragraph",e[e.title=1]="title",e[e.image=2]="image"}(n||(n={})),function(e){e.news="/news",e.support="/support",e.product="/product"}(i||(i={})),function(e){e[e.cylan=0]="cylan",e[e.industry=1]="industry"}(s||(s={}))}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[638,47,563,613],()=>a(8125));module.exports=n})();