import styles from './videos.module.scss'
import Flex4ItemsBox, {
  Flex4ItemsInfo,
  itemsMode,
} from '@/app/[locale]/ui/components/flex-4items-box'
import Pagination from '../ui/components/pagination'
import PageTabs from '../ui/components/page-tabs'

export default function Videos() {
  const tabs = [
    {
      id: '0',
      text: '全部',
    },
    {
      id: '1',
      text: '视频分类1',
    },
    {
      id: '2',
      text: '视频分类2',
    },
    {
      id: '3',
      text: '视频分类3',
    },
  ]
  return (
    <div className={styles.videos}>
      <PageTabs
        iconSrc="/video-icon.svg"
        title="视频中心"
        currentTab={tabs[0].id}
        tabs={tabs}
      />
      <List />
    </div>
  )
}

function List() {
  const VideosInfo: {
    infos: Flex4ItemsInfo
    imageSize: {
      width: number
      height: number
    }
    imageBox: {
      width: number
      height: number
    }
    mode: itemsMode
  } = {
    infos: [
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/videos-image-1.png',
        title: 'C31复位配网-中英文字幕版',
        tip: '2021-03-05',
      },
    ],
    imageSize: {
      width: 300,
      height: 168,
    },
    imageBox: {
      width: 300,
      height: 168,
    },
    mode: itemsMode.normal,
  }

  return (
    <div className={styles.videos__list}>
      <Flex4ItemsBox {...VideosInfo} />
      <ListPagination />
    </div>
  )
}

function ListPagination() {
  return (
    <div className={styles.videos__pagination}>
      <Pagination currentPage={1} count={37}></Pagination>
    </div>
  )
}
