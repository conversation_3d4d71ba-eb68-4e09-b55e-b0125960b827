'use client'
import ShowMore from './show-more'

export default function Pagination({
  currentPage = 1,
  pageSize = 8,
  count = 0,
  onChange = () => {},
}: {
  currentPage: number
  pageSize?: number
  count: number
  onChange?: Function
}) {
  enum JumperType {
    first = 0,
    last = 1,
  }

  const maxPage = Math.ceil(count / pageSize)

  const PageJumper = ({ type = JumperType.first }) => (
    <button
      onClick={() => {
        if (currentPage === 1 && type === JumperType.first) {
        } else if (currentPage === maxPage && type === JumperType.last) {
        } else {
          onChange(type === JumperType.first ? 1 : maxPage)
        }
      }}
      className={'pagination__pagejumper'}
      disabled={
        (type === JumperType.first && currentPage === 1) ||
        (type === JumperType.last && currentPage === maxPage)
      }
    >
      {type === JumperType.first ? '首页' : '尾页'}
    </button>
  )
  return (
    <>
      {count > 8 && (
        <div className={'pagination hide-on-small'}>
          <PageJumper type={JumperType.first} />
          {Array(maxPage)
            .fill(0)
            .map((item, index) => (
              <button
                key={index}
                onClick={() => {
                  if (currentPage !== index + 1) onChange(index + 1)
                }}
                className={`pagination__page ${
                  currentPage === index + 1 ? 'pagination__page--active' : ''
                }`}
              >
                {index + 1}
              </button>
            ))}
          {maxPage > 5 && (
            <button className={`pagination__page`}>{'...'}</button>
          )}
          <PageJumper type={JumperType.last} />
        </div>
      )}
    </>
  )
}
