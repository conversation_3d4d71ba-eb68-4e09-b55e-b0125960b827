'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import styles from './home.module.scss'
import Image from 'next/image'
import { useCurrentLocale } from '@/locales/client'

export default function BannerSlider() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null)

  const locale = useCurrentLocale()
  const images = [
    locale === 'zh' ? '/home/<USER>' : '/home/<USER>',
    locale === 'zh' ? '/home/<USER>' : '/home/<USER>',
  ]

  // 自动播放功能
  const startAutoPlay = useCallback(() => {
    if (images.length <= 1) return

    autoPlayRef.current = setInterval(() => {
      if (!isPaused) {
        setCurrentIndex(prev => (prev + 1) % images.length)
      }
    }, 5000) // 5秒切换一次
  }, [images.length, isPaused])

  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current)
      autoPlayRef.current = null
    }
  }, [])

  // 切换到指定索引
  const goToSlide = useCallback((index: number) => {
    if (index === currentIndex || isTransitioning) return
    setCurrentIndex(index)
  }, [currentIndex, isTransitioning])

  // 上一张
  const goToPrev = useCallback(() => {
    if (isTransitioning) return
    const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1
    goToSlide(newIndex)
  }, [currentIndex, images.length, isTransitioning, goToSlide])

  // 下一张
  const goToNext = useCallback(() => {
    if (isTransitioning) return
    const newIndex = (currentIndex + 1) % images.length
    goToSlide(newIndex)
  }, [currentIndex, images.length, isTransitioning, goToSlide])

  // 触摸事件处理
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null)
  const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(null)

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
    })
    setIsPaused(true) // 暂停自动播放
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
    })
  }

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const deltaX = touchEnd.x - touchStart.x
    const deltaY = touchEnd.y - touchStart.y
    const minSwipeDistance = 50

    // 判断是否为水平滑动
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
      if (deltaX > 0) {
        goToPrev()
      } else {
        goToNext()
      }
    }

    setTouchStart(null)
    setTouchEnd(null)
    setIsPaused(false) // 恢复自动播放
  }

  // 鼠标事件处理
  const handleMouseEnter = () => setIsPaused(true)
  const handleMouseLeave = () => setIsPaused(false)

  // 生命周期管理
  useEffect(() => {
    startAutoPlay()
    return () => stopAutoPlay()
  }, [startAutoPlay, stopAutoPlay])

  useEffect(() => {
    if (isPaused) {
      stopAutoPlay()
    } else {
      startAutoPlay()
    }
  }, [isPaused, startAutoPlay, stopAutoPlay])

  // 过渡动画控制
  useEffect(() => {
    setIsTransitioning(true)
    const timer = setTimeout(() => setIsTransitioning(false), 300)
    return () => clearTimeout(timer)
  }, [currentIndex])

  return (
    <div
      className={styles['banner-slider']}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        ref={containerRef}
        className={styles['banner-slider__list']}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{
          transform: `translateX(-${currentIndex * 100}%)`,
          transition: isTransitioning ? 'transform 0.3s ease-in-out' : 'none',
        }}
      >
        {images.map((item, index) => (
          <div key={index} className={styles['banner-slider__slide']}>
            <Image
              src={item}
              width={1920}
              height={500}
              alt={`Banner ${index + 1}`}
              unoptimized
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                objectPosition: 'center',
              }}
            />
          </div>
        ))}
      </div>

      {images.length > 1 && (
        <>
          {/* 左右翻页按钮 */}
          <Switcher
            position="left"
            disabled={currentIndex === 0}
            onClick={goToPrev}
          />
          <Switcher
            position="right"
            disabled={currentIndex === images.length - 1}
            onClick={goToNext}
          />

          {/* 翻页器 */}
          <Indicator
            count={images.length}
            currentIndex={currentIndex}
            onIndicatorClick={goToSlide}
          />
        </>
      )}
    </div>
  )
}

function Switcher({
  position = 'left',
  disabled = false,
  onClick,
}: {
  position: string
  disabled?: boolean
  onClick: () => void
}) {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${
        styles[`banner-slider__switcher${position === 'left' ? '' : '--right'}`]
      } ${disabled ? styles[`banner-slider__switcher--disabled`] : ''}`}
      aria-label={`${position === 'left' ? 'Previous' : 'Next'} slide`}
    >
      <Image
        src={`/slider-${position === 'left' ? 'left' : 'right'}.svg`}
        width={44}
        height={44}
        alt=""
      />
    </button>
  )
}

function Indicator({
  count,
  currentIndex,
  onIndicatorClick,
}: {
  count: number
  currentIndex: number
  onIndicatorClick: (index: number) => void
}) {
  return (
    <div className={styles[`banner-slider__indicator`]}>
      {Array(count)
        .fill('')
        .map((_, index) => (
          <button
            key={index}
            onClick={() => onIndicatorClick(index)}
            className={`${styles[`banner-slider__indicator__item`]} ${
              index === currentIndex
                ? styles[`banner-slider__indicator__item--active`]
                : ''
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
    </div>
  )
}
