'use client'
import Image from 'next/image'
import { useEffect, useState } from 'react'
import './components.scss'
import { useCurrentLocale, useI18n, I18nProviderClient } from '@/locales/client'

export default function BackToTopLayout() {
  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <BackToTop />
    </I18nProviderClient>
  )
}

function BackToTop() {
  const t = useI18n()
  const locale = useCurrentLocale()

  const [showBackTop, setShowBackTop] = useState(false)
  let scrollTimer: ReturnType<typeof setTimeout> | null = null
  let isScrolling = false

  useEffect(() => {
    const handleScroll = () => {
      const scrollHeight =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop
      setShowBackTop(scrollHeight > window.innerHeight)
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const scrollToTop = () => {
    if (isScrolling) return // 如果正在滚动,则不执行操作
    isScrolling = true // 设置滚动标记为 true

    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    scrollTimer = setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'smooth' })
      isScrolling = false // 滚动结束,将标记设置为 false
    }, 100)
  }

  return (
    <button
      className={`back-top ${showBackTop ? '' : 'back-top--hide'}`}
      onClick={scrollToTop}
    >
      <Image src={'/back-top.svg'} width={26} height={26} alt="Top"></Image>
      {locale === 'zh' && (
        <span className="hide-on-small">{t('backToTop')}</span>
      )}
    </button>
  )
}
