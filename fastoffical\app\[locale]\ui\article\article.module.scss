.article-content {
  padding: 30px;
  width: 880px;
  border-radius: 6px;
  background-color: #fff;
  h1 {
    font-size: 30px;
  }
  &__page {
    display: flex;
  }
  &__time {
    font-size: var(--font-small);
    color: var(--text-mark);
    margin-top: 20px;
  }
  &__line {
    border-bottom: 1px solid var(--gray-4);
    margin-top: 20px;
  }
  &__content {
    margin-top: 20px;
    p {
      font-size: 15px;
      line-height: 30px;
      margin: 20px auto;
    }
    &__image {
      width: 500px;
      height: 500px;
      margin: 20px auto;
    }
    h4 {
      margin: 20px auto;
      text-align: center;
    }
  }
  &__link {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 40px;
    &__item {
      width: 400px;
      height: 50px;
      line-height: 50px;
      padding: 0 10px;
      border-radius: 4px;
      background-color: #f7f7f7;
      display: flex;
      margin: auto;
      span:first-of-type {
        font-size: var(--font-small);
        color: var(--text-mark);
        margin-right: 4px;
      }
      span:last-of-type {
        color: var(--text-description);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
      }
      &:hover {
        cursor: pointer;
      }
    }
  }
}

.article-hot {
  width: 370px;
  padding: 20px;
  background-color: #fff;
  border-radius: 6px;
  &__list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
    &__item {
      display: flex;
      gap: 10px;
      border-radius: 4px;
      overflow: hidden;
      > div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 15px;
        span {
          font-size: var(--font-small);
          color: var(--text-mark);
        }
      }
    }
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .article-content {
    width: 100%;
  }
}

@media (max-width: 450px) {
  .article-content {
    width: 100%;
    &__link {
      margin-top: 20px;
      flex-direction: column;
      justify-content: unset;
      align-items: unset;
      gap: 20px;
      &__item {
        width: auto;
        flex-direction: column;
        height: auto;
        gap: 10px;
        max-height: 96px;
        padding: 10px;
        margin: 0;
        span {
          line-height: normal;
        }
        span:last-of-type {
          display: -webkit-box;
          -webkit-line-clamp: 2; /* 最多显示 2 行 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
        }
      }
    }
    &__content {
      &__image {
        width: 100%;
      }
    }
  }
}
