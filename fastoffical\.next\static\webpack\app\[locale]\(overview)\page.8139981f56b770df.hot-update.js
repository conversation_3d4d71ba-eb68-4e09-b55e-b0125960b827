"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(overview)/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/home/<USER>":
/*!************************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BannerSlider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./home.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BannerSlider() {\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale)();\n    const images = [\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>",\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>"\n    ];\n    // 自动播放功能\n    const startAutoPlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (images.length <= 1) return;\n        autoPlayRef.current = setInterval(()=>{\n            if (!isPaused) {\n                setCurrentIndex((prev)=>(prev + 1) % images.length);\n            }\n        }, 5000) // 5秒切换一次\n        ;\n    }, [\n        images.length,\n        isPaused\n    ]);\n    const stopAutoPlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (autoPlayRef.current) {\n            clearInterval(autoPlayRef.current);\n            autoPlayRef.current = null;\n        }\n    }, []);\n    // 切换到指定索引\n    const goToSlide = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((index)=>{\n        if (index === currentIndex || isTransitioning) return;\n        setCurrentIndex(index);\n    }, [\n        currentIndex,\n        isTransitioning\n    ]);\n    // 上一张\n    const goToPrev = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isTransitioning) return;\n        const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;\n        goToSlide(newIndex);\n    }, [\n        currentIndex,\n        images.length,\n        isTransitioning,\n        goToSlide\n    ]);\n    // 下一张\n    const goToNext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isTransitioning) return;\n        const newIndex = (currentIndex + 1) % images.length;\n        goToSlide(newIndex);\n    }, [\n        currentIndex,\n        images.length,\n        isTransitioning,\n        goToSlide\n    ]);\n    // 触摸事件处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleTouchStart = (e)=>{\n        setTouchStart({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY\n        });\n        setIsPaused(true) // 暂停自动播放\n        ;\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY\n        });\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const deltaX = touchEnd.x - touchStart.x;\n        const deltaY = touchEnd.y - touchStart.y;\n        const minSwipeDistance = 50;\n        // 判断是否为水平滑动\n        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {\n            if (deltaX > 0) {\n                goToPrev();\n            } else {\n                goToNext();\n            }\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n        setIsPaused(false) // 恢复自动播放\n        ;\n    };\n    // 鼠标事件处理\n    const handleMouseEnter = ()=>setIsPaused(true);\n    const handleMouseLeave = ()=>setIsPaused(false);\n    // 生命周期管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        startAutoPlay();\n        return ()=>stopAutoPlay();\n    }, [\n        startAutoPlay,\n        stopAutoPlay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isPaused) {\n            stopAutoPlay();\n        } else {\n            startAutoPlay();\n        }\n    }, [\n        isPaused,\n        startAutoPlay,\n        stopAutoPlay\n    ]);\n    // 过渡动画控制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTransitioning(true);\n        const timer = setTimeout(()=>setIsTransitioning(false), 300);\n        return ()=>clearTimeout(timer);\n    }, [\n        currentIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider\"]),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: listRef,\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__list\"]),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: images.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: item,\n                            width: 1920,\n                            height: 500,\n                            alt: \"\",\n                            unoptimized: true,\n                            style: {\n                                width: \"100%\",\n                                height: \"100%\",\n                                objectFit: \"cover\",\n                                objectPosition: \"center\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Swicher, {\n                        position: \"left\",\n                        disabled: true,\n                        onClick: ()=>handleSwitchClick(-1)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Swicher, {\n                        position: \"right\",\n                        onClick: ()=>handleSwitchClick(1)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Indicator, {\n                        count: images.length,\n                        currentIndex: currentIndex\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(BannerSlider, \"42xz7GIKgMnLmVHtnJ3qRJYf70o=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale\n    ];\n});\n_c = BannerSlider;\nfunction Swicher(param) {\n    let { position = \"left\", disabled = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: ()=>onClick(),\n        className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"banner-slider__switcher\".concat(position === \"left\" ? \"\" : \"--right\")], \" \").concat(disabled ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__switcher--disabled\"]) : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: \"/slider-\".concat(position === \"left\" ? \"left\" : \"right\", \".svg\"),\n            width: 44,\n            height: 44,\n            alt: \"\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Swicher;\nfunction Indicator(param) {\n    let { count, currentIndex } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator\"]),\n            children: Array(count).fill(\"\").map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item\"]), \" \").concat(index === currentIndex ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item--active\"]) : \"\")\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_c2 = Indicator;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"BannerSlider\");\n$RefreshReg$(_c1, \"Swicher\");\n$RefreshReg$(_c2, \"Indicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/home/<USER>"));

/***/ })

});