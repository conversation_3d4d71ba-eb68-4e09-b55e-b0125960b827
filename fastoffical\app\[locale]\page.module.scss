.page404container {
  position: relative;
  height: 100%;
  min-height: 300px;
}
.page404 {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 29.5px;
  &__content {
    width: 240px;
    display: flex;
    flex-direction: column;
    p {
      font-size: var(--font-large);
    }
    span {
      margin-top: 10px;
      font-size: var(--font-small);
      color: var(--text-description);
    }
  }
  &__buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    button {
      height: 38px;
      font-size: 17px;
      padding: 0 15px;
      line-height: 38px;
      border-radius: 4px;
      background-color: #fff;
    }
    &__back {
      color: var(--text-description);
    }
    &__home {
      color: var(--color-theme);
    }
  }
}
