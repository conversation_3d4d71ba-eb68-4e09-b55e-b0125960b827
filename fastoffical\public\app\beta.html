﻿<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8"/>
    <meta content="mobiSiteGalore" name="Generator" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="apple-touch-fullscreen" content="YES" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta name="format-detection" content="telephone=no">
    <title>APP Download</title>
    <style type="text/css">
        html{background: #ffffff}
        .section1{width: 100%;text-align: cente;margin: 30px 15px;}
    </style>
    <script src="./js/jquery-1.8.0.min.js"></script>
    <script>
        $(function(){
            var userAgent = navigator.userAgent;
            //自动下载
            if(/iPad|iPhone|iPod/.test(userAgent)){
                document.location = "itms-services://?action=download-manifest&url=https://app8h.sinaapp.com/getplist/http:--mdmyun.sinaapp.com-appplist-3561";
            } else if(/Android/.test(userAgent)){
                document.location = "http://yundownload.app8h.com/appdownload?comKey=429&appKey=3047";
            }
        })
    </script>
</head>
<body>
<div class="section1">
	<p>没有自动下载APP？</p>
	<p>Android用户，请选择用“浏览器”打开</p>
	<p>iOS用户，请选择用“Safari”打开</p>
                 <p> &nbsp;</p>
	<p>There is no automatic download the APP ?</p>
	<p>Android users, please open it on the browser</p>
	<p>iOS users, please open it on the Safari</p>
</div>
</body>
</html>