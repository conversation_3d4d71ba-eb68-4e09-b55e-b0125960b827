.videos {
  width: 100vw;
  position: relative;
  &__tabs {
    height: 220px;
    position: relative;
    background-color: rgb(179, 220, 252);
    &__content {
      width: var(--width-content);
      margin: auto;
      height: 220px;
      position: relative;
      &__title {
        display: flex;
        position: absolute;
        top: 31px;
        left: 0;
        gap: 8px;
        h1 {
          color: #fff;
        }
      }
      &__items {
        display: flex;
        gap: 14px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        &__item {
          height: 42px;
          padding: 0 30px;
          border-radius: 4px;
          font-size: var(--font-medium);
          color: var(--text-description);
          line-height: 42px;
          background-color: #fff;
          &--active {
            background-color: var(--color-theme);
            color: #fff;
          }
        }
      }
    }
  }
  &__list {
    width: var(--width-content);
    margin: auto;
    padding-top: 34px;
  }
  &__pagination {
    margin: auto;
    margin-top: 30px;
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .videos {
    &__list {
      padding: 34px 16px 0;
    }
  }
}

@media (max-width: 450px) {
  .videos {
    &__list {
      padding: 10px 16px 0;
    }
    &__pagination {
      margin-top: 0;
    }
  }
}
