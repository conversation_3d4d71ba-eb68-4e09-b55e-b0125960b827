'use client'
import styles from './product.module.scss'
import Image from 'next/image'
import { ProductData } from '@/data/type'
import { useState } from 'react'

export default function ProductPreview({
  productData,
}: {
  productData: ProductData
}) {
  const { imageSrc, subImageSrcs, name, types, description } = productData
  const [currentType, setCurrentType] = useState(types[0].name)

  return (
    <div className={styles['product-preview']}>
      <PreviewLeft imageSrc={imageSrc} subImageSrcs={subImageSrcs} />
      <PreviewRight
        title={name}
        description={description}
        types={types.map((item) => item.name)}
        properties={types.find((item) => item.name === currentType)?.properties}
        currentType={currentType}
        onClick={(type: string) => {
          setCurrentType(type)
        }}
      />
    </div>
  )
}

function PreviewLeft({
  imageSrc,
  subImageSrcs,
}: {
  imageSrc: string
  subImageSrcs: string[]
}) {
  const PreviewImage = () => (
    <div className={styles['product-preview__left__image']}>
      <Image
        src={imageSrc}
        width={200}
        height={200}
        alt="product"
        style={{ width: '100%', height: '100%' }}
      ></Image>
    </div>
  )

  const PreviewSwitch = () => {
    const SubImage = ({ src }: { src: string }) => (
      <div className={styles['product-preview__left__switch__image']}>
        <Image
          src={src}
          width={40}
          height={40}
          alt="product"
          style={{ width: '100%', height: '100%', objectFit: 'contain' }}
        ></Image>
      </div>
    )

    return (
      <div
        className={`${styles['product-preview__left__switch']} hide-on-small`}
      >
        <div
          className={styles['product-preview__left__switch__switcher']}
          style={{ transform: 'rotate(180deg)' }}
        >
          <Image
            src={'/arrow-right.svg'}
            width={12}
            height={24}
            alt="<"
          ></Image>
        </div>
        <div className={styles['product-preview__left__switch__image-list']}>
          <SubImage src={imageSrc} />
          {subImageSrcs.map((item, index) => (
            <SubImage key={index} src={item} />
          ))}
        </div>

        <div className={styles['product-preview__left__switch__switcher']}>
          <Image
            src={'/arrow-right.svg'}
            width={12}
            height={24}
            alt=">"
          ></Image>
        </div>
      </div>
    )
  }

  return (
    <div className={styles['product-preview__left']}>
      <PreviewImage />
      <PreviewSwitch />
    </div>
  )
}

function PreviewRight({
  title,
  description,
  types,
  currentType,
  properties,
  onClick,
}: {
  title: string
  description: string
  types: string[]
  currentType: string
  properties: string[] | undefined
  onClick: Function
}) {
  return (
    <div className={styles['product-preview__right']}>
      <h1>{title}</h1>
      <p>{description}</p>
      <div
        className={`${styles['product-preview__right__line']} hide-on-small`}
      ></div>
      <div className={styles['product-preview__right__types']}>
        {types.map((item, index) => (
          <button
            key={index}
            onClick={() => {
              onClick(item)
            }}
            className={`${styles['product-preview__right__types__item']} ${
              item === currentType
                ? styles['product-preview__right__types__item--active']
                : ''
            }`}
          >
            {item}
          </button>
        ))}
      </div>
      <div className={styles['product-preview__right__properties']}>
        {properties &&
          properties.map((item, index) => <span key={index}>{item}</span>)}
      </div>
      <div className={styles['product-preview__right__line']}></div>
      <div style={{ flex: 1 }}></div>
      <div className={styles['product-preview__right__buttons']}>
        <button>去购买</button>
        <button>
          <Image
            src={'/download-blue.svg'}
            width={18}
            height={18}
            alt=""
          ></Image>
          下载说明书
        </button>
      </div>
    </div>
  )
}
