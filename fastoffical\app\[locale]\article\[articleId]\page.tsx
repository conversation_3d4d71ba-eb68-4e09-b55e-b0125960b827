import BreadCrumbs from '../../ui/components/bread-crumbs'
import styles from '../article.module.scss'
import ArticleContent from '../../ui/article/article-content'
import ArticleHot from '../../ui/article/article-hot'
import Flex2ItemsBox from '../../ui/components/flex-2items-box'
import Image from 'next/image'
import Link from 'next/link'
import { getNewsData, getPrevAndNextNewsData, getHotNews } from '@/data/news'
import {
  ArticleData,
  HelpData,
  NewsData,
  PrevAndNextArticle,
  ProductData,
  ArticleType,
} from '@/data/type'
import { getProductData } from '@/data/products'
import { formatDate } from '@/utils/utils'
import {
  getHelpData,
  getHotHelpDatas,
  getPrevAndNextHelpData,
} from '@/data/helps'
import { getCurrentLocale, getI18n } from '@/locales/server'
import articleIds from './articleIds'
import { notFound } from 'next/navigation'
import { PageProps } from '@/data/type'
import ArticlePageTitle from '../../ui/article/article-title'
import type { Metadata, ResolvingMetadata } from 'next'

export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    description: '赛蓝科技 引领生活',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default async function Page({
  params,
}: {
  params: {
    articleId: string
  }
}) {
  const type = params.articleId.split('__')[0]
  const id = params.articleId
  const locale = await getCurrentLocale()

  if (!articleIds.find((item) => item.articleId === id)) {
    notFound()
    return
  }

  const t = await getI18n()

  let articleData: ArticleData = {
    title: '',
    time: new Date(),
    content: [],
  }
  let prevAndNextData: PrevAndNextArticle = {
    prev: -1,
    prevLink: '',
    prevTitle: '',
    prevTitleEn: '',
    next: -1,
    nextLink: '',
    nextTitle: '',
    nextTitleEn: '',
  }
  let hotDatas: HelpData[] = []
  let breadNavs: { link: string; name: string }[] = []

  if (type === 'help') {
    const helpDataRes = await getHelpData(id)
    if (!helpDataRes) return <div></div>
    articleData = {
      title:
        locale === 'zh'
          ? helpDataRes.title
          : helpDataRes.titleEn
          ? helpDataRes.titleEn
          : helpDataRes.title,
      content:
        locale === 'zh'
          ? helpDataRes.content
          : helpDataRes.contentEn
          ? helpDataRes.contentEn
          : helpDataRes.content,
      time: helpDataRes.time,
    }
    prevAndNextData = await getPrevAndNextHelpData(id)
    hotDatas = await getHotHelpDatas()
    breadNavs.push({
      link: '/support',
      name: t('support'),
    })
    breadNavs.push({
      link: '/support/help',
      name: t('help'),
    })
    breadNavs.push({
      link: `/support/help?nav=${helpDataRes.groupId}`,
      name: locale === 'zh' ? helpDataRes.groupName : helpDataRes.groupNameEn,
    })
  } else {
    const articleId = params.articleId
    const newsDataRes = await getNewsData(articleId)
    const prevAndNextNewsDataRes = await getPrevAndNextNewsData(articleId)
    let newsData: NewsData | undefined
    let prevAndNextNewsData: PrevAndNextArticle | undefined
    if (!newsDataRes) {
      return <div></div>
    }
    newsData = newsDataRes
    if (!prevAndNextNewsDataRes) {
      prevAndNextNewsData = {
        prev: -1,
        prevLink: '',
        prevTitle: '',
        prevTitleEn: '',
        next: -1,
        nextLink: '',
        nextTitle: '',
        nextTitleEn: '',
      }
    } else {
      prevAndNextNewsData = prevAndNextNewsDataRes
    }
    let linkProduct: ProductData | undefined
    if (newsData.linkProductId) {
      const productRes = await getProductData(newsData.linkProductId)
      if (productRes) linkProduct = productRes
      else linkProduct = undefined
    } else {
      linkProduct = undefined
    }
  }

  return (
    <>
      <div className={`${styles.article}`}>
        <div className={styles.article__breadcrumbs}>
          <BreadCrumbs navs={breadNavs} />
        </div>
        <div className={styles.article__page}>
          <ArticleContent
            articleData={articleData}
            prevAndNext={prevAndNextData}
          />
          <div className="hide-on-medium hide-on-small">
            <ArticleHot datas={hotDatas} type={type} />
          </div>
          {/* {linkProduct && <OtherProduct product={linkProduct} />} */}
          <News datas={hotDatas} type={type} />
        </div>
      </div>

      <ArticlePageTitle title={articleData.title} />
    </>
  )
}

function OtherProduct({ product }: { product: ProductData }) {
  const { imageSrc, name, description, properties, id } = product

  return (
    <Link
      href={`/product/product-detail/${id}`}
      className={`${styles.article__page__other} hide-on-large`}
    >
      <h3>关联产品</h3>
      <div className={styles.article__page__other__card}>
        <Image src={imageSrc} width={160} height={160} alt=""></Image>
        <div>
          <span>{name}</span>
          <p>{description}</p>
          <div>
            {properties.map((item, index) => (
              <>{index < 3 && <span key={index}>{item}</span>}</>
            ))}
          </div>
        </div>
      </div>
    </Link>
  )
}

async function News({ datas, type }: { datas: HelpData[]; type: string }) {
  const t = await getI18n()
  const locale = await getCurrentLocale()

  const infos = datas.map((item) => {
    const news = {
      imageSrc: item.coverSrc,
      title:
        locale === 'zh' ? item.title : item.titleEn ? item.titleEn : item.title,
      description: item.tip,
      time: formatDate(item.time, 'YYYY-MM-DD'),
      link: `/article/${item.id}`,
    }
    return news
  })

  const title = type === 'help' ? `${t('hot')}${t('help')}` : ''

  return (
    <div className={`${styles.article__page__news} hide-on-large`}>
      <h3>{title}</h3>
      <Flex2ItemsBox infos={infos} isProductDetail />
    </div>
  )
}
