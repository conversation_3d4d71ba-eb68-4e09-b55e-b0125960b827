// 定义共通样式
.nav--scrolled {
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
}
.nav {
  z-index: 10;
  width: 100vw;
  background-color: #fff;
  height: 66px;
  position: fixed;
  top: 0;
  left: 0;
  &__placeholder {
    height: 66px;
  }
  &__content {
    display: flex;
    align-items: center;
    height: 66px;
    line-height: 66px;
    width: var(--width-content);
    margin: auto;
  }

  &__list {
    display: flex;
    gap: 34px;
    margin: auto;
    &__item {
      height: 66px;
      line-height: 66px;
      display: flex;
      align-items: center;
      gap: 5.5px;
      font-size: var(--font-medium);
      &:hover {
        cursor: pointer;
      }
      &--link:hover {
        color: var(--color-theme);
      }
      &--active {
        color: var(--color-theme);
        border-bottom: 2px solid var(--color-theme);
      }
    }
  }

  &__right {
    display: flex;
    gap: 10px;
    align-items: center;
    &__language {
      width: 108px;
      display: flex;
      justify-content: center;
      gap: 8px;
      font-size: var(--font-medium);
    }

    &__language__text {
      color: var(--text-mark);
      font-size: var(--font-medium);
      &:hover {
        cursor: pointer;
      }
      &--active {
        color: var(--text-dark);
        &:hover {
          cursor: default;
        }
      }
    }

    &__search {
      width: 44px;
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: auto;
    }
    &__menu {
      width: 49px;
      height: 50px;
    }
  }
  &__drop {
    position: fixed;
    top: 66px;
    left: 0;
    z-index: 9;
  }
  &__mask {
    position: fixed;
    z-index: 8;
    left: 0;
    top: 0;
    width: 100vw;
    height: 1000vh;
    background-color: rgba($color: #000000, $alpha: 0.3);
  }
}

.banner-slider {
  position: relative;
  height: 500px;
  background-color: #c4c4c4;
  overflow: hidden;

  &__swiper {
    width: 100%;
    height: 100%;
    max-width: 2560px;
    margin: auto;
  }

  &__slide {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgb(13, 114, 153);
  }

  &__button {
    position: absolute;
    width: 44px;
    height: 44px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
      transform: translateY(-50%) scale(1.1);
    }

    &.swiper-button-disabled {
      opacity: 0.3;
      cursor: not-allowed;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
        transform: translateY(-50%) scale(1);
      }
    }
  }

  &__button-prev {
    left: 100px;
  }

  &__button-next {
    right: 100px;
  }
  &__pagination {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 18px;
    z-index: 10;
  }

  &__bullet {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 1;

    &:hover {
      background-color: rgba(255, 255, 255, 0.8);
      transform: scale(1.2);
    }

    &--active {
      background-color: #fff;
      transform: scale(1.2);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    height: 300px;

    &__switcher {
      left: 20px;
      width: 36px;
      height: 36px;

      &--right {
        right: 20px;
      }
    }

    &__indicator {
      bottom: 15px;
      gap: 12px;

      &__item {
        width: 10px;
        height: 10px;
      }
    }
  }

  @media (max-width: 480px) {
    height: 250px;

    &__switcher {
      left: 10px;
      width: 32px;
      height: 32px;

      &--right {
        right: 10px;
      }
    }
  }
}

.hot-spot {
  width: var(--width-content);
  margin: auto;
  margin-top: 34px;
  &__captain {
    display: flex;
    height: 31px;
    align-items: center;
    line-height: 31px;
    margin-bottom: 16px;
    position: relative;
    h2 {
      margin: 0;
    }
    &__more {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      height: 31px;
      line-height: 31px;
      align-items: center;
      gap: 4px;
      font-size: var(--font-small);
      color: var(--text-description);
    }
  }
  &__news {
    display: flex;
    justify-content: space-between;
    &__left,
    &__right {
      width: 625px;
      height: 350px;
    }
    &__left {
      border-radius: 6px;
      background-color: #fff;
      overflow: hidden;
    }
    &__right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    &__item {
      height: 160px;
      border-radius: 6px;
      background-color: #fff;
      display: flex;
      overflow: hidden;
      &__info {
        height: 100%;
      }
      &__image {
        width: 625px;
        height: 240px;
        &--right {
          width: 240px;
          height: 160px;
        }
      }
      &__info {
        padding: 16.5px 20px;
        display: flex;
        flex-direction: column;
        gap: 6px;
        div {
          color: var(--text-description);
          overflow: hidden;
          text-overflow: ellipsis;
        }
        span {
          display: block;
          font-size: var(--font-small);
        }
      }
      &--left {
        width: 100%;
        height: 100%;
        display: block;
        .hot-spot__news__item__info {
          div {
            white-space: nowrap;
          }
        }
      }
    }
  }
}

.about {
  margin-top: 30px;
  position: relative;
  &__cover {
    position: absolute;
    top: 0;
    left: 0;
    height: 221px;
    width: 100%;
    background: linear-gradient(0deg, #f5f7fa 0%, #0078e7);
  }
  &__content {
    position: relative;
    z-index: 1;
    width: var(--width-content);
    margin: auto;
    padding-top: 30px;
    h3 {
      color: #fff;
    }
    &__time {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 97px;
      margin-top: 12px;
      &__item {
        color: #fff;
        text-align: center;
        div:first-of-type {
          height: 42px;
          line-height: 42px;
          display: flex;
          align-items: center;
          gap: 4px;
          span:first-of-type {
            font-size: 35px;
            font-weight: 500;
          }
          span:last-of-type {
            font-size: var(--font-medium);
          }
        }
      }
      &--page {
        .about__content__time {
          &__item {
            color: var(--text-dark);
            div:last-of-type {
              color: var(--text-description);
            }
          }
        }
      }
    }
    &__prides {
      margin-top: 34px;
    }
  }
}

.contacts {
  display: flex;
  justify-content: space-between;
  margin-top: 52px;
  &--page {
    margin-top: 0;
  }
  &__info {
    width: 625px;
    height: 249px;
    border-radius: 6px;
    background-color: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 20px;
    &__items {
      margin: auto 0;
      display: flex;
      flex-direction: column;
      gap: 14px;
      justify-content: center;
    }
    &__title {
      font-size: var(--font-large);
    }
    &__item {
      display: flex;
      gap: 12px;
      color: var(--text-description);
    }
  }
  &__address {
    width: 625px;
    height: 249px;
    border-radius: 6px;
    overflow: hidden;
  }
}

.footer {
  margin-top: 38px;
  background-color: #282c30;
  & > div {
    width: var(--width-content);
    margin: auto;
  }
  &__logo {
    margin-right: 48px;
  }
  &__links {
    padding: 20px;
    display: flex;
    justify-content: center;
    gap: 51px;
    &__item {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 140px;
      align-items: flex-start;
      div {
        color: var(--gray-4);
        font-weight: var(--font-bolder);
      }
      span {
        color: var(--gray-1);
        font-weight: 400;
      }
    }
    &__follow {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
      > div {
        color: var(--gray-1);
      }
      &__weixin {
        position: relative;
        > div {
          position: absolute;
          z-index: 3;
          left: 80%;
          bottom: 80%;
          background-color: #fff;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding-bottom: 6px;
          span {
            text-align: center;
            color: var(--text-description);
            padding-bottom: 4px;
            font-size: var(--font-small);
          }
          a {
            color: var(--color-theme);
            font-size: var(--font-small);
          }
        }
      }
    }
  }
  &__copyright {
    text-align: center;
    font-size: var(--font-small);
    color: var(--gray);
    padding: 10px 0;
    &__link {
      &:hover {
        color: #fff;
      }
    }
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .nav {
    &__content {
      justify-content: space-between;
      padding: 0 15px;
    }
  }
  .hot-spot {
    padding: 0 20px;
    &__captain {
      h2 {
        text-align: left;
        margin: 0;
      }
    }
    &__news {
      &__left,
      &__right {
        width: calc(50% - 10px);
      }
      &__item {
        &__image {
          width: 100%;
          height: 71%;
          &--right {
            width: 50%;
            height: 100%;
          }
        }
        &__info {
          width: 50%;
          height: 100%;
          h4 {
            font-size: var(--font-medium);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          div {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
          }
        }
        &--left {
          .hot-spot__news__item__info {
            height: 29%;
            width: 100%;
            h4 {
              display: block;
              font-size: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin: 0;
              text-align: center;
              padding: 0 10px;
            }
            div {
              white-space: normal;
            }
            span {
              display: none;
            }
          }
        }
      }
    }
  }
  .about {
    &__content {
      padding: 30px 20px;
    }
  }
  .contacts {
    &__info,
    &__address {
      width: calc(50% - 10px);
    }
  }
  .footer {
    &__links {
      gap: 0;
      width: 120px;
    }
  }
  .banner-slider {
    height: 400px;
  }
}

@media (max-width: 450px) {
  .hot-spot {
    padding: 0 16px;
  }
  .footer {
    margin-top: 35px;
    &__logo {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 0 15px 0;
    }
    &__links {
      &__follow {
        padding-left: 30px;
      }
    }
  }
  .about {
    h3 {
      padding-left: 16px;
    }
    &__content {
      &__time {
        gap: 14px;
        justify-content: center;
        &__item {
          max-width: 140px;
          div:first-of-type {
            justify-content: center;
          }
        }
      }
    }
  }
  .hot-spot {
    &__news {
      flex-wrap: wrap;
      gap: 15px;
      &__left,
      &__right {
        width: 100%;
        height: auto;
      }
      &__right {
        gap: 15px;
        justify-content: flex-start;
      }
      &__item {
        height: 93px;
        &__info {
          padding: 7px 8px;
          justify-content: center;
          gap: 14px;
          h4 {
            font-size: var(--font-normal);
            display: -webkit-box;
            -webkit-line-clamp: 2; /* 最多显示 3 行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          div {
            display: none;
          }
        }
        &__image {
          width: 100%;
          height: 132px;
          &--right {
            width: 60%;
          }
        }
        &--left {
          height: auto;
          display: flex;
          flex-direction: column;
          .hot-spot__news__item__info {
            height: auto;
            padding: 20px;
            display: flex;
            gap: 6px;
            justify-content: flex-start;
            h4 {
              font-size: 20px;
            }
            div {
              display: block;
            }
          }
        }
      }
    }
  }
  .nav {
    height: 54px;
    &__placeholder {
      height: 54px;
    }
    &__content {
      justify-content: space-between;
      padding: 2px 15px;
      height: 54px;
    }
    &__list {
      &__item {
        height: 54px;
        line-height: 54px;
      }
    }
    &__drop {
      top: 54px;
    }
  }
  .contacts {
    margin-top: 20px;
    flex-direction: column;
    justify-content: flex-start;
    gap: 20px;
    padding: 16px;
    &__info,
    &__address {
      width: 100%;
    }
    &__address {
      height: 200px;
    }
  }
  .banner-slider {
    height: 44vw;
    &__indicator {
      bottom: 10px;
      &__item {
        width: 4px;
        height: 4px;
        &--active {
          background-color: transparent;
          border: 1px solid #fff;
        }
      }
    }
    &__switcher {
      display: none;
    }
  }
}
