import BreadCrumbs, {
  BreadLink,
} from '@/app/[locale]/ui/components/bread-crumbs'
import styles from '@/app/[locale]/product/product.module.scss'
import ProductPreview from '@/app/[locale]/ui/product/product-preview'
import ProductInfo from '@/app/[locale]/ui/product/product-info'
import { getProductData } from '@/data/products'
import { ProductData } from '@/data/type'

export default async function Page({
  params,
}: {
  params: {
    productId: string
  }
}) {
  const breadCrumbsNav: Array<BreadLink> = [
    {
      link: '/product',
      name: '产品中心',
    },
    {
      link: '/product',
      name: '产品分类1',
    },
  ]

  const productData: false | ProductData = await getProductData(
    params.productId
  )
  if (!productData) {
    return <div></div>
  }

  return (
    <div className={styles['product-detail']}>
      <div className={styles['product-detail__breadcrumbs']}>
        <BreadCrumbs navs={breadCrumbsNav}></BreadCrumbs>
      </div>
      <div className={styles['product-detail__preview']}>
        <ProductPreview productData={productData} />
      </div>
      <div className={styles['product-detail__info']}>
        <ProductInfo productData={productData} />
      </div>
    </div>
  )
}
