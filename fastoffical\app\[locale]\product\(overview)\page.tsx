import ProductList from '../../ui/product/product-list'
import { getGroupDatas, getProductDatas } from '@/data/products'
import type { Metadata, ResolvingMetadata } from 'next'
import { PageProps } from '@/data/type'

export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    title: locale === 'zh' ? '产品中心' : 'Product',
    description: '赛蓝科技 引领生活',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default async function page() {
  const groups = await getGroupDatas()
  const products = await getProductDatas()

  return (
    <div>
      <ProductList groups={groups} products={products} />
    </div>
  )
}
