# 官网

使用 Next.js 14 Typescript 编写

## 开发环境搭建

1. 环境安装： git clone 之后，运行 npm install 进行安装依赖

```bash
  npm install
```

2. 调试启动：进入 imcam_official_site 目录之后，运行 npm run dev 启动开发服务器

```bash
  npm run dev
```

## 打包

1. 在项目根目录下，运行 npm run build 等待打包

```bash
  npm run build
```

2. 打包完成后，进入.next 文件夹，删除 Cache 目录(如果有的话)
3. 返回根目录，将.next / public / package.json / README.md 这 4 个文件进行压缩，压缩文件名为 imcam_official_site.zip (也可以不压缩 README)
4. 将压缩包放到 \\***************\package\Garfield\offical
5. 如果之前未上传过 node_modules 或者项目有添加新的 module，需要将整个 node_modules 打包成 node_modules.zip，然后放到 \\***************\package\Garfield\offical

## Docker 打包安装

1. 首先需要安装 node 环境
2. 把 imcam_official_site.zip 和 node_modules.zip 压缩包中的文件全部拷贝到目录下
3. 输入下面命令来运行服务器

```bash
RUN npm run start
```

4. 后续更新的时候，如果不更新 node_modules，就只用替换 imcam_official_site.zip 里的文件后重新 npm run start 就可以。 如果更新的话，就要全部替换
