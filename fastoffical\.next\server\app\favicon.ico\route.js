(()=>{var A={};A.id=155,A.ids=[155],A.modules={517:A=>{"use strict";A.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4424:(A,e,t)=>{"use strict";t.r(e),t.d(e,{headerHooks:()=>b,originalPathname:()=>g,patchFetch:()=>m,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>p,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>f});var i={};t.r(i),t.d(i,{GET:()=>l,dynamic:()=>u});var r=t(5419),o=t(9108),n=t(9678),a=t(4231);let s=Buffer.from("AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAABMLAAATCwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAiTQKAIk1CwGJMgcNiTIGDokyBgOJMgYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAl0gfAJhGHQGXSCADl0khA5NGHwORRyEDjToRBYgyBmqIMQXEiTIGUcKbjQCPQhsDkUUhAo5EGACIKwUAhzEOAYcvCwKHLwsChy8LAocvCwKJMwsCk0EXA5hJIgOmWUAAmEUgAJQ+FgGVQxsDl0ggA5RFHAGVRh4AAAAAAIgwBACJMgYriDIGkIkyBqKJMgajiDIGo4gyBqWJMgaAiTIHMogxBdeIMQXIiTMJM4gzB5KIMgZ1ijMJBYkyBi6IMQWQiDEFoYgxBaGIMQWhiDEFoIgxBm+JNAhPiTIGn4kyB0eJMgcAjDgPCIkzB3aJMwaciTQILIkzBwAAAAAAhy4CAIkyBmaIMQX+iDEGyYgyBqaIMgaoiDIGqYgyBp6JMwgmiTIGc4gxBfaJMgZ+iDIGpYgxBe6JMgdDiDEGdYgyBvmJMwewiTIGmYkzB6SIMgbwiDEFy4gyBlmIMQX4iDEGoI08EgKFJQAAiDEGkYgxBf+IMgZhiDEFAAAAAACIMQUAiTIGR4gxBfWIMgZohCoAAJFHHQGVRxsBjUIiAYkyBxWJMgd8iDEF2YgxBdOJMgdSiDEF5ogyBrGJMgZTiDIG4YkzB7qJMwegijQIiYkyB7SIMQXviDIHWYgxBdeIMQXUiTMHGogyBgCJMgdHiDEF84gxBqWKMwkHiTIIAIgyBwCIMgciiDEF34gxBqiLNgwTjzoWCow5FAuNRSMCiTIGWIgxBeCJMgaYiDEF8okyBoOIMgaDiDEF+YkyBmKJMgZTiDEFqIgxBa6JMgZbiTIHUogxBfiIMgaUiDIGkIgxBfqJMgZeiDEFAIkzBxCIMQXLiDEF6YgyBy6IMgYAijQJAIs3DASIMgaTiDEF+YgxBsuIMgbBiDIGuIkzCDyIMQa2iDEF6okyBj6IMQW5iDEF4IkyB1yIMQXdiDEFwIk0CSWIMgZ5iDIGmYgxBpOIMQW1iDEF/4gxBcKJMgZTiDEF+IkyBq+JMwdsiTIGmogxBeeIMQX9iTIGVYcwBQAAAAAAiTEGAIkyByKIMgaWiDIG0YgyBtWIMgamiTMGSYgxBcSIMgaRiTUKBokyBkWIMQXPiTIGd4gyBnaIMQX6iTIGb4gyBn6IMgbZiDIG04kyBtKIMgbOiDIGfokzCB2JMgatiTIHtIkzB1eIMgaviDIG04gyBreJMgcuiDAGAAAAAAAAAAAAiDMHAIg0CAeJNAgWiTMIF4o0Cg2JMgYLiTEFGYozBwyHLAAAiTMHBIkxBReJMQURijQIG4gxBcaIMQXHijQJLIk0CRiJMwgXijMIF4ozCBWKNQwGjjwOAYo1ChCKNAkXijQJBok1Cg2JNAkYiTMJDo4zEQGNNA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIw2CQBpEQAAiDIGLIgxBUyJMgcegS0AAP+BowAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA///////////////////////////////////////////////////////////+H///wAMAQ4AAAEOAAAAjiAAAIYAAACGAAAABwAAAAeAgAAH//j////////////////////////////////////////////////////////////8=","base64");function l(){return new a.NextResponse(s,{headers:{"Content-Type":"image/x-icon","Cache-Control":"public, max-age=0, must-revalidate"}})}let u="force-static",d=new r.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/favicon.ico/route",pathname:"/favicon.ico",filename:"favicon",bundlePath:"app/favicon.ico/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Ffavicon.ico%2Froute&filePath=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cfavicon.ico&isDynamic=0!?__next_metadata_route__",nextConfigOutput:"",userland:i}),{requestAsyncStorage:c,staticGenerationAsyncStorage:h,serverHooks:p,headerHooks:b,staticGenerationBailout:f}=d,g="/favicon.ico/route";function m(){return(0,n.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:h})}},2491:A=>{"use strict";var e=Object.defineProperty,t=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,r=Object.prototype.hasOwnProperty,o={};function n(A){var e;let t=["path"in A&&A.path&&`Path=${A.path}`,"expires"in A&&(A.expires||0===A.expires)&&`Expires=${("number"==typeof A.expires?new Date(A.expires):A.expires).toUTCString()}`,"maxAge"in A&&"number"==typeof A.maxAge&&`Max-Age=${A.maxAge}`,"domain"in A&&A.domain&&`Domain=${A.domain}`,"secure"in A&&A.secure&&"Secure","httpOnly"in A&&A.httpOnly&&"HttpOnly","sameSite"in A&&A.sameSite&&`SameSite=${A.sameSite}`,"partitioned"in A&&A.partitioned&&"Partitioned","priority"in A&&A.priority&&`Priority=${A.priority}`].filter(Boolean);return`${A.name}=${encodeURIComponent(null!=(e=A.value)?e:"")}; ${t.join("; ")}`}function a(A){let e=new Map;for(let t of A.split(/; */)){if(!t)continue;let A=t.indexOf("=");if(-1===A){e.set(t,"true");continue}let[i,r]=[t.slice(0,A),t.slice(A+1)];try{e.set(i,decodeURIComponent(null!=r?r:"true"))}catch{}}return e}function s(A){var e,t;if(!A)return;let[[i,r],...o]=a(A),{domain:n,expires:s,httponly:d,maxage:c,path:h,samesite:p,secure:b,partitioned:f,priority:g}=Object.fromEntries(o.map(([A,e])=>[A.toLowerCase(),e]));return function(A){let e={};for(let t in A)A[t]&&(e[t]=A[t]);return e}({name:i,value:decodeURIComponent(r),domain:n,...s&&{expires:new Date(s)},...d&&{httpOnly:!0},..."string"==typeof c&&{maxAge:Number(c)},path:h,...p&&{sameSite:l.includes(e=(e=p).toLowerCase())?e:void 0},...b&&{secure:!0},...g&&{priority:u.includes(t=(t=g).toLowerCase())?t:void 0},...f&&{partitioned:!0}})}((A,t)=>{for(var i in t)e(A,i,{get:t[i],enumerable:!0})})(o,{RequestCookies:()=>d,ResponseCookies:()=>c,parseCookie:()=>a,parseSetCookie:()=>s,stringifyCookie:()=>n}),A.exports=((A,o,n,a)=>{if(o&&"object"==typeof o||"function"==typeof o)for(let n of i(o))r.call(A,n)||void 0===n||e(A,n,{get:()=>o[n],enumerable:!(a=t(o,n))||a.enumerable});return A})(e({},"__esModule",{value:!0}),o);var l=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(A){this._parsed=new Map,this._headers=A;let e=A.get("cookie");if(e)for(let[A,t]of a(e))this._parsed.set(A,{name:A,value:t})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...A){let e="string"==typeof A[0]?A[0]:A[0].name;return this._parsed.get(e)}getAll(...A){var e;let t=Array.from(this._parsed);if(!A.length)return t.map(([A,e])=>e);let i="string"==typeof A[0]?A[0]:null==(e=A[0])?void 0:e.name;return t.filter(([A])=>A===i).map(([A,e])=>e)}has(A){return this._parsed.has(A)}set(...A){let[e,t]=1===A.length?[A[0].name,A[0].value]:A,i=this._parsed;return i.set(e,{name:e,value:t}),this._headers.set("cookie",Array.from(i).map(([A,e])=>n(e)).join("; ")),this}delete(A){let e=this._parsed,t=Array.isArray(A)?A.map(A=>e.delete(A)):e.delete(A);return this._headers.set("cookie",Array.from(e).map(([A,e])=>n(e)).join("; ")),t}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(A=>`${A.name}=${encodeURIComponent(A.value)}`).join("; ")}},c=class{constructor(A){var e,t,i;this._parsed=new Map,this._headers=A;let r=null!=(i=null!=(t=null==(e=A.getSetCookie)?void 0:e.call(A))?t:A.get("set-cookie"))?i:[];for(let A of Array.isArray(r)?r:function(A){if(!A)return[];var e,t,i,r,o,n=[],a=0;function s(){for(;a<A.length&&/\s/.test(A.charAt(a));)a+=1;return a<A.length}for(;a<A.length;){for(e=a,o=!1;s();)if(","===(t=A.charAt(a))){for(i=a,a+=1,s(),r=a;a<A.length&&"="!==(t=A.charAt(a))&&";"!==t&&","!==t;)a+=1;a<A.length&&"="===A.charAt(a)?(o=!0,a=r,n.push(A.substring(e,i)),e=a):a=i+1}else a+=1;(!o||a>=A.length)&&n.push(A.substring(e,A.length))}return n}(r)){let e=s(A);e&&this._parsed.set(e.name,e)}}get(...A){let e="string"==typeof A[0]?A[0]:A[0].name;return this._parsed.get(e)}getAll(...A){var e;let t=Array.from(this._parsed.values());if(!A.length)return t;let i="string"==typeof A[0]?A[0]:null==(e=A[0])?void 0:e.name;return t.filter(A=>A.name===i)}has(A){return this._parsed.has(A)}set(...A){let[e,t,i]=1===A.length?[A[0].name,A[0].value,A[0]]:A,r=this._parsed;return r.set(e,function(A={name:"",value:""}){return"number"==typeof A.expires&&(A.expires=new Date(A.expires)),A.maxAge&&(A.expires=new Date(Date.now()+1e3*A.maxAge)),(null===A.path||void 0===A.path)&&(A.path="/"),A}({name:e,value:t,...i})),function(A,e){for(let[,t]of(e.delete("set-cookie"),A)){let A=n(t);e.append("set-cookie",A)}}(r,this._headers),this}delete(...A){let[e,t,i]="string"==typeof A[0]?[A[0]]:[A[0].name,A[0].path,A[0].domain];return this.set({name:e,path:t,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(n).join("; ")}}},3825:(A,e,t)=>{var i;(()=>{var r={226:function(r,o){!function(n,a){"use strict";var s="function",l="undefined",u="object",d="string",c="major",h="model",p="name",b="type",f="vendor",g="version",m="architecture",w="console",v="mobile",y="tablet",x="smarttv",k="wearable",P="embedded",I="Amazon",M="Apple",_="ASUS",j="BlackBerry",C="Browser",S="Chrome",O="Firefox",B="Google",R="Huawei",T="Microsoft",E="Motorola",L="Opera",D="Samsung",U="Sharp",G="Sony",N="Xiaomi",q="Zebra",H="Facebook",z="Chromium OS",J="Mac OS",Q=function(A,e){var t={};for(var i in A)e[i]&&e[i].length%2==0?t[i]=e[i].concat(A[i]):t[i]=A[i];return t},F=function(A){for(var e={},t=0;t<A.length;t++)e[A[t].toUpperCase()]=A[t];return e},W=function(A,e){return typeof A===d&&-1!==Y(e).indexOf(Y(A))},Y=function(A){return A.toLowerCase()},$=function(A,e){if(typeof A===d)return A=A.replace(/^\s\s*/,""),typeof e===l?A:A.substring(0,350)},X=function(A,e){for(var t,i,r,o,n,l,d=0;d<e.length&&!n;){var c=e[d],h=e[d+1];for(t=i=0;t<c.length&&!n&&c[t];)if(n=c[t++].exec(A))for(r=0;r<h.length;r++)l=n[++i],typeof(o=h[r])===u&&o.length>0?2===o.length?typeof o[1]==s?this[o[0]]=o[1].call(this,l):this[o[0]]=o[1]:3===o.length?typeof o[1]!==s||o[1].exec&&o[1].test?this[o[0]]=l?l.replace(o[1],o[2]):a:this[o[0]]=l?o[1].call(this,l,o[2]):a:4===o.length&&(this[o[0]]=l?o[3].call(this,l.replace(o[1],o[2])):a):this[o]=l||a;d+=2}},K=function(A,e){for(var t in e)if(typeof e[t]===u&&e[t].length>0){for(var i=0;i<e[t].length;i++)if(W(e[t][i],A))return"?"===t?a:t}else if(W(e[t],A))return"?"===t?a:t;return A},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},V={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[g,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[g,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,g],[/opios[\/ ]+([\w\.]+)/i],[g,[p,L+" Mini"]],[/\bopr\/([\w\.]+)/i],[g,[p,L]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,g],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[g,[p,"UC"+C]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[g,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[g,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[g,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[g,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[g,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+C],g],[/\bfocus\/([\w\.]+)/i],[g,[p,O+" Focus"]],[/\bopt\/([\w\.]+)/i],[g,[p,L+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[g,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[g,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[g,[p,L+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[g,[p,"MIUI "+C]],[/fxios\/([-\w\.]+)/i],[g,[p,O]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+C]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+C],g],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],g],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,g],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,H],g],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,g],[/\bgsa\/([\w\.]+) .*safari\//i],[g,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[g,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[g,[p,S+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,S+" WebView"],g],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[g,[p,"Android "+C]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,g],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[g,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[g,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[g,K,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,g],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],g],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[g,[p,O+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,g],[/(cobalt)\/([\w\.]+)/i],[p,[g,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,Y]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows (ce|mobile); ppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[m,/ower/,"",Y]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,Y]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[f,D],[b,y]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[f,D],[b,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[f,M],[b,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[f,M],[b,y]],[/(macintosh);/i],[h,[f,M]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[f,U],[b,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[f,R],[b,y]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[f,R],[b,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[f,N],[b,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[f,N],[b,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[f,"OPPO"],[b,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[f,"Vivo"],[b,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[h,[f,"Realme"],[b,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[f,E],[b,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[f,E],[b,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[f,"LG"],[b,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[f,"LG"],[b,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[f,"Lenovo"],[b,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[f,"Nokia"],[b,v]],[/(pixel c)\b/i],[h,[f,B],[b,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[f,B],[b,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[f,G],[b,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[f,G],[b,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[f,"OnePlus"],[b,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[f,I],[b,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[f,I],[b,v]],[/(playbook);[-\w\),; ]+(rim)/i],[h,f,[b,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[f,j],[b,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[f,_],[b,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[f,_],[b,v]],[/(nexus 9)/i],[h,[f,"HTC"],[b,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[f,[h,/_/g," "],[b,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[f,"Acer"],[b,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[f,"Meizu"],[b,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[f,h,[b,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[f,h,[b,y]],[/(surface duo)/i],[h,[f,T],[b,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[f,"Fairphone"],[b,v]],[/(u304aa)/i],[h,[f,"AT&T"],[b,v]],[/\bsie-(\w*)/i],[h,[f,"Siemens"],[b,v]],[/\b(rct\w+) b/i],[h,[f,"RCA"],[b,y]],[/\b(venue[\d ]{2,7}) b/i],[h,[f,"Dell"],[b,y]],[/\b(q(?:mv|ta)\w+) b/i],[h,[f,"Verizon"],[b,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[f,"Barnes & Noble"],[b,y]],[/\b(tm\d{3}\w+) b/i],[h,[f,"NuVision"],[b,y]],[/\b(k88) b/i],[h,[f,"ZTE"],[b,y]],[/\b(nx\d{3}j) b/i],[h,[f,"ZTE"],[b,v]],[/\b(gen\d{3}) b.+49h/i],[h,[f,"Swiss"],[b,v]],[/\b(zur\d{3}) b/i],[h,[f,"Swiss"],[b,y]],[/\b((zeki)?tb.*\b) b/i],[h,[f,"Zeki"],[b,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[f,"Dragon Touch"],h,[b,y]],[/\b(ns-?\w{0,9}) b/i],[h,[f,"Insignia"],[b,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[f,"NextBook"],[b,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],h,[b,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[f,"LvTel"],h,[b,v]],[/\b(ph-1) /i],[h,[f,"Essential"],[b,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[f,"Envizen"],[b,y]],[/\b(trio[-\w\. ]+) b/i],[h,[f,"MachSpeed"],[b,y]],[/\btu_(1491) b/i],[h,[f,"Rotor"],[b,y]],[/(shield[\w ]+) b/i],[h,[f,"Nvidia"],[b,y]],[/(sprint) (\w+)/i],[f,h,[b,v]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[f,T],[b,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[f,q],[b,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[f,q],[b,v]],[/smart-tv.+(samsung)/i],[f,[b,x]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[f,D],[b,x]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[f,"LG"],[b,x]],[/(apple) ?tv/i],[f,[h,M+" TV"],[b,x]],[/crkey/i],[[h,S+"cast"],[f,B],[b,x]],[/droid.+aft(\w)( bui|\))/i],[h,[f,I],[b,x]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[f,U],[b,x]],[/(bravia[\w ]+)( bui|\))/i],[h,[f,G],[b,x]],[/(mitv-\w{5}) bui/i],[h,[f,N],[b,x]],[/Hbbtv.*(technisat) (.*);/i],[f,h,[b,x]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[f,$],[h,$],[b,x]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[b,x]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,h,[b,w]],[/droid.+; (shield) bui/i],[h,[f,"Nvidia"],[b,w]],[/(playstation [345portablevi]+)/i],[h,[f,G],[b,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[f,T],[b,w]],[/((pebble))app/i],[f,h,[b,k]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[f,M],[b,k]],[/droid.+; (glass) \d/i],[h,[f,B],[b,k]],[/droid.+; (wt63?0{2,3})\)/i],[h,[f,q],[b,k]],[/(quest( 2| pro)?)/i],[h,[f,H],[b,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[f,[b,P]],[/(aeobc)\b/i],[h,[f,I],[b,P]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[h,[b,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[b,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[b,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[b,v]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[f,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[g,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[g,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,g],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[g,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,g],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[g,K,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[g,K,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[g,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,J],[g,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[g,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,g],[/\(bb(10);/i],[g,[p,j]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[g,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[g,[p,O+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[g,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[g,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[g,[p,S+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,z],g],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,g],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],g],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,g]]},AA=function(A,e){if(typeof A===u&&(e=A,A=a),!(this instanceof AA))return new AA(A,e).getResult();var t=typeof n!==l&&n.navigator?n.navigator:a,i=A||(t&&t.userAgent?t.userAgent:""),r=t&&t.userAgentData?t.userAgentData:a,o=e?Q(V,e):V,w=t&&t.userAgent==i;return this.getBrowser=function(){var A,e={};return e[p]=a,e[g]=a,X.call(e,i,o.browser),e[c]=typeof(A=e[g])===d?A.replace(/[^\d\.]/g,"").split(".")[0]:a,w&&t&&t.brave&&typeof t.brave.isBrave==s&&(e[p]="Brave"),e},this.getCPU=function(){var A={};return A[m]=a,X.call(A,i,o.cpu),A},this.getDevice=function(){var A={};return A[f]=a,A[h]=a,A[b]=a,X.call(A,i,o.device),w&&!A[b]&&r&&r.mobile&&(A[b]=v),w&&"Macintosh"==A[h]&&t&&typeof t.standalone!==l&&t.maxTouchPoints&&t.maxTouchPoints>2&&(A[h]="iPad",A[b]=y),A},this.getEngine=function(){var A={};return A[p]=a,A[g]=a,X.call(A,i,o.engine),A},this.getOS=function(){var A={};return A[p]=a,A[g]=a,X.call(A,i,o.os),w&&!A[p]&&r&&"Unknown"!=r.platform&&(A[p]=r.platform.replace(/chrome os/i,z).replace(/macos/i,J)),A},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(A){return i=typeof A===d&&A.length>350?$(A,350):A,this},this.setUA(i),this};AA.VERSION="1.0.35",AA.BROWSER=F([p,g,c]),AA.CPU=F([m]),AA.DEVICE=F([h,f,b,w,v,x,y,k,P]),AA.ENGINE=AA.OS=F([p,g]),typeof o!==l?(r.exports&&(o=r.exports=AA),o.UAParser=AA):t.amdO?void 0!==(i=(function(){return AA}).call(e,t,e,A))&&(A.exports=i):typeof n!==l&&(n.UAParser=AA);var Ae=typeof n!==l&&(n.jQuery||n.Zepto);if(Ae&&!Ae.ua){var At=new AA;Ae.ua=At.getResult(),Ae.ua.get=function(){return At.getUA()},Ae.ua.set=function(A){At.setUA(A);var e=At.getResult();for(var t in e)Ae.ua[t]=e[t]}}}("object"==typeof window?window:this)}},o={};function n(A){var e=o[A];if(void 0!==e)return e.exports;var t=o[A]={exports:{}},i=!0;try{r[A].call(t.exports,t,t.exports,n),i=!1}finally{i&&delete o[A]}return t.exports}n.ab=__dirname+"/";var a=n(226);A.exports=a})()},5419:(A,e,t)=>{"use strict";A.exports=t(517)},3457:(A,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var t in e)Object.defineProperty(A,t,{enumerable:!0,get:e[t]})}(e,{PageSignatureError:function(){return t},RemovedPageError:function(){return i},RemovedUAError:function(){return r}});class t extends Error{constructor({page:A}){super(`The middleware "${A}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class i extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class r extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},4231:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var t in e)Object.defineProperty(A,t,{enumerable:!0,get:e[t]})}(e,{ImageResponse:function(){return i.ImageResponse},NextRequest:function(){return r.NextRequest},NextResponse:function(){return o.NextResponse},userAgent:function(){return n.userAgent},userAgentFromString:function(){return n.userAgentFromString},URLPattern:function(){return a.URLPattern}});let i=t(7054),r=t(7699),o=t(4704),n=t(1172),a=t(8169)},2778:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NextURL",{enumerable:!0,get:function(){return u}});let i=t(7567),r=t(1219),o=t(2329),n=t(6993),a=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function s(A,e){return new URL(String(A).replace(a,"localhost"),e&&String(e).replace(a,"localhost"))}let l=Symbol("NextURLInternal");class u{constructor(A,e,t){let i,r;"object"==typeof e&&"pathname"in e||"string"==typeof e?(i=e,r=t||{}):r=t||e||{},this[l]={url:s(A,i??r.base),options:r,basePath:""},this.analyze()}analyze(){var A,e,t,r,a;let s=(0,n.getNextPathnameInfo)(this[l].url.pathname,{nextConfig:this[l].options.nextConfig,parseData:!0,i18nProvider:this[l].options.i18nProvider}),u=(0,o.getHostname)(this[l].url,this[l].options.headers);this[l].domainLocale=this[l].options.i18nProvider?this[l].options.i18nProvider.detectDomainLocale(u):(0,i.detectDomainLocale)(null==(e=this[l].options.nextConfig)?void 0:null==(A=e.i18n)?void 0:A.domains,u);let d=(null==(t=this[l].domainLocale)?void 0:t.defaultLocale)||(null==(a=this[l].options.nextConfig)?void 0:null==(r=a.i18n)?void 0:r.defaultLocale);this[l].url.pathname=s.pathname,this[l].defaultLocale=d,this[l].basePath=s.basePath??"",this[l].buildId=s.buildId,this[l].locale=s.locale??d,this[l].trailingSlash=s.trailingSlash}formatPathname(){return(0,r.formatNextPathnameInfo)({basePath:this[l].basePath,buildId:this[l].buildId,defaultLocale:this[l].options.forceLocale?void 0:this[l].defaultLocale,locale:this[l].locale,pathname:this[l].url.pathname,trailingSlash:this[l].trailingSlash})}formatSearch(){return this[l].url.search}get buildId(){return this[l].buildId}set buildId(A){this[l].buildId=A}get locale(){return this[l].locale??""}set locale(A){var e,t;if(!this[l].locale||!(null==(t=this[l].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.locales.includes(A)))throw TypeError(`The NextURL configuration includes no locale "${A}"`);this[l].locale=A}get defaultLocale(){return this[l].defaultLocale}get domainLocale(){return this[l].domainLocale}get searchParams(){return this[l].url.searchParams}get host(){return this[l].url.host}set host(A){this[l].url.host=A}get hostname(){return this[l].url.hostname}set hostname(A){this[l].url.hostname=A}get port(){return this[l].url.port}set port(A){this[l].url.port=A}get protocol(){return this[l].url.protocol}set protocol(A){this[l].url.protocol=A}get href(){let A=this.formatPathname(),e=this.formatSearch();return`${this.protocol}//${this.host}${A}${e}${this.hash}`}set href(A){this[l].url=s(A),this.analyze()}get origin(){return this[l].url.origin}get pathname(){return this[l].url.pathname}set pathname(A){this[l].url.pathname=A}get hash(){return this[l].url.hash}set hash(A){this[l].url.hash=A}get search(){return this[l].url.search}set search(A){this[l].url.search=A}get password(){return this[l].url.password}set password(A){this[l].url.password=A}get username(){return this[l].url.username}set username(A){this[l].url.username=A}get basePath(){return this[l].basePath}set basePath(A){this[l].basePath=A.startsWith("/")?A:`/${A}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new u(String(this),this[l].options)}}},7770:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var t in e)Object.defineProperty(A,t,{enumerable:!0,get:e[t]})}(e,{RequestCookies:function(){return i.RequestCookies},ResponseCookies:function(){return i.ResponseCookies}});let i=t(2491)},7054:(A,e)=>{"use strict";function t(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"ImageResponse",{enumerable:!0,get:function(){return t}})},7699:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var t in e)Object.defineProperty(A,t,{enumerable:!0,get:e[t]})}(e,{INTERNALS:function(){return a},NextRequest:function(){return s}});let i=t(2778),r=t(952),o=t(3457),n=t(7770),a=Symbol("internal request");class s extends Request{constructor(A,e={}){let t="string"!=typeof A&&"url"in A?A.url:String(A);(0,r.validateURL)(t),A instanceof Request?super(A,e):super(t,e);let o=new i.NextURL(t,{headers:(0,r.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:e.nextConfig});this[a]={cookies:new n.RequestCookies(this.headers),geo:e.geo||{},ip:e.ip,nextUrl:o,url:o.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[a].cookies}get geo(){return this[a].geo}get ip(){return this[a].ip}get nextUrl(){return this[a].nextUrl}get page(){throw new o.RemovedPageError}get ua(){throw new o.RemovedUAError}get url(){return this[a].url}}},4704:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NextResponse",{enumerable:!0,get:function(){return l}});let i=t(2778),r=t(952),o=t(7770),n=Symbol("internal response"),a=new Set([301,302,303,307,308]);function s(A,e){var t;if(null==A?void 0:null==(t=A.request)?void 0:t.headers){if(!(A.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let t=[];for(let[i,r]of A.request.headers)e.set("x-middleware-request-"+i,r),t.push(i);e.set("x-middleware-override-headers",t.join(","))}}class l extends Response{constructor(A,e={}){super(A,e),this[n]={cookies:new o.ResponseCookies(this.headers),url:e.url?new i.NextURL(e.url,{headers:(0,r.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:e.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[n].cookies}static json(A,e){let t=Response.json(A,e);return new l(t.body,t)}static redirect(A,e){let t="number"==typeof e?e:(null==e?void 0:e.status)??307;if(!a.has(t))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let i="object"==typeof e?e:{},o=new Headers(null==i?void 0:i.headers);return o.set("Location",(0,r.validateURL)(A)),new l(null,{...i,headers:o,status:t})}static rewrite(A,e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-rewrite",(0,r.validateURL)(A)),s(e,t),new l(null,{...e,headers:t})}static next(A){let e=new Headers(null==A?void 0:A.headers);return e.set("x-middleware-next","1"),s(A,e),new l(null,{...A,headers:e})}}},8169:(A,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"URLPattern",{enumerable:!0,get:function(){return t}});let t="undefined"==typeof URLPattern?void 0:URLPattern},1172:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var t in e)Object.defineProperty(A,t,{enumerable:!0,get:e[t]})}(e,{isBot:function(){return r},userAgentFromString:function(){return o},userAgent:function(){return n}});let i=function(A){return A&&A.__esModule?A:{default:A}}(t(3825));function r(A){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(A)}function o(A){return{...(0,i.default)(A),isBot:void 0!==A&&r(A)}}function n({headers:A}){return o(A.get("user-agent")||void 0)}},952:(A,e)=>{"use strict";function t(A){let e=new Headers;for(let[t,i]of Object.entries(A))for(let A of Array.isArray(i)?i:[i])void 0!==A&&("number"==typeof A&&(A=A.toString()),e.append(t,A));return e}function i(A){var e,t,i,r,o,n=[],a=0;function s(){for(;a<A.length&&/\s/.test(A.charAt(a));)a+=1;return a<A.length}for(;a<A.length;){for(e=a,o=!1;s();)if(","===(t=A.charAt(a))){for(i=a,a+=1,s(),r=a;a<A.length&&"="!==(t=A.charAt(a))&&";"!==t&&","!==t;)a+=1;a<A.length&&"="===A.charAt(a)?(o=!0,a=r,n.push(A.substring(e,i)),e=a):a=i+1}else a+=1;(!o||a>=A.length)&&n.push(A.substring(e,A.length))}return n}function r(A){let e={},t=[];if(A)for(let[r,o]of A.entries())"set-cookie"===r.toLowerCase()?(t.push(...i(o)),e[r]=1===t.length?t[0]:t):e[r]=o;return e}function o(A){try{return String(new URL(String(A)))}catch(e){throw Error(`URL is malformed "${String(A)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:e})}}Object.defineProperty(e,"__esModule",{value:!0}),function(A,e){for(var t in e)Object.defineProperty(A,t,{enumerable:!0,get:e[t]})}(e,{fromNodeOutgoingHttpHeaders:function(){return t},splitCookiesString:function(){return i},toNodeOutgoingHttpHeaders:function(){return r},validateURL:function(){return o}})},2329:(A,e)=>{"use strict";function t(A,e){let t;if((null==e?void 0:e.host)&&!Array.isArray(e.host))t=e.host.toString().split(":",1)[0];else{if(!A.hostname)return;t=A.hostname}return t.toLowerCase()}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getHostname",{enumerable:!0,get:function(){return t}})},7567:(A,e)=>{"use strict";function t(A,e,t){if(A)for(let o of(t&&(t=t.toLowerCase()),A)){var i,r;if(e===(null==(i=o.domain)?void 0:i.split(":",1)[0].toLowerCase())||t===o.defaultLocale.toLowerCase()||(null==(r=o.locales)?void 0:r.some(A=>A.toLowerCase()===t)))return o}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"detectDomainLocale",{enumerable:!0,get:function(){return t}})},1111:(A,e)=>{"use strict";function t(A,e){let t;let i=A.split("/");return(e||[]).some(e=>!!i[1]&&i[1].toLowerCase()===e.toLowerCase()&&(t=e,i.splice(1,1),A=i.join("/")||"/",!0)),{pathname:A,detectedLocale:t}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizeLocalePath",{enumerable:!0,get:function(){return t}})},1763:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"addLocale",{enumerable:!0,get:function(){return o}});let i=t(1963),r=t(3312);function o(A,e,t,o){if(!e||e===t)return A;let n=A.toLowerCase();return!o&&((0,r.pathHasPrefix)(n,"/api")||(0,r.pathHasPrefix)(n,"/"+e.toLowerCase()))?A:(0,i.addPathPrefix)(A,"/"+e)}},1963:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"addPathPrefix",{enumerable:!0,get:function(){return r}});let i=t(7711);function r(A,e){if(!A.startsWith("/")||!e)return A;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(A);return""+e+t+r+o}},8929:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"addPathSuffix",{enumerable:!0,get:function(){return r}});let i=t(7711);function r(A,e){if(!A.startsWith("/")||!e)return A;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(A);return""+t+e+r+o}},1219:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"formatNextPathnameInfo",{enumerable:!0,get:function(){return a}});let i=t(6915),r=t(1963),o=t(8929),n=t(1763);function a(A){let e=(0,n.addLocale)(A.pathname,A.locale,A.buildId?void 0:A.defaultLocale,A.ignorePrefix);return(A.buildId||!A.trailingSlash)&&(e=(0,i.removeTrailingSlash)(e)),A.buildId&&(e=(0,o.addPathSuffix)((0,r.addPathPrefix)(e,"/_next/data/"+A.buildId),"/"===A.pathname?"index.json":".json")),e=(0,r.addPathPrefix)(e,A.basePath),!A.buildId&&A.trailingSlash?e.endsWith("/")?e:(0,o.addPathSuffix)(e,"/"):(0,i.removeTrailingSlash)(e)}},6993:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"getNextPathnameInfo",{enumerable:!0,get:function(){return n}});let i=t(1111),r=t(6034),o=t(3312);function n(A,e){var t,n;let{basePath:a,i18n:s,trailingSlash:l}=null!=(t=e.nextConfig)?t:{},u={pathname:A,trailingSlash:"/"!==A?A.endsWith("/"):l};a&&(0,o.pathHasPrefix)(u.pathname,a)&&(u.pathname=(0,r.removePathPrefix)(u.pathname,a),u.basePath=a);let d=u.pathname;if(u.pathname.startsWith("/_next/data/")&&u.pathname.endsWith(".json")){let A=u.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),t=A[0];u.buildId=t,d="index"!==A[1]?"/"+A.slice(1).join("/"):"/",!0===e.parseData&&(u.pathname=d)}if(s){let A=e.i18nProvider?e.i18nProvider.analyze(u.pathname):(0,i.normalizeLocalePath)(u.pathname,s.locales);u.locale=A.detectedLocale,u.pathname=null!=(n=A.pathname)?n:u.pathname,!A.detectedLocale&&u.buildId&&(A=e.i18nProvider?e.i18nProvider.analyze(d):(0,i.normalizeLocalePath)(d,s.locales)).detectedLocale&&(u.locale=A.detectedLocale)}return u}},7711:(A,e)=>{"use strict";function t(A){let e=A.indexOf("#"),t=A.indexOf("?"),i=t>-1&&(e<0||t<e);return i||e>-1?{pathname:A.substring(0,i?t:e),query:i?A.substring(t,e>-1?e:void 0):"",hash:e>-1?A.slice(e):""}:{pathname:A,query:"",hash:""}}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"parsePath",{enumerable:!0,get:function(){return t}})},3312:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"pathHasPrefix",{enumerable:!0,get:function(){return r}});let i=t(7711);function r(A,e){if("string"!=typeof A)return!1;let{pathname:t}=(0,i.parsePath)(A);return t===e||t.startsWith(e+"/")}},6034:(A,e,t)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"removePathPrefix",{enumerable:!0,get:function(){return r}});let i=t(3312);function r(A,e){if(!(0,i.pathHasPrefix)(A,e))return A;let t=A.slice(e.length);return t.startsWith("/")?t:"/"+t}},6915:(A,e)=>{"use strict";function t(A){return A.replace(/\/$/,"")||"/"}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"removeTrailingSlash",{enumerable:!0,get:function(){return t}})}};var e=require("../../webpack-runtime.js");e.C(A);var t=A=>e(e.s=A),i=e.X(0,[638],()=>t(4424));module.exports=i})();