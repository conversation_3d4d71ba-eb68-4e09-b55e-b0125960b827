(()=>{var e={};e.id=853,e.ids=[853],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},4188:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>l,routeModule:()=>u,tree:()=>c});var a=i(482),s=i(9108),n=i(2563),o=i.n(n),r=i(8300),_={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(_[e]=()=>r[e]);i.d(t,_);let c=["",{children:["[locale]",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,5248)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\page.tsx"],d="/[locale]/about/page",m={require:i,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/about/page",pathname:"/[locale]/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5865:(e,t,i)=>{Promise.resolve().then(i.bind(i,9649))},9649:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u});var a=i(5344),s=i(9378),n=i.n(s),o=i(9410),r=i(355),_=i(3898),c=i(6112),l=i(3729),d=i(8428),m=i(2716);function u(){return a.jsx(m.e4,{locale:(0,m.eV)(),children:a.jsx(p,{})})}function p(){let e=(0,m.QT)(),[t,i]=(0,l.useState)([{id:"0",text:e("aboutCylan")},{id:"1",text:e("cylanPrides")},{id:"2",text:e("contactUs")}]),[s,_]=(0,l.useState)(t[0].id),[c,d]=(0,l.useState)(s),u=(0,l.useRef)(null),p=e=>{d(e)};return(0,a.jsxs)("div",{className:n().about,children:[a.jsx("div",{className:`${n().about__image} hide-on-small`,children:a.jsx(o.default,{src:"/about-banner.webp",width:1920,height:900,alt:"",style:{width:"100%",height:"100%",objectFit:"fill",objectPosition:"center"},unoptimized:!0})}),a.jsx("h6",{id:"about-cylan"}),a.jsx("div",{className:"hide-on-small",children:a.jsx(r.default,{title:e("aboutUs"),iconSrc:"/about-icon.svg",currentTab:s,tabs:t,showBanner:!1,background:"transparent",onTabChange:e=>{p(e)}})}),a.jsx("div",{className:"hide-on-medium hide-on-large",children:a.jsx(r.default,{title:e("aboutUs"),iconSrc:"/about-icon.svg",currentTab:s,tabs:t,bannerMobileSrc:"/about-banner-mobile.jpg",background:"rgb(214,218,211)",onTabChange:e=>{p(e)}})}),a.jsx(l.Suspense,{children:a.jsx(x,{contentRef:u,tabs:t,setCurrentTab:_,trigger:c})})]})}function x({tabs:e,setCurrentTab:t=()=>{},contentRef:i,trigger:s}){let o=(0,l.useRef)(null),r=(0,l.useRef)(null),c=(0,l.useRef)(null),u=(0,l.useRef)(!0),p=(0,d.useRouter)(),x=(0,d.usePathname)(),g=(0,l.useCallback)(i=>{let a;a=i===e[0].id?"about-cylan":i===e[1].id?"prides":"contacts",t(i),p.replace(`${x}/#${a}`)},[e,t,p,x]);return(0,l.useEffect)(()=>{let i=()=>{let i=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop,a=o.current?.getBoundingClientRect().top;a&&i<a&&t(e[0].id)};return window.addEventListener("scroll",i),()=>{window.removeEventListener("scroll",i)}},[g,e,t]),(0,l.useEffect)(()=>{u.current?u.current=!1:g(s)},[s,g]),(0,a.jsxs)(m.e4,{locale:(0,m.eV)(),children:[(0,a.jsxs)("div",{ref:i,className:n().about__content,children:[a.jsx("div",{ref:o,children:a.jsx(h,{})}),a.jsx(b,{}),a.jsx("div",{ref:r,children:a.jsx(j,{})}),a.jsx("h6",{id:"contacts"}),a.jsx("div",{ref:c,className:`${n().about__content__contacts} hide-on-small`,children:a.jsx(_.Contacts,{isAboutPage:!0})})]}),a.jsx("div",{className:`${n().about__content__contacts} hide-on-medium hide-on-large `,children:a.jsx(_.Contacts,{isAboutPage:!0})})]})}function h(){let e=(0,m.QT)();e("aboutCylanQuotesTitle1"),e("aboutCylanQuotesText1"),e("aboutCylanQuotesTitle2"),e("aboutCylanQuotesText2"),e("aboutCylanQuotesTitle3"),e("aboutCylanQuotesText3");let t=({title:e,textList:t})=>(0,a.jsxs)("div",{className:n().about__content__main__view__card,children:[a.jsx("h5",{children:e}),a.jsx("div",{className:n().about__content__main__view__card__content,children:t.map((e,t)=>a.jsx("div",{children:e},t))})]}),i=[e("companySetTime"),e("companyMainLocation"),e("companyCoreTech"),e("companyService")],s=[e("companyCoreProductText1"),e("companyCoreProductText2"),e("companyCoreProductText3"),e("companyCoreProductText4"),e("companyCoreProductText5"),e("companyCoreProductText6"),e("companyCoreProductText7"),e("companyCoreProductText8"),e("companyCoreProductText9"),e("companyCoreProductText10")],o=[e("companyGlobalText1"),e("companyGlobalText2"),e("companyGlobalText3"),e("companyGlobalText4"),e("companyGlobalText5")];return(0,a.jsxs)("div",{className:n().about__content__main,children:[a.jsx("h1",{children:e("aboutCylanTitle")}),(0,a.jsxs)("div",{className:n().about__content__main__view,children:[a.jsx(t,{title:e("companyReview"),textList:i}),a.jsx(t,{title:e("companyCoreProduct"),textList:s}),a.jsx(t,{title:e("companyGlobal"),textList:o})]})]})}function b(){var e;let t;let i=(0,m.QT)();return(e=t||(t={}))[e.first=0]="first",e[e.normal=1]="normal",e[e.last=2]="last",(0,a.jsxs)("div",{className:n().about__content__career,children:[a.jsx("h1",{children:i("career")}),a.jsx("p",{children:i("careerDescription")}),a.jsx("div",{className:n().about__content__career__companytime,children:a.jsx(_.CompanyTime,{isAboutPage:!0})}),a.jsx("h6",{id:"prides"})]})}function j(){var e;let t;let i=(0,m.QT)();(e=t||(t={}))[e.small=0]="small",e[e.normal=1]="normal",e[e.large=2]="large";let[s,r]=(0,l.useState)([{text:i("pride1"),show:!1},{text:i("pride2"),show:!1},{text:i("pride3"),show:!1},{text:i("pride4"),show:!1},{text:i("pride5"),show:!1},{text:i("pride6"),show:!1},{text:i("pride7"),show:!1},{text:i("pride8"),show:!1},{text:i("pride9"),show:!1}]),_=({src:e,size:t=1,index:i})=>(0,a.jsxs)("div",{onClick:()=>{r(s.map((e,t)=>(t===i&&(e.show=!e.show),e)))},className:`${n().about__content__prides__list__item} ${2===t?n()["about__content__prides__list__item--large"]:1===t?n()["about__content__prides__list__item--normal"]:n()["about__content__prides__list__item--small"]}`,children:[a.jsx(o.default,{src:e,width:2===t?478:231,height:0===t?154:326,alt:"",style:{height:"100%",width:"100%",objectFit:"fill"},unoptimized:!0}),s[i].show&&a.jsx("div",{className:n().about__content__prides__list__item__cover,children:s[i].text})]});return a.jsx(a.Fragment,{children:(0,a.jsxs)("div",{className:`${n().about__content__prides}`,children:[a.jsx("h1",{children:i("cylanPrides")}),(0,a.jsxs)("div",{className:"hide-on-small",children:[(0,a.jsxs)("div",{className:n().about__content__prides__list,children:[a.jsx(_,{src:"/pride-image-1.jpg",index:0}),a.jsx(_,{src:"/pride-image-2.jpg",size:2,index:1}),a.jsx(_,{src:"/pride-image-3.jpg",index:2}),a.jsx(_,{src:"/pride-image-4.jpg",index:3})]}),(0,a.jsxs)("div",{className:n().about__content__prides__list,children:[a.jsx(_,{src:"/pride-image-5.jpg",size:0,index:4}),a.jsx(_,{src:"/pride-image-6.jpg",size:0,index:5}),a.jsx(_,{src:"/pride-image-7.jpg",size:0,index:6}),a.jsx(_,{src:"/pride-image-8.jpg",size:0,index:7}),a.jsx(_,{src:"/pride-image-9.jpg",size:0,index:8})]})]}),a.jsx(c.Z,{})]})})}},6112:(e,t,i)=>{"use strict";i.d(t,{Z:()=>o});var a=i(5344),s=i(9410),n=i(2716);function o(){return a.jsx(n.e4,{locale:(0,n.eV)(),children:a.jsx(r,{})})}function r(){let e=(0,n.QT)(),t=[{imageSrc:"/pride-image-1.jpg",title:e("pride1"),imageWidth:120},{imageSrc:"/pride-image-2.jpg",title:e("pride2"),imageWidth:242},{imageSrc:"/pride-image-3.jpg",title:e("pride3"),imageWidth:120},{imageSrc:"/pride-image-4.jpg",title:e("pride4"),imageWidth:120},{imageSrc:"/pride-image-5.jpg",title:e("pride5"),imageWidth:240},{imageSrc:"/pride-image-6.jpg",title:e("pride6"),imageWidth:240},{imageSrc:"/pride-image-7.jpg",title:e("pride7"),imageWidth:240},{imageSrc:"/pride-image-8.jpg",title:e("pride8"),imageWidth:240},{imageSrc:"/pride-image-9.jpg",title:e("pride9"),imageWidth:240}],i=({imageSrc:e,title:t,imageWidth:i})=>(0,a.jsxs)("div",{style:{width:i},children:[a.jsx("div",{children:a.jsx(s.default,{src:e,height:160,width:i,alt:"",unoptimized:!0})}),a.jsx("div",{children:t})]});return a.jsx("div",{className:"cylan-certificates hide-on-medium hide-on-large",children:t.map((e,t)=>a.jsx(i,{...e},t))})}},7572:(e,t,i)=>{"use strict";i.d(t,{H:()=>a,Z:()=>c});var a,s=i(5344),n=i(9410),o=i(3729),r=i.n(o);i(4507);var _=i(6506);function c({infos:e,imageSize:t,imageBox:i,mode:a="",gap:o=0,isDetail:c=!1}){let l=({imageSrc:e,title:o,tip:r="",link:c="",videoSrc:l=""})=>{let d=!!c,m=!!l,u=`flex-box-with-4items__card ${"product"===a?"flex-box-with-4items__card--product":""} ${d?"flex-box-with-4items__card--link":""} ${m?"flex-box-with-4items__card--video":""}`,p=`${""===a?"":`flex-box-with-4items__card__info--${a}`} flex-box-with-4items__card__info`,x=(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"flex-box-with-4items__card__image",style:{aspectRatio:i.width/i.height},children:m?(0,s.jsxs)("video",{width:t.width,height:t.height,controls:!0,poster:e,children:[s.jsx("source",{src:l,type:"video/mp4"}),"Your browser does not support the video tag."]}):s.jsx(n.default,{unoptimized:!0,src:e,width:t.width,height:t.height,alt:"image",style:{objectFit:"fill",width:"100%",height:"auto"}})}),(0,s.jsxs)("div",{className:p,children:[s.jsx("div",{children:o}),"pride"===a?"":r?s.jsx("span",{children:r}):""]})]});return d?s.jsx(_.default,{href:c,className:u,children:x}):s.jsx("div",{className:u,children:x})};return s.jsx("div",{className:`flex-box-with-4items ${"product"===a?"flex-box-with-4items--product":""} ${c?"flex-box-with-4items--detail":""}`,style:o?{gap:o}:{},children:e.map((e,t)=>s.jsx(r().Fragment,{children:l(e)},`${e.link}-${t}`))})}!function(e){e.normal="",e.product="product",e.pride="pride"}(a||(a={}))},355:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c});var a=i(5344);i(4507);var s=i(9410);function n({src:e="/search-banner.webp",mobileSrc:t="/search-banner.webp"}){return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"banner hide-on-small",children:a.jsx(s.default,{src:e,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})}),a.jsx("div",{className:"banner hide-on-medium hide-on-large",children:a.jsx(s.default,{src:t,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})})]})}var o=i(3729),r=i.n(o),_=i(6506);function c({title:e="",iconSrc:t="",tabs:i=[],currentTab:o,bannerSrc:c="/pic_shipinbg@2x (1).webp",bannerMobileSrc:d="/pic_shipinbg@2x (1).webp",background:m="rgb(179, 220, 252)",onTabChange:u=()=>{},showBanner:p=!0,isSearch:x=!1,isLink:h=!1}){let b=e=>{u(e)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"page-tabs",style:m?{background:m}:{},children:[p&&a.jsx(n,{src:c,mobileSrc:d}),a.jsx(()=>a.jsx("div",{className:"page-tabs__content",children:(0,a.jsxs)("div",{className:"page-tabs__content__title",children:[a.jsx(s.default,{src:t,width:34,height:34,alt:""}),a.jsx("h1",{children:e})]})}),{}),!x&&a.jsx(({isLink:e=!1})=>a.jsx("div",{className:"page-tabs__content__items hide-on-small",children:i.map((t,i)=>a.jsx(r().Fragment,{children:e?a.jsx(_.default,{href:`${t.id}`,children:a.jsx("button",{className:`page-tabs__content__items__item ${o===t.id?"page-tabs__content__items__item--active":""}`,onClick:()=>b(t.id),children:t.text})}):a.jsx("button",{className:`page-tabs__content__items__item ${o===t.id?"page-tabs__content__items__item--active":""}`,onClick:()=>b(t.id),children:t.text})},i))}),{isLink:h})]}),a.jsx(l,{onTabChange:u,tabs:i,currentTab:o,isLink:h})]})}function l({tabs:e,currentTab:t,onTabChange:i,isLink:s}){let n=({id:e,text:s})=>a.jsx("div",{className:`${t===e?"page-tabs__tabs-small__tab--active":""} page-tabs__tabs-small__tab`,onClick:()=>{i(e)},children:s});return a.jsx("div",{className:"page-tabs__tabs-small hide-on-medium hide-on-large",children:e.map((e,t)=>a.jsx(r().Fragment,{children:s?a.jsx(_.default,{href:e.id,children:a.jsx(n,{...e})}):a.jsx(n,{...e})},t))})}},3898:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CompanyTime:()=>u,Contacts:()=>x,default:()=>d});var a=i(5344),s=i(5620),n=i.n(s),o=i(6506),r=i(9410),_=i(7572),c=i(6112),l=i(2716);function d(){return a.jsx(l.e4,{locale:(0,l.eV)(),children:a.jsx(m,{})})}function m(){let e=(0,l.QT)();return(0,a.jsxs)("div",{className:n().about,children:[a.jsx("div",{className:n().about__cover}),(0,a.jsxs)("div",{className:n().about__content,children:[a.jsx("h3",{children:e("aboutUs")}),a.jsx(u,{}),a.jsx(p,{}),a.jsx(x,{})]})]})}function u({isAboutPage:e=!1}){let t=(0,l.QT)(),i=(0,l.eV)(),s=({num:e,unit:t,text:i})=>(0,a.jsxs)("div",{className:n().about__content__time__item,children:[(0,a.jsxs)("div",{children:[a.jsx("span",{children:e}),a.jsx("span",{children:t})]}),a.jsx("div",{children:i})]}),o=()=>a.jsx("div",{style:{height:46,width:0,opacity:.1,border:`1px solid ${e?"var(--gray)":"#fff"}`}});return(0,a.jsxs)("div",{className:`${n().about__content__time} ${e?n()["about__content__time--page"]:""}`,children:[a.jsx(s,{num:2005,unit:t("year"),text:t("companiyTime")}),a.jsx(o,{}),a.jsx(s,{num:"zh"===i?45:450,unit:"zh"===i?"万+":"K+",text:t("deviceSells")}),a.jsx(o,{}),a.jsx(s,{num:"zh"===i?90:900,unit:"zh"===i?"万+":"K+",text:t("activeUsers")})]})}function p(){let e=(0,l.QT)(),t={infos:[{imageSrc:"/pride-image-6.jpg",title:e("pride6")},{imageSrc:"/pride-image-9.jpg",title:e("pride9")},{imageSrc:"/pride-image-5.jpg",title:e("pride5")},{imageSrc:"/pride-image-2.jpg",title:e("pride2")}],imageSize:{width:300,height:200},imageBox:{width:300,height:200},mode:_.H.pride};return(0,a.jsxs)("div",{className:n().about__prides,children:[a.jsx("div",{className:"hide-on-small",style:{marginTop:30},children:a.jsx(_.Z,{...t})}),a.jsx(c.Z,{})]})}function x({isAboutPage:e=!1}){let t=(0,l.QT)(),i=({iconUrl:e,text:t})=>(0,a.jsxs)("div",{className:n().contacts__info__item,children:[a.jsx(r.default,{src:e,width:18,height:20,alt:"icon"}),a.jsx("div",{style:{display:"flex",flexDirection:"column",gap:4},children:t.split("/n").map((e,t)=>a.jsx("div",{children:e},t))})]});return(0,a.jsxs)("div",{className:`${n().contacts} ${e?n()["contacts--page"]:""}`,children:[(0,a.jsxs)("div",{className:n().contacts__info,children:[a.jsx("div",{className:n().contacts__info__title,children:t("contactUs")}),(0,a.jsxs)("div",{className:n().contacts__info__items,children:[a.jsx(i,{iconUrl:"/contact-position.svg",text:t("cylanAddress")}),a.jsx(i,{iconUrl:"/contact-email.svg",text:"<EMAIL>"}),a.jsx(i,{iconUrl:"/contact-phone.svg",text:"+86-0755-83073491"})]})]}),a.jsx("div",{className:n().contacts__address,children:a.jsx(o.default,{href:"https://map.baidu.com/poi/%E5%90%88%E6%88%90%E5%8F%B7%E6%B7%B1%E5%9C%B3%E6%B0%91%E4%BF%97%E6%96%87%E5%8C%96%E4%BA%A7%E4%B8%9A%E5%9B%AD/@12682764.738888016,2566016.5718568345,14z?uid=eb63e5cd850d1ef4a3acc4a1&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo",children:a.jsx(r.default,{src:"/address-image.webp",width:625,height:249,alt:"address"})})})]})}},9378:e=>{e.exports={about:"about_about__wvePq",about__image:"about_about__image__KxBNO",about__content:"about_about__content__LS1_A",about__content__main__view:"about_about__content__main__view__SDZLN",about__content__main__view__card:"about_about__content__main__view__card___chDY",about__content__main__view__card__content:"about_about__content__main__view__card__content__HK2_u",about__content__main__quotes:"about_about__content__main__quotes__gj59l",about__content__main__quotes__item:"about_about__content__main__quotes__item__fN1Rr",about__content__career__companytime:"about_about__content__career__companytime__HUZ14",about__content__career__timeline:"about_about__content__career__timeline__hCaJK",about__content__career__timeline__item:"about_about__content__career__timeline__item__r5rr0","about__content__career__timeline__item--reverse":"about_about__content__career__timeline__item--reverse__s8Qhz",about__content__prides:"about_about__content__prides__0DhgW",about__content__prides__list:"about_about__content__prides__list__LSiiG",about__content__prides__list__item:"about_about__content__prides__list__item__BPyny","about__content__prides__list__item--normal":"about_about__content__prides__list__item--normal__tpjHv","about__content__prides__list__item--large":"about_about__content__prides__list__item--large__VfdUa","about__content__prides__list__item--small":"about_about__content__prides__list__item--small__fRQpL",about__content__prides__list__item__cover:"about_about__content__prides__list__item__cover__GN83j",about__content__contacts:"about_about__content__contacts__S9y1Y"}},5248:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>c,generateMetadata:()=>_});var a=i(5036);let s=(0,i(6843).createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\about\about-content.tsx`),{__esModule:n,$$typeof:o}=s,r=s.default;async function _({params:e,searchParams:t},i){return{title:"zh"===e.locale?"关于我们":"About Us",description:"赛蓝科技 引领生活",icons:{icon:"/favicon.ico"}}}function c(){return a.jsx(r,{})}}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[638,47,563,613],()=>i(4188));module.exports=a})();