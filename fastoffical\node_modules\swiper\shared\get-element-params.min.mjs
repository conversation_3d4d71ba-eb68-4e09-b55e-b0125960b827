import{e as extend,i as isObject,c as attrToProp,p as paramsList}from"./update-swiper.min.mjs";import{d as defaults}from"./swiper-core.min.mjs";const formatValue=e=>{if(parseFloat(e)===Number(e))return Number(e);if("true"===e)return!0;if(""===e)return!0;if("false"===e)return!1;if("null"===e)return null;if("undefined"!==e){if("string"==typeof e&&e.includes("{")&&e.includes("}")&&e.includes('"')){let a;try{a=JSON.parse(e)}catch(t){a=e}return a}return e}},modulesParamsList=["a11y","autoplay","controller","cards-effect","coverflow-effect","creative-effect","cube-effect","fade-effect","flip-effect","free-mode","grid","hash-navigation","history","keyboard","mousewheel","navigation","pagination","parallax","scrollbar","thumbs","virtual","zoom"];function getParams(e,a,t){const r={},n={};extend(r,defaults);const i=[...paramsList,"on"],o=i.map((e=>e.replace(/_/,"")));i.forEach((a=>{a=a.replace("_",""),void 0!==e[a]&&(n[a]=e[a])}));const s=[...e.attributes];return"string"==typeof a&&void 0!==t&&s.push({name:a,value:isObject(t)?{...t}:t}),s.forEach((e=>{const a=modulesParamsList.find((a=>e.name.startsWith(`${a}-`)));if(a){const t=attrToProp(a),r=attrToProp(e.name.split(`${a}-`)[1]);void 0===n[t]&&(n[t]={}),!0===n[t]&&(n[t]={enabled:!0}),!1===n[t]&&(n[t]={enabled:!1}),n[t][r]=formatValue(e.value)}else{const a=attrToProp(e.name);if(!o.includes(a))return;const t=formatValue(e.value);n[a]&&modulesParamsList.includes(e.name)&&!isObject(t)?(n[a].constructor!==Object&&(n[a]={}),n[a].enabled=!!t):n[a]=t}})),extend(r,n),r.navigation?r.navigation={prevEl:".swiper-button-prev",nextEl:".swiper-button-next",...!0!==r.navigation?r.navigation:{}}:!1===r.navigation&&delete r.navigation,r.scrollbar?r.scrollbar={el:".swiper-scrollbar",...!0!==r.scrollbar?r.scrollbar:{}}:!1===r.scrollbar&&delete r.scrollbar,r.pagination?r.pagination={el:".swiper-pagination",...!0!==r.pagination?r.pagination:{}}:!1===r.pagination&&delete r.pagination,{params:r,passedParams:n}}export{getParams as g};
//# sourceMappingURL=get-element-params.min.mjs.map