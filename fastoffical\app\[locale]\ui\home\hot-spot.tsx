import { formatDate } from '@/utils/utils'
import { NewsData } from '@/data/type'
import getNewsDatas from '@/data/news'
import styles from './home.module.scss'
import Link from 'next/link'
import Image from 'next/image'
import Flex4ItemsBox, {
  Flex4ItemsInfo,
  itemsMode,
  CardInfo,
} from '../components/flex-4items-box'
import ShowMore from '../components/show-more'
import { getProductDatas } from '@/data/products'
import { getI18n, getStaticParams, getCurrentLocale } from '@/locales/server'

export default async function HotSpot() {
  const t = await getI18n()

  return (
    <div>
      {/* <div className={styles[`hot-spot`]}>
        <Captain title="新闻资讯" link="/news" />
        <News />
      </div> */}
      <div className={styles[`hot-spot`]}>
        <Captain title={t('productCenter')} link="/product" />
        <Products />
      </div>
      <div className={styles[`hot-spot`]}>
        <Captain title={t('prodoctVideos')} />
        <Videos />
      </div>
    </div>
  )
}

async function Captain({ title, link }: { title: string; link?: string }) {
  const t = await getI18n()

  return (
    <div className={styles['hot-spot__captain']}>
      <h2>{title}</h2>
      {link && (
        <Link
          href={link}
          className={`${styles['hot-spot__captain__more']} hide-on-small`}
        >
          {t('seeMore')}
          <Image
            src={'/arrow-right.svg'}
            width={10}
            height={17}
            alt="arrow"
          ></Image>
        </Link>
      )}
    </div>
  )
}

async function News() {
  const t = await getI18n()
  const ItemInfo = ({
    title,
    content,
    time,
    isRight = false,
  }: {
    title: string
    content: string
    time: string
    isRight?: boolean
  }) => {
    return (
      <div className={styles[`hot-spot__news__item__info`]}>
        <h4>{title}</h4>
        <div>{content}</div>
        {isRight && <div className="hide-on-small" style={{ flex: 1 }}></div>}
        <span>{time}</span>
      </div>
    )
  }

  const NewsItem = ({
    title,
    content,
    time,
    isRight = false,
    id,
  }: {
    title: string
    content: string
    time: string
    isRight?: boolean
    id: string
  }) => {
    return (
      <Link
        href={`/article/${id}`}
        className={`${styles['hot-spot__news__item']} ${
          isRight ? '' : styles['hot-spot__news__item--left']
        }`}
      >
        <div
          className={`${styles['hot-spot__news__item__image']} ${
            styles[`hot-spot__news__item__image--${isRight ? 'right' : 'left'}`]
          }`}
        >
          <Image
            src={'/hotspot-image-1.png'}
            width={isRight ? 240 : 625}
            height={isRight ? 160 : 240}
            alt="news"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center',
            }}
          ></Image>
        </div>

        <ItemInfo
          title={title}
          content={content}
          time={time}
          isRight={isRight}
        />
      </Link>
    )
  }

  const newsItemsRes = await getNewsDatas()

  const newsItems = newsItemsRes
    .filter((_, index) => index < 3)
    .map((news, index) => {
      type item = {
        title: string
        content: string
        time: string
        id: string
      }
      type itemRight = item & {
        isRight: true
      }
      const itemLeft: item = {
        title: news.title,
        content: news.tip,
        time: formatDate(news.time, 'YYYY-MM-DD'),
        id: news.id,
      }
      const itemRight: itemRight = {
        ...itemLeft,
        isRight: true,
      }
      if (index === 0) return itemLeft
      else return itemRight
    })

  return (
    <>
      <div className={styles['hot-spot__news']}>
        <div className={styles['hot-spot__news__left']}>
          <NewsItem {...newsItems[0]} />
        </div>
        <div className={styles['hot-spot__news__right']}>
          <NewsItem {...newsItems[1]} />
          <NewsItem {...newsItems[2]} />
        </div>
      </div>
      <ShowMore text="查看更多新闻" link="/news" />
    </>
  )
}

async function Products() {
  const locale = getCurrentLocale()

  const productDatas = await getProductDatas()
  const infos: Flex4ItemsInfo = productDatas
    .filter((item, index) => ['c31','02','04','07'].includes(item.id))
    .map((item) => {
      const product: CardInfo = {
        imageSrc: item.imageSrc,
        title: locale === 'zh' ? item.name : item.nameEn,
        tip: locale === 'zh' ? item.description : item.descriptionEn,
        link: item.id === 'c31' ? `/product/c31` : '',
      }
      return product
    })

  const ProductsInfo: {
    infos: Flex4ItemsInfo
    imageSize: {
      width: number
      height: number
    }
    imageBox: {
      width: number
      height: number
    }
    mode: itemsMode
  } = {
    infos,
    imageSize: {
      width: 200,
      height: 200,
    },
    imageBox: {
      width: 300,
      height: 300,
    },
    mode: itemsMode.product,
  }

  return (
    <>
      <Flex4ItemsBox {...ProductsInfo} />
      <ShowMore text="查看更多产品" link="/product" />
    </>
  )
}

async function Videos() {
  const t = await getI18n()
  const locale = getCurrentLocale()

  const VideosInfo: {
    infos: Flex4ItemsInfo
    imageSize: {
      width: number
      height: number
    }
    imageBox: {
      width: number
      height: number
    }
    mode: itemsMode
  } = {
    infos: [
      {
        imageSrc: '/videos/phone-call-device-cover.webp',
        title:
          locale === 'zh'
            ? '双向视频通话演示'
            : 'Two-Way Video Call Demonstration',
        videoSrc: '/videos/device-call-phone-video1.mp4',
      },
      {
        imageSrc: '/videos/tx-video-call-cover.webp',
        title:
          locale === 'zh'
            ? '腾讯云音视频微通话（TWeCall）设备通话演示'
            : 'Tencent Cloud Audio and Video WeCall (TWeCall) Device Call Demonstration',
        videoSrc: '/videos/wx-twecall-video1.mp4',
      },
      {
        imageSrc: '/videos/voice-wakeup.webp',
        title: locale === 'zh' ? '语音唤醒演示' : 'Voice Wake-up Demonstration',
        videoSrc:
          locale === 'zh'
            ? '/videos/voice-wakeup-video1.mp4'
            : '/videos/voice-wakeup-en-video1.mp4',
      },
      {
        imageSrc: '/videos/device-call-device.webp',
        title:
          locale === 'zh'
            ? '设备与设备通话演示'
            : 'Device-to-Device Call Demonstration',
        videoSrc: '/videos/device-call-device-video1.mp4',
      },
    ],
    imageSize: {
      width: 300,
      height: 168,
    },
    imageBox: {
      width: 300,
      height: 168,
    },
    mode: itemsMode.normal,
  }

  return (
    <>
      <Flex4ItemsBox {...VideosInfo} />
      {/* <ShowMore text="查看更多视频" /> */}
    </>
  )
}
