"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(overview)/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/home/<USER>":
/*!************************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BannerSlider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./home.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BannerSlider() {\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale)();\n    const images = [\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>",\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>"\n    ];\n    // 自动播放功能\n    const startAutoPlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (images.length <= 1) return;\n        autoPlayRef.current = setInterval(()=>{\n            if (!isPaused) {\n                setCurrentIndex((prev)=>(prev + 1) % images.length);\n            }\n        }, 5000) // 5秒切换一次\n        ;\n    }, [\n        images.length,\n        isPaused\n    ]);\n    const stopAutoPlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (autoPlayRef.current) {\n            clearInterval(autoPlayRef.current);\n            autoPlayRef.current = null;\n        }\n    }, []);\n    // 切换到指定索引\n    const goToSlide = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((index)=>{\n        if (index === currentIndex || isTransitioning) return;\n        setCurrentIndex(index);\n    }, [\n        currentIndex,\n        isTransitioning\n    ]);\n    // 上一张\n    const goToPrev = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isTransitioning) return;\n        const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;\n        goToSlide(newIndex);\n    }, [\n        currentIndex,\n        images.length,\n        isTransitioning,\n        goToSlide\n    ]);\n    // 下一张\n    const goToNext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isTransitioning) return;\n        const newIndex = (currentIndex + 1) % images.length;\n        goToSlide(newIndex);\n    }, [\n        currentIndex,\n        images.length,\n        isTransitioning,\n        goToSlide\n    ]);\n    // 触摸事件处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleTouchStart = (e)=>{\n        setTouchStart({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY\n        });\n        setIsPaused(true) // 暂停自动播放\n        ;\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY\n        });\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const deltaX = touchEnd.x - touchStart.x;\n        const deltaY = touchEnd.y - touchStart.y;\n        const minSwipeDistance = 50;\n        // 判断是否为水平滑动\n        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {\n            if (deltaX > 0) {\n                goToPrev();\n            } else {\n                goToNext();\n            }\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n        setIsPaused(false) // 恢复自动播放\n        ;\n    };\n    // 鼠标事件处理\n    const handleMouseEnter = ()=>setIsPaused(true);\n    const handleMouseLeave = ()=>setIsPaused(false);\n    // 生命周期管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        startAutoPlay();\n        return ()=>stopAutoPlay();\n    }, [\n        startAutoPlay,\n        stopAutoPlay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isPaused) {\n            stopAutoPlay();\n        } else {\n            startAutoPlay();\n        }\n    }, [\n        isPaused,\n        startAutoPlay,\n        stopAutoPlay\n    ]);\n    // 过渡动画控制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTransitioning(true);\n        const timer = setTimeout(()=>setIsTransitioning(false), 300);\n        return ()=>clearTimeout(timer);\n    }, [\n        currentIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider\"]),\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: containerRef,\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__list\"]),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                style: {\n                    transform: \"translateX(-\".concat(currentIndex * 100, \"%)\"),\n                    transition: isTransitioning ? \"transform 0.3s ease-in-out\" : \"none\"\n                },\n                children: images.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__slide\"]),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: item,\n                            width: 1920,\n                            height: 500,\n                            alt: \"Banner \".concat(index + 1),\n                            unoptimized: true,\n                            style: {\n                                width: \"100%\",\n                                height: \"100%\",\n                                objectFit: \"cover\",\n                                objectPosition: \"center\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switcher, {\n                        position: \"left\",\n                        disabled: currentIndex === 0,\n                        onClick: goToPrev\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switcher, {\n                        position: \"right\",\n                        disabled: currentIndex === images.length - 1,\n                        onClick: goToNext\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Indicator, {\n                        count: images.length,\n                        currentIndex: currentIndex,\n                        onIndicatorClick: goToSlide\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(BannerSlider, \"42xz7GIKgMnLmVHtnJ3qRJYf70o=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale\n    ];\n});\n_c = BannerSlider;\nfunction Swicher(param) {\n    let { position = \"left\", disabled = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: ()=>onClick(),\n        className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"banner-slider__switcher\".concat(position === \"left\" ? \"\" : \"--right\")], \" \").concat(disabled ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__switcher--disabled\"]) : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: \"/slider-\".concat(position === \"left\" ? \"left\" : \"right\", \".svg\"),\n            width: 44,\n            height: 44,\n            alt: \"\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Swicher;\nfunction Indicator(param) {\n    let { count, currentIndex } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator\"]),\n            children: Array(count).fill(\"\").map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item\"]), \" \").concat(index === currentIndex ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item--active\"]) : \"\")\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n            lineNumber: 221,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_c2 = Indicator;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"BannerSlider\");\n$RefreshReg$(_c1, \"Swicher\");\n$RefreshReg$(_c2, \"Indicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/home/<USER>"));

/***/ })

});