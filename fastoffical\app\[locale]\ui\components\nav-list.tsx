'use client'
import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'
import { useI18n, useCurrentLocale, I18nProviderClient } from '@/locales/client'

type NavType = {
  iconSrc?: string
  title: string
  link: string
  id: number
}

type NavItemWithSubNavs = {
  iconSrc?: never
  title?: never
  subNavs: NavType[]
  link?: never
  id?: never
  active?: never
}
type NavItemWithoutSubNavsBase = {
  iconSrc: string
  title: string
  subNavs?: never
  id: number
}
type NavItemWithoutSubNavsNoLink = NavItemWithoutSubNavsBase & {
  active: boolean
  link?: never
}
type NavItemWithoutSubNavsWithLink = NavItemWithoutSubNavsBase & {
  link: string
  active?: never
}
type NavItemWithoutSubNavs =
  | NavItemWithoutSubNavsNoLink
  | NavItemWithoutSubNavsWithLink

type NavItemProps = NavItemWithSubNavs | NavItemWithoutSubNavs

export default function NavListLayout({
  isFooter = false,
  onClick = () => {},
}: {
  isFooter?: boolean
  onClick?: Function
}) {
  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <NavList isFooter={isFooter} onClick={onClick} />
    </I18nProviderClient>
  )
}

function NavList({
  isFooter = false,
  onClick,
}: {
  isFooter?: boolean
  onClick?: Function
}) {
  const t = useI18n()

  const _items = [
    {
      iconSrc: '/menu-home-icon.svg',
      title: t('home'),
      id: 0,
      link: '/',
    },
    {
      iconSrc: '/menu-product-icon.svg',
      title: t('productCenter'),
      id: 1,
      active: false,
    },
    {
      subNavs: [{ title: t('productCamera'), link: '/product', id: 1 }],
    },
    // {
    //   iconSrc: '/menu-videos-icon.svg',
    //   title: '产品视频',
    //   link: '/videos',
    //   id: 2,
    // },
    {
      iconSrc: '/menu-support-icon.svg',
      title: t('support'),
      link: '/support',
      id: 3,
    },
    // {
    //   iconSrc: '/menu-news-icon.svg',
    //   title: '新闻资讯',
    //   link: '/news',
    //   id: 4,
    // },
    {
      iconSrc: '/menu-about-icon.svg',
      title: t('aboutCylan'),
      link: '/about',
      id: 5,
    },
  ]
  const [items, setItems] = useState(_items)

  const handleClick = (id: number, isLink: boolean) => {
    if (isLink) {
      onClick && onClick()
      return
    }
    const newItems = [...items]
    newItems.forEach((item) => {
      if (item.id === id) item.active = !item.active
    })
    setItems(newItems)
  }

  return (
    <div className={`nav-list ${isFooter ? 'nav-list--footer' : ''}`}>
      {items.map((item, index) => (
        <NavItem
          key={index}
          navItemPros={item}
          navItemStatus={items}
          onClick={handleClick}
        />
      ))}
    </div>
  )
}

function NavItem({
  navItemPros,
  navItemStatus,
  onClick,
}: {
  navItemPros: NavItemProps
  navItemStatus: Array<NavItemProps>
  onClick: Function
}) {
  if (navItemPros.subNavs) {
    const subNavs = navItemPros.subNavs
    const id = subNavs[0].id
    const mainItem = navItemStatus.find((item) => item?.id === id)

    const handleClick = () => {
      const isLink = true
      onClick(id, isLink)
    }
    return (
      <>
        {mainItem?.active &&
          subNavs.map((item, index) => (
            <Link href={item.link} key={index} onClick={handleClick}>
              <div className="nav-list__item nav-list__item--sub">
                <div className="nav-list__item__icon"></div>
                <span className="nav-list__item__title">{item.title}</span>
              </div>
            </Link>
          ))}
      </>
    )
  } else {
    const { iconSrc, title, link, id, active } = navItemPros

    const handleClick = () => {
      const isLink = !!link
      onClick(id, isLink)
    }

    const Item = () => (
      <>
        <div className="nav-list__item__icon">
          {iconSrc && (
            <Image src={iconSrc} width={24} height={24} alt=""></Image>
          )}
        </div>
        <span className="nav-list__item__title">{title}</span>
        <div style={{ flex: 1 }}></div>
        {!link && (
          <div className="nav-list__item__button">
            <Image
              src={`/menu-arrow-${active ? 'up' : 'down'}.svg`}
              width={32}
              height={32}
              alt=""
            ></Image>
          </div>
        )}
      </>
    )

    return (
      <>
        <div onClick={handleClick} className="nav-list__item">
          {!link && <Item />}
          {link && (
            <Link
              style={{ width: '100%', height: '100%' }}
              className="nav-list__item"
              href={link}
            >
              <Item />
            </Link>
          )}
        </div>
      </>
    )
  }
}
