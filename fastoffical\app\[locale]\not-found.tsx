import styles from '@/app/[locale]/page.module.scss'
import Image from 'next/image'
import { getI18n } from '@/locales/server'
import Link from 'next/link'

export default async function NotFound() {
  const t = await getI18n()

  return (
    <div className={styles.page404container}>
      <div className={styles.page404}>
        <div className={styles.page404__image}>
          <Image src={'/404.png'} width={331} height={151} alt=""></Image>
        </div>
        <div className={styles.page404__content}>
          <p>{t('page404Description')}</p>
          <span>{t('page404Tip1')}</span>
          <span>{t('page404Tip2')}</span>
        </div>
        <div className={styles.page404__buttons}>
          <Link href={'/'}>
            <button className={styles.page404__buttons__home}>
              {t('backToHome')}
            </button>
          </Link>
        </div>
      </div>
    </div>
  )
}
