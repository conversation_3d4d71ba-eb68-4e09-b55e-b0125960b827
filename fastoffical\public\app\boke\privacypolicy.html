<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <title>隐私政策</title>
  <script>
    window.onload = function () {
      var jsSrc = (navigator.language || navigator.browserLanguage).toLowerCase();
      if (jsSrc.indexOf('zh') != -1) {
        // 浏览器语言是中文
        window.location = './privacypolicy_cn.html'
      } else if (jsSrc.indexOf('en') != -1) {
        // 浏览器语言是英文
        window.location = './privacypolicy_en.html'
      } else {
        // 浏览器语言是其它
        window.location = './privacypolicy_en.html'
      }
    }
  </script>
</head>

<body>

</body>

</html>