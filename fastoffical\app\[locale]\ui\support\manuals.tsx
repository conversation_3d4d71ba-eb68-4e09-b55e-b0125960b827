'use client'
import styles from './support.module.scss'
import Image from 'next/image'
import Pagination from '../components/pagination'
import DropdownWindow from '../components/dropdown-window'
import { useState } from 'react'

export default function Manuals() {
  return (
    <div className={styles.manuals}>
      <Captain />
      <List />
      <ListPagination />
    </div>
  )
}

function Captain() {
  const navs = [
    {
      id: '0',
      text: '全部',
    },
    {
      id: '1',
      text: '产品分类1',
    },
    {
      id: '2',
      text: '产品分类2',
    },
    {
      id: '3',
      text: '产品分类3',
    },
  ]
  const currentNav = '0'
  let currentLanguage = 0

  const Navs = () => {
    const [isShowMenu, setShowMenu] = useState(false)

    const handleCloseMenu = () => {
      setShowMenu(false)
    }

    const handleShowMenu = () => {
      setShowMenu(true)
    }

    return (
      <>
        <div className={`${styles.manuals__captain__navs} hide-on-small`}>
          {navs.map((item, index) => (
            <button
              key={index}
              className={`${styles.manuals__captain__navs__button} ${
                currentNav === item.id
                  ? styles['manuals__captain__navs__button--active']
                  : ''
              }`}
            >
              {item.text}
            </button>
          ))}
        </div>
        <div
          className={`${styles.manuals__captain__menu} hide-on-medium hide-on-large`}
        >
          <DropdownWindow
            show={isShowMenu}
            list={navs}
            onClick={handleCloseMenu}
            onClickMask={handleCloseMenu}
          >
            <div
              onClick={handleShowMenu}
              className={styles.manuals__captain__menu__item}
            >
              全部
              <Image
                src={'/arrow-down.svg'}
                width={16}
                height={10}
                alt=""
              ></Image>
            </div>
          </DropdownWindow>
        </div>
      </>
    )
  }

  const Languages = () => {
    return (
      <div className={styles.manuals__captain__languages}>
        <button
          className={`${
            currentLanguage === 0
              ? styles['manuals__captain__languages__item--active']
              : ''
          } ${styles.manuals__captain__languages__item}`}
        >
          中文版
        </button>
        <span>/</span>
        <button
          className={`${
            currentLanguage === 1
              ? styles['manuals__captain__languages__item--active']
              : ''
          } ${styles.manuals__captain__languages__item}`}
        >
          英文版
        </button>
      </div>
    )
  }

  return (
    <div className={styles.manuals__captain}>
      <Navs></Navs>
      <Languages />
    </div>
  )
}

function List() {
  const items = [
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
    {
      imageSrc: '/product/product-c31.jpg',
      text: '看家王C31说明书中文版-V4版本',
    },
  ]

  const Item = ({ imageSrc, text }: { imageSrc: string; text: string }) => {
    return (
      <div className={styles.manuals__list__item}>
        <div className={styles.manuals__list__item__image}>
          <Image src={imageSrc} width={40} height={50} alt=""></Image>
        </div>
        <span>{text}</span>
        <Image src={'/download.svg'} width={36} height={36} alt=""></Image>
      </div>
    )
  }

  return (
    <div className={styles.manuals__list}>
      {items.map((item, index) => (
        <Item key={index} {...item}></Item>
      ))}
    </div>
  )
}

function ListPagination() {
  return (
    <div className={styles.manuals__pagination}>
      <Pagination count={27} currentPage={1}></Pagination>
    </div>
  )
}
