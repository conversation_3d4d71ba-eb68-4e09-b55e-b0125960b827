{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/about", "regex": "^/([^/]+?)/about(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/about(?:/)?$"}, {"page": "/[locale]/article/[articleId]", "regex": "^/([^/]+?)/article/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtParticleId": "nxtParticleId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/article/(?<nxtParticleId>[^/]+?)(?:/)?$"}, {"page": "/[locale]/news", "regex": "^/([^/]+?)/news(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/news(?:/)?$"}, {"page": "/[locale]/product", "regex": "^/([^/]+?)/product(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/product(?:/)?$"}, {"page": "/[locale]/product/c31", "regex": "^/([^/]+?)/product/c31(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/product/c31(?:/)?$"}, {"page": "/[locale]/product/product-detail/[productId]", "regex": "^/([^/]+?)/product/product\\-detail/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPproductId": "nxtPproductId"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/product/product\\-detail/(?<nxtPproductId>[^/]+?)(?:/)?$"}, {"page": "/[locale]/product/t1pro", "regex": "^/([^/]+?)/product/t1pro(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/product/t1pro(?:/)?$"}, {"page": "/[locale]/support", "regex": "^/([^/]+?)/support(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/support(?:/)?$"}, {"page": "/[locale]/support/[type]", "regex": "^/([^/]+?)/support/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPtype": "nxtPtype"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/support/(?<nxtPtype>[^/]+?)(?:/)?$"}, {"page": "/[locale]/videos", "regex": "^/([^/]+?)/videos(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/videos(?:/)?$"}, {"page": "/[locale]/[...rest]", "regex": "^/([^/]+?)/(.+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPrest": "nxtPrest"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/(?<nxtPrest>.+?)(?:/)?$"}], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}