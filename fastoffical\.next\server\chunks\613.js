exports.id=613,exports.ids=[613],exports.modules={7621:(e,t,_)=>{Promise.resolve().then(_.t.bind(_,1900,23)),Promise.resolve().then(_.t.bind(_,1476,23))},9676:(e,t,_)=>{Promise.resolve().then(_.bind(_,6136)),Promise.resolve().then(_.bind(_,5049)),Promise.resolve().then(_.bind(_,899))},9162:(e,t,_)=>{Promise.resolve().then(_.bind(_,3211))},5113:(e,t,_)=>{Promise.resolve().then(_.t.bind(_,2583,23)),Promise.resolve().then(_.t.bind(_,6840,23)),Promise.resolve().then(_.t.bind(_,8771,23)),Promise.resolve().then(_.t.bind(_,3225,23)),Promise.resolve().then(_.t.bind(_,9295,23)),Promise.resolve().then(_.t.bind(_,3982,23))},5303:()=>{},6136:(e,t,_)=>{"use strict";_.r(t),_.d(t,{default:()=>a});var n=_(5344),i=_(9410),s=_(3729);_(4507);var o=_(2716);function a(){return n.jsx(o.e4,{locale:(0,o.eV)(),children:n.jsx(l,{})})}function l(){let e=(0,o.QT)(),t=(0,o.eV)(),[_,a]=(0,s.useState)(!1),l=null,r=!1;return(0,s.useEffect)(()=>{let e=()=>{a((window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop)>window.innerHeight)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]),(0,n.jsxs)("button",{className:`back-top ${_?"":"back-top--hide"}`,onClick:()=>{r||(r=!0,l&&clearTimeout(l),l=setTimeout(()=>{window.scrollTo({top:0,behavior:"smooth"}),r=!1},100))},children:[n.jsx(i.default,{src:"/back-top.svg",width:26,height:26,alt:"Top"}),"zh"===t&&n.jsx("span",{className:"hide-on-small",children:e("backToTop")})]})}},2326:(e,t,_)=>{"use strict";_.d(t,{Z:()=>o});var n=_(5344),i=_(9410),s=_(6506);function o({children:e,list:t,show:_=!1,onClick:o=()=>{},onClickMask:a=()=>{}}){let l=e=>{o(e)};return(0,n.jsxs)("div",{style:{position:"relative"},children:[e,_&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"dropdown-window",children:[n.jsx("div",{className:"dropdown-window__above",children:n.jsx(i.default,{src:"/dropdown-window-above.svg",width:15,height:8,alt:"drop"})}),n.jsx("div",{className:"dropdown-window__list",children:t.map((e,t)=>n.jsx("div",{children:e.link?n.jsx(s.default,{href:e.link,children:n.jsx("div",{className:"dropdown-window__list__item",onClick:()=>{l(e.link)},children:e.text})}):n.jsx("div",{className:"dropdown-window__list__item",onClick:()=>{l(e?.id)},children:e.text})},t))}),n.jsx("div",{className:"dropdown-window__placeholder"})]}),n.jsx("div",{className:"dropdown-window__mask hide-on-medium hide-on-large",onClick:()=>{a()}})]})]})}},1217:(e,t,_)=>{"use strict";_.d(t,{Z:()=>l});var n=_(5344),i=_(6506),s=_(9410),o=_(3729),a=_(2716);function l({isFooter:e=!1,onClick:t=()=>{}}){return n.jsx(a.e4,{locale:(0,a.eV)(),children:n.jsx(r,{isFooter:e,onClick:t})})}function r({isFooter:e=!1,onClick:t}){let _=(0,a.QT)(),i=[{iconSrc:"/menu-home-icon.svg",title:_("home"),id:0,link:"/"},{iconSrc:"/menu-product-icon.svg",title:_("productCenter"),id:1,active:!1},{subNavs:[{title:_("productCamera"),link:"/product",id:1}]},{iconSrc:"/menu-support-icon.svg",title:_("support"),link:"/support",id:3},{iconSrc:"/menu-about-icon.svg",title:_("aboutCylan"),link:"/about",id:5}],[s,l]=(0,o.useState)(i),r=(e,_)=>{if(_){t&&t();return}let n=[...s];n.forEach(t=>{t.id===e&&(t.active=!t.active)}),l(n)};return n.jsx("div",{className:`nav-list ${e?"nav-list--footer":""}`,children:s.map((e,t)=>n.jsx(c,{navItemPros:e,navItemStatus:s,onClick:r},t))})}function c({navItemPros:e,navItemStatus:t,onClick:_}){if(e.subNavs){let s=e.subNavs,o=s[0].id,a=t.find(e=>e?.id===o),l=()=>{_(o,!0)};return n.jsx(n.Fragment,{children:a?.active&&s.map((e,t)=>n.jsx(i.default,{href:e.link,onClick:l,children:(0,n.jsxs)("div",{className:"nav-list__item nav-list__item--sub",children:[n.jsx("div",{className:"nav-list__item__icon"}),n.jsx("span",{className:"nav-list__item__title",children:e.title})]})},t))})}{let{iconSrc:t,title:o,link:a,id:l,active:r}=e,c=()=>(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"nav-list__item__icon",children:t&&n.jsx(s.default,{src:t,width:24,height:24,alt:""})}),n.jsx("span",{className:"nav-list__item__title",children:o}),n.jsx("div",{style:{flex:1}}),!a&&n.jsx("div",{className:"nav-list__item__button",children:n.jsx(s.default,{src:`/menu-arrow-${r?"up":"down"}.svg`,width:32,height:32,alt:""})})]});return n.jsx(n.Fragment,{children:(0,n.jsxs)("div",{onClick:()=>{_(l,!!a)},className:"nav-list__item",children:[!a&&n.jsx(c,{}),a&&n.jsx(i.default,{style:{width:"100%",height:"100%"},className:"nav-list__item",href:a,children:n.jsx(c,{})})]})})}}},5049:(e,t,_)=>{"use strict";_.r(t),_.d(t,{default:()=>h});var n=_(5344),i=_(3729),s=_.n(i),o=_(5620),a=_.n(o),l=_(9410),r=_(6506),c=_(1217),d=_(2716);function h(){return n.jsx(d.e4,{locale:(0,d.eV)(),children:n.jsx(m,{})})}function m(){let e=(0,d.QT)(),t=({link:e,text:t})=>n.jsx("a",{href:e,children:n.jsx("span",{children:t})}),_=({title:e,links:_})=>(0,n.jsxs)("div",{className:a().footer__links__item,children:[n.jsx("div",{children:e}),_.map((e,_)=>n.jsx(s().Fragment,{children:t({link:e.link,text:e.text})},`${e.link}-${_}`))]}),o=[{link:"/product?tab=01",text:e("productCamera")},{link:"/product?tab=02",text:e("productTranslator")}],h=[{link:"/support/download_client",text:e("downloadClient")},{link:"/support/help",text:e("help")}],m=[{link:"/about#about-cylan",text:e("aboutCylan")},{link:"/about#prides",text:e("cylanPrides")},{link:"/about#contacts",text:e("contactUs")}],u=()=>{let[t,_]=(0,i.useState)(!1);return(0,n.jsxs)("div",{className:a().footer__links__follow,children:[n.jsx("div",{children:e("followUs")}),(0,n.jsxs)("div",{onMouseEnter:()=>{_(!0)},onMouseLeave:()=>{_(!1)},className:a().footer__links__follow__weixin,children:[n.jsx(l.default,{src:"/weixin.svg",width:20,height:20,alt:"weixin"}),t&&(0,n.jsxs)("div",{children:[n.jsx(l.default,{src:"/support/imcam-gongzhonghao.jpg",width:140,height:140,alt:""}),n.jsx("span",{children:e("imcamGongzhonghao")}),n.jsx("a",{className:"hide-on-medium hide-on-large",href:"/support/imcam-gongzhonghao.jpg",download:!0,children:e("downloadQRcode")})]})]})]})};return(0,n.jsxs)("div",{className:a().footer,children:[n.jsx("div",{className:`${a().footer__logo} hide-on-medium hide-on-large`,children:n.jsx(r.default,{href:"/",children:n.jsx(l.default,{src:"/cylan_logo-white.png",width:125,height:44,alt:"logo",unoptimized:!0})})}),(0,n.jsxs)("div",{className:`${a().footer__links} hide-on-small`,children:[n.jsx("div",{className:`${a().footer__logo}`,children:n.jsx(r.default,{href:"/",children:n.jsx(l.default,{src:"/cylan_logo-white.png",width:125,height:44,alt:"logo",unoptimized:!0})})}),n.jsx(_,{title:e("productCenter"),links:o}),n.jsx(_,{title:e("support"),links:h}),n.jsx(_,{title:e("aboutUs"),links:m}),n.jsx(u,{})]}),(0,n.jsxs)("div",{className:"hide-on-large hide-on-medium",children:[n.jsx(c.Z,{isFooter:!0}),n.jsx(u,{})]}),(0,n.jsxs)("div",{className:a().footer__copyright,children:[e("copyrightText"),n.jsx(r.default,{className:a().footer__copyright__link,href:"https://beian.miit.gov.cn/",target:"_blank",children:e("copyrightLink")})]})]})}},899:(e,t,_)=>{"use strict";_.r(t),_.d(t,{default:()=>m});var n=_(5344),i=_(5620),s=_.n(i),o=_(6506),a=_(9410),l=_(2326),r=_(3729),c=_(8428),d=_(1217),h=_(2716);function m(){return"__DEFAULT__"===(0,c.useSelectedLayoutSegment)()?n.jsx("div",{}):n.jsx(h.e4,{locale:(0,h.eV)(),children:n.jsx(u,{})})}function u(){let e=(0,h.QT)(),[t,_]=(0,r.useState)(!1),[i,l]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{_(window.scrollY>0)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]);let c=()=>{l(!1)};return(0,n.jsxs)("div",{style:{position:"relative"},children:[n.jsx("div",{className:s().nav__placeholder}),n.jsx("div",{className:`${s().nav} ${t?s()["nav--scrolled"]:""} ${i?s()["nav--scrolled"]:""}`,children:(0,n.jsxs)("div",{className:s().nav__content,children:[n.jsx(o.default,{href:"/",style:{height:44},children:n.jsx(a.default,{src:"/cylan_logo.png",width:125,height:44,alt:"Cylan Logo",unoptimized:!0})}),(0,n.jsxs)("div",{className:`${s().nav__list} hide-on-small hide-on-medium`,children:[n.jsx(v,{title:e("home"),link:"/"}),n.jsx(v,{title:e("productCenter"),showArrow:!0,link:"/product",links:[{link:"/product?tab=01",text:e("productCamera")},{link:"/product?tab=02",text:e("productTranslator")}]}),n.jsx(v,{title:e("support"),showArrow:!0,link:"/support",links:[{link:"/support/download_client",text:e("downloadClient")},{link:"/support/help",text:e("help")}]}),n.jsx(v,{title:e("aboutUs"),link:"/about"})]}),(0,n.jsxs)("div",{className:s().nav__right,children:[n.jsx(x,{}),n.jsx("button",{onClick:()=>{l(!i)},className:`${s().nav__right__menu} hide-on-large`,children:n.jsx(a.default,{src:"/menu.svg",width:49,height:50,alt:"menu"})})]})]})}),i&&(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:`${s().nav__drop} hide-on-large`,children:n.jsx(d.Z,{onClick:()=>c()})}),n.jsx("div",{className:s().nav__mask,onClick:()=>{l(!1)}})]})]})}function p(){return n.jsx(a.default,{src:"/arrow-down.svg",height:10,width:16,alt:""})}function v({title:e,link:t="/",showArrow:_=!1,links:i=[]}){let[a,d]=(0,r.useState)(!1),h=(0,c.usePathname)();return h="/"+(h=h.includes("/zh/")||h.includes("/en/")?h.replace(/\/zh|\/en/,""):"/").split("/")[1],n.jsx("div",{onMouseEnter:()=>{d(!0)},onMouseLeave:()=>{d(!1)},children:_?n.jsx(l.Z,{onClick:()=>d(!1),list:i,show:a,children:n.jsx(o.default,{href:t,children:(0,n.jsxs)("div",{className:`${s().nav__list__item} ${h===t.split("?")[0]?s()["nav__list__item--active"]:""}`,children:[e," ",_&&n.jsx(p,{})]})})}):n.jsx(o.default,{href:t,children:(0,n.jsxs)("div",{className:`${s().nav__list__item} ${s()["nav__list__item--link"]} ${h===`/${t.split("/")[1]}`?s()["nav__list__item--active"]:""}`,children:[e," ",_&&n.jsx(p,{})]})})})}function x(){let e=(0,h.Zt)(),t=(0,h.eV)(),_=(0,h.QT)(),i=({lang:t,isActive:i=!1})=>n.jsx("span",{onClick:()=>{i||e(t)},className:`${i?s()["nav__right__language__text--active"]:""} ${s().nav__right__language__text}`,children:"zh"===t?_("chinese"):_("english")});return(0,n.jsxs)("div",{className:s().nav__right__language,children:[n.jsx(i,{lang:"zh",isActive:"zh"===t}),n.jsx("span",{children:"/"}),n.jsx(i,{lang:"en",isActive:"en"===t})]})}},3211:(e,t,_)=>{"use strict";_.r(t),_.d(t,{default:()=>o});var n=_(5344),i=_(6886),s=_.n(i);function o(){return n.jsx("html",{lang:"en",children:n.jsx("body",{children:n.jsx(s(),{statusCode:404})})})}},2716:(e,t,_)=>{"use strict";_.d(t,{QT:()=>n,Zt:()=>a,e4:()=>s,eV:()=>o});let{useI18n:n,useScopedI18n:i,I18nProviderClient:s,useCurrentLocale:o,useChangeLocale:a}=(0,_(2930).createI18nClient)({en:()=>_.e(547).then(_.bind(_,1547)),zh:()=>_.e(849).then(_.bind(_,8849))})},2297:e=>{e.exports={page404container:"page_page404container__c1LIB",page404:"page_page404__1cE3u",page404__content:"page_page404__content__oco9m",page404__buttons:"page_page404__buttons__8oWnx",page404__buttons__back:"page_page404__buttons__back__z0MU7",page404__buttons__home:"page_page404__buttons__home__sx9Sv"}},5620:e=>{e.exports={"nav--scrolled":"home_nav--scrolled__f5oaX",nav:"home_nav__gr65i",nav__placeholder:"home_nav__placeholder__R_bDj",nav__content:"home_nav__content__gXoig",nav__list:"home_nav__list__dmRBz",nav__list__item:"home_nav__list__item__Ti9E4","nav__list__item--link":"home_nav__list__item--link__dx88I","nav__list__item--active":"home_nav__list__item--active__oPRJX",nav__right:"home_nav__right__4GRXj",nav__right__language:"home_nav__right__language__YzU4O",nav__right__language__text:"home_nav__right__language__text__yUNmB","nav__right__language__text--active":"home_nav__right__language__text--active__e4h1y",nav__right__search:"home_nav__right__search__QAvd_",nav__right__menu:"home_nav__right__menu__tMG4s",nav__drop:"home_nav__drop__RNd3y",nav__mask:"home_nav__mask__YVj5E","banner-slider":"home_banner-slider__UBj9I","banner-slider__swiper":"home_banner-slider__swiper__9Bl8q","banner-slider__slide":"home_banner-slider__slide__2U7Uu","banner-slider__button":"home_banner-slider__button__GKjGy","swiper-button-disabled":"home_swiper-button-disabled__siaDk","banner-slider__button-prev":"home_banner-slider__button-prev__VeikB","banner-slider__button-next":"home_banner-slider__button-next__UC5d2","banner-slider__pagination":"home_banner-slider__pagination__J58et","banner-slider__bullet":"home_banner-slider__bullet__c3a9X","banner-slider__bullet--active":"home_banner-slider__bullet--active__5BpSZ","banner-slider__switcher":"home_banner-slider__switcher__SoaxS","banner-slider__switcher--right":"home_banner-slider__switcher--right___84yN","banner-slider__indicator":"home_banner-slider__indicator__0OOU4","banner-slider__indicator__item":"home_banner-slider__indicator__item__f8vBh","hot-spot":"home_hot-spot__HmXBc","hot-spot__captain":"home_hot-spot__captain__P7sAg","hot-spot__captain__more":"home_hot-spot__captain__more__hoe30","hot-spot__news":"home_hot-spot__news__mFPbX","hot-spot__news__left":"home_hot-spot__news__left__bYNbF","hot-spot__news__right":"home_hot-spot__news__right__IYxxG","hot-spot__news__item":"home_hot-spot__news__item__i6svw","hot-spot__news__item__info":"home_hot-spot__news__item__info__GSDkz","hot-spot__news__item__image":"home_hot-spot__news__item__image__0Dj0A","hot-spot__news__item__image--right":"home_hot-spot__news__item__image--right__scey9","hot-spot__news__item--left":"home_hot-spot__news__item--left__W7YL9",about:"home_about__vPbFi",about__cover:"home_about__cover__SPvuD",about__content:"home_about__content__EA9EW",about__content__time:"home_about__content__time__HcHq6",about__content__time__item:"home_about__content__time__item__n4W8C","about__content__time--page":"home_about__content__time--page__Azkeq",about__content__prides:"home_about__content__prides__zHCpT",contacts:"home_contacts__TRH4N","contacts--page":"home_contacts--page__0BV0w",contacts__info:"home_contacts__info__pIGy0",contacts__info__items:"home_contacts__info__items__qUSi9",contacts__info__title:"home_contacts__info__title__3_UHT",contacts__info__item:"home_contacts__info__item__eDIm0",contacts__address:"home_contacts__address___ZQdr",footer:"home_footer__qefFZ",footer__logo:"home_footer__logo__jG71u",footer__links:"home_footer__links__q5uiZ",footer__links__item:"home_footer__links__item__gB0TO",footer__links__follow:"home_footer__links__follow__jv8nP",footer__links__follow__weixin:"home_footer__links__follow__weixin__yeCNp",footer__copyright:"home_footer__copyright__M6lua",footer__copyright__link:"home_footer__copyright__link__PBT0B","banner-slider__indicator__item--active":"home_banner-slider__indicator__item--active__Pkcak"}},6529:(e,t,_)=>{"use strict";_.r(t),_.d(t,{default:()=>j,generateMetadata:()=>f});var n=_(5036),i=_(7780),s=_.n(i);_(3440);var o=_(6843);let a=(0,o.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\footer.tsx`),{__esModule:l,$$typeof:r}=a,c=a.default,d=(0,o.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\nav.tsx`),{__esModule:h,$$typeof:m}=d,u=d.default,p=(0,o.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\components\back-to-top.tsx`),{__esModule:v,$$typeof:x}=p,g=p.default;async function f({params:e,searchParams:t},_){return e.locale,{description:"赛蓝科技 引领生活",icons:{icon:"/favicon.ico"}}}async function j({children:e,params:t}){return n.jsx("html",{lang:t.locale,children:(0,n.jsxs)("body",{className:s().className,children:[n.jsx("header",{children:n.jsx(u,{})}),n.jsx("main",{children:e}),n.jsx("footer",{children:n.jsx(c,{})}),n.jsx(g,{})]})})}},8157:(e,t,_)=>{"use strict";_.r(t),_.d(t,{default:()=>r});var n=_(5036),i=_(2297),s=_.n(i),o=_(2813),a=_(6904),l=_(6274);async function r(){let e=await (0,a.nI)();return n.jsx("div",{className:s().page404container,children:(0,n.jsxs)("div",{className:s().page404,children:[n.jsx("div",{className:s().page404__image,children:n.jsx(o.default,{src:"/404.png",width:331,height:151,alt:""})}),(0,n.jsxs)("div",{className:s().page404__content,children:[n.jsx("p",{children:e("page404Description")}),n.jsx("span",{children:e("page404Tip1")}),n.jsx("span",{children:e("page404Tip2")})]}),n.jsx("div",{className:s().page404__buttons,children:n.jsx(l.default,{href:"/",children:n.jsx("button",{className:s().page404__buttons__home,children:e("backToHome")})})})]})})}},2917:(e,t,_)=>{"use strict";async function n({children:e,params:t}){return e}_.r(t),_.d(t,{default:()=>n})},1429:(e,t,_)=>{"use strict";_.r(t),_.d(t,{$$typeof:()=>s,__esModule:()=>i,default:()=>o});let n=(0,_(6843).createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\not-found.tsx`),{__esModule:i,$$typeof:s}=n,o=n.default},6904:(e,t,_)=>{"use strict";_.d(t,{BH:()=>o,nI:()=>n});let{getI18n:n,getScopedI18n:i,getStaticParams:s,getCurrentLocale:o}=(0,_(2616).createI18nServer)({en:()=>_.e(473).then(_.bind(_,8473)),zh:()=>_.e(150).then(_.bind(_,8150))})},7481:(e,t,_)=>{"use strict";_.r(t),_.d(t,{default:()=>i});var n=_(337);let i=e=>[{type:"image/x-icon",sizes:"32x32",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},4507:()=>{},3440:()=>{}};