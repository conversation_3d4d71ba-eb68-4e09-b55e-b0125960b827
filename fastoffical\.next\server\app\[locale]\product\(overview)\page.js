/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/product/(overview)/page";
exports.ids = ["app/[locale]/product/(overview)/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'product',\n        {\n        children: [\n        '(overview)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/product/(overview)/page.tsx */ \"(rsc)/./app/[locale]/product/(overview)/page.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\product\\\\(overview)\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/product/layout.tsx */ \"(rsc)/./app/[locale]/product/layout.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\product\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/layout.tsx */ \"(rsc)/./app/[locale]/layout.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/not-found.tsx */ \"(rsc)/./app/[locale]/not-found.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\product\\\\(overview)\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/product/(overview)/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/product/(overview)/page\",\n        pathname: \"/[locale]/product\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/image-component.js */ \"(ssr)/./node_modules/next/dist/client/image-component.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDcGFnZS5tb2R1bGUuc2NzcyZtb2R1bGVzPUQlM0ElNUMtLS1BUHJvamVjdHMtLS0lNUNpbWNhbS1vZmZpY2lhbHNpdGUlNUNmYXN0b2ZmaWNhbCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDaW1hZ2UtY29tcG9uZW50LmpzJm1vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTkFBK0k7QUFDL0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz84OGJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxpbWFnZS1jb21wb25lbnQuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXC0tLUFQcm9qZWN0cy0tLVxcXFxpbWNhbS1vZmZpY2lhbHNpdGVcXFxcZmFzdG9mZmljYWxcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cpage.module.scss&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Cimage-component.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/components/back-to-top.tsx */ \"(ssr)/./app/[locale]/ui/components/back-to-top.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/home/<USER>/ \"(ssr)/./app/[locale]/ui/home/<USER>"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNjb21wb25lbnRzJTVDYmFjay10by10b3AudHN4Jm1vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNob21lJTVDZm9vdGVyLnRzeCZtb2R1bGVzPUQlM0ElNUMtLS1BUHJvamVjdHMtLS0lNUNpbWNhbS1vZmZpY2lhbHNpdGUlNUNmYXN0b2ZmaWNhbCU1Q2FwcCU1QyU1QmxvY2FsZSU1RCU1Q3VpJTVDaG9tZSU1Q25hdi50c3gmbW9kdWxlcz1EJTNBJTVDLS0tQVByb2plY3RzLS0tJTVDaW1jYW0tb2ZmaWNpYWxzaXRlJTVDZmFzdG9mZmljYWwlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyYXBwJTVDJTVDJTVCbG9jYWxlJTVEJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1EJTNBJTVDLS0tQVByb2plY3RzLS0tJTVDaW1jYW0tb2ZmaWNpYWxzaXRlJTVDZmFzdG9mZmljYWwlNUNhcHAlNUNnbG9iYWxzLnNjc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUF5STtBQUN6SSw4S0FBOEg7QUFDOUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz9jOTMxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcdWlcXFxcY29tcG9uZW50c1xcXFxiYWNrLXRvLXRvcC50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXC0tLUFQcm9qZWN0cy0tLVxcXFxpbWNhbS1vZmZpY2lhbHNpdGVcXFxcZmFzdG9mZmljYWxcXFxcYXBwXFxcXFtsb2NhbGVdXFxcXHVpXFxcXGhvbWVcXFxcZm9vdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcdWlcXFxcaG9tZVxcXFxuYXYudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Ccomponents%5Cback-to-top.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cfooter.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Chome%5Cnav.tsx&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5C%5Blocale%5D%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cglobals.scss&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Cproduct%5Cproduct-list.tsx&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Cproduct%5Cproduct-list.tsx&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[locale]/ui/product/product-list.tsx */ \"(ssr)/./app/[locale]/ui/product/product-list.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDJTVCbG9jYWxlJTVEJTVDdWklNUNwcm9kdWN0JTVDcHJvZHVjdC1saXN0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz9iY2IxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcW2xvY2FsZV1cXFxcdWlcXFxccHJvZHVjdFxcXFxwcm9kdWN0LWxpc3QudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5C%5Blocale%5D%5Cui%5Cproduct%5Cproduct-list.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(ssr)/./app/not-found.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Qy0tLUFQcm9qZWN0cy0tLSU1Q2ltY2FtLW9mZmljaWFsc2l0ZSU1Q2Zhc3RvZmZpY2FsJTVDYXBwJTVDbm90LWZvdW5kLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLz9mZGMwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcLS0tQVByb2plY3RzLS0tXFxcXGltY2FtLW9mZmljaWFsc2l0ZVxcXFxmYXN0b2ZmaWNhbFxcXFxhcHBcXFxcbm90LWZvdW5kLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp%5Cnot-found.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/back-to-top.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/back-to-top.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BackToTopLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction BackToTopLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_4__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BackToTop, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\nfunction BackToTop() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useI18n)();\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)();\n    const [showBackTop, setShowBackTop] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    let scrollTimer = null;\n    let isScrolling = false;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            const scrollHeight = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;\n            setShowBackTop(scrollHeight > window.innerHeight);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    const scrollToTop = ()=>{\n        if (isScrolling) return; // 如果正在滚动,则不执行操作\n        isScrolling = true // 设置滚动标记为 true\n        ;\n        if (scrollTimer) {\n            clearTimeout(scrollTimer);\n        }\n        scrollTimer = setTimeout(()=>{\n            window.scrollTo({\n                top: 0,\n                behavior: \"smooth\"\n            });\n            isScrolling = false // 滚动结束,将标记设置为 false\n            ;\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: `back-top ${showBackTop ? \"\" : \"back-top--hide\"}`,\n        onClick: scrollToTop,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: \"/back-top.svg\",\n                width: 26,\n                height: 26,\n                alt: \"Top\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            locale === \"zh\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"hide-on-small\",\n                children: t(\"backToTop\")\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\back-to-top.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/back-to-top.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/banner.tsx":
/*!***********************************************!*\
  !*** ./app/[locale]/ui/components/banner.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Banner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n\n\n\nfunction Banner({ src = \"/search-banner.webp\", mobileSrc = \"/search-banner.webp\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"banner hide-on-small\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: src,\n                    width: 1920,\n                    height: 220,\n                    alt: \"\",\n                    className: \"banner__image\",\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"banner hide-on-medium hide-on-large\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    src: mobileSrc,\n                    width: 1920,\n                    height: 220,\n                    alt: \"\",\n                    className: \"banner__image\",\n                    unoptimized: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\banner.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9iYW5uZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUNKO0FBRVgsU0FBU0MsT0FBTyxFQUM3QkMsTUFBTSxxQkFBcUIsRUFDM0JDLFlBQVkscUJBQXFCLEVBQ2xDO0lBQ0MscUJBQ0U7OzBCQUNFLDhEQUFDQztnQkFBSUMsV0FBVzswQkFDZCw0RUFBQ0wsa0RBQUtBO29CQUNKRSxLQUFLQTtvQkFDTEksT0FBTztvQkFDUEMsUUFBUTtvQkFDUkMsS0FBSTtvQkFDSkgsV0FBVztvQkFDWEksV0FBVzs7Ozs7Ozs7Ozs7MEJBR2YsOERBQUNMO2dCQUFJQyxXQUFXOzBCQUNkLDRFQUFDTCxrREFBS0E7b0JBQ0pFLEtBQUtDO29CQUNMRyxPQUFPO29CQUNQQyxRQUFRO29CQUNSQyxLQUFJO29CQUNKSCxXQUFXO29CQUNYSSxXQUFXOzs7Ozs7Ozs7Ozs7O0FBS3JCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS91aS9jb21wb25lbnRzL2Jhbm5lci50c3g/MGQyNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSW1hZ2UgZnJvbSAnbmV4dC9pbWFnZSdcclxuaW1wb3J0ICcuL2NvbXBvbmVudHMuc2NzcydcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEJhbm5lcih7XHJcbiAgc3JjID0gJy9zZWFyY2gtYmFubmVyLndlYnAnLFxyXG4gIG1vYmlsZVNyYyA9ICcvc2VhcmNoLWJhbm5lci53ZWJwJyxcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17J2Jhbm5lciBoaWRlLW9uLXNtYWxsJ30+XHJcbiAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICBzcmM9e3NyY31cclxuICAgICAgICAgIHdpZHRoPXsxOTIwfVxyXG4gICAgICAgICAgaGVpZ2h0PXsyMjB9XHJcbiAgICAgICAgICBhbHQ9XCJcIlxyXG4gICAgICAgICAgY2xhc3NOYW1lPXsnYmFubmVyX19pbWFnZSd9XHJcbiAgICAgICAgICB1bm9wdGltaXplZFxyXG4gICAgICAgID48L0ltYWdlPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9eydiYW5uZXIgaGlkZS1vbi1tZWRpdW0gaGlkZS1vbi1sYXJnZSd9PlxyXG4gICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgc3JjPXttb2JpbGVTcmN9XHJcbiAgICAgICAgICB3aWR0aD17MTkyMH1cclxuICAgICAgICAgIGhlaWdodD17MjIwfVxyXG4gICAgICAgICAgYWx0PVwiXCJcclxuICAgICAgICAgIGNsYXNzTmFtZT17J2Jhbm5lcl9faW1hZ2UnfVxyXG4gICAgICAgICAgdW5vcHRpbWl6ZWRcclxuICAgICAgICA+PC9JbWFnZT5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8Lz5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIkltYWdlIiwiQmFubmVyIiwic3JjIiwibW9iaWxlU3JjIiwiZGl2IiwiY2xhc3NOYW1lIiwid2lkdGgiLCJoZWlnaHQiLCJhbHQiLCJ1bm9wdGltaXplZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/banner.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/dropdown-window.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/ui/components/dropdown-window.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DropdownWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\nfunction DropdownWindow({ children, list, show = false, onClick = ()=>{}, onClickMask = ()=>{} }) {\n    const handleClick = (id)=>{\n        onClick(id);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            children,\n            show && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown-window\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__above\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    src: \"/dropdown-window-above.svg\",\n                                    width: 15,\n                                    height: 8,\n                                    alt: \"drop\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__list\",\n                                children: list.map((item, index)=>{\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.link ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.link,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"dropdown-window__list__item\",\n                                                onClick: ()=>{\n                                                    handleClick(item.link);\n                                                },\n                                                children: item.text\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"dropdown-window__list__item\",\n                                            onClick: ()=>{\n                                                handleClick(item?.id);\n                                            },\n                                            children: item.text\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"dropdown-window__placeholder\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dropdown-window__mask hide-on-medium hide-on-large\",\n                        onClick: ()=>{\n                            onClickMask();\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\dropdown-window.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9kcm9wZG93bi13aW5kb3cudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUNGO0FBUWIsU0FBU0UsZUFBZSxFQUNyQ0MsUUFBUSxFQUNSQyxJQUFJLEVBQ0pDLE9BQU8sS0FBSyxFQUNaQyxVQUFVLEtBQU8sQ0FBQyxFQUNsQkMsY0FBYyxLQUFPLENBQUMsRUFPdEI7SUFDQSxNQUFNQyxjQUFjLENBQUNDO1FBQ25CSCxRQUFRRztJQUNWO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU87WUFBRUMsVUFBVTtRQUFXOztZQUNoQ1Q7WUFDQUUsc0JBQ0M7O2tDQUNFLDhEQUFDSzt3QkFBSUcsV0FBVTs7MENBQ2IsOERBQUNIO2dDQUFJRyxXQUFVOzBDQUNiLDRFQUFDYixrREFBS0E7b0NBQ0pjLEtBQUs7b0NBQ0xDLE9BQU87b0NBQ1BDLFFBQVE7b0NBQ1JDLEtBQUk7Ozs7Ozs7Ozs7OzBDQUdSLDhEQUFDUDtnQ0FBSUcsV0FBVTswQ0FDWlQsS0FBS2MsR0FBRyxDQUFDLENBQUNDLE1BQU1DO29DQUNmLHFCQUNFLDhEQUFDVjtrREFDRVMsS0FBS0UsSUFBSSxpQkFDUiw4REFBQ3BCLGlEQUFJQTs0Q0FBQ3FCLE1BQU1ILEtBQUtFLElBQUk7c0RBQ25CLDRFQUFDWDtnREFDQ0csV0FBVTtnREFDVlAsU0FBUztvREFDUEUsWUFBWVcsS0FBS0UsSUFBSTtnREFDdkI7MERBRUNGLEtBQUtJLElBQUk7Ozs7Ozs7Ozs7aUVBSWQsOERBQUNiOzRDQUNDRyxXQUFVOzRDQUNWUCxTQUFTO2dEQUNQRSxZQUFZVyxNQUFNVjs0Q0FDcEI7c0RBRUNVLEtBQUtJLElBQUk7Ozs7Ozt1Q0FuQk5IOzs7OztnQ0F3QmQ7Ozs7OzswQ0FFRiw4REFBQ1Y7Z0NBQUlHLFdBQVU7Ozs7Ozs7Ozs7OztrQ0FFakIsOERBQUNIO3dCQUNDRyxXQUFVO3dCQUNWUCxTQUFTOzRCQUNQQzt3QkFDRjs7Ozs7Ozs7Ozs7Ozs7QUFNWiIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9kcm9wZG93bi13aW5kb3cudHN4P2VmZmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnXHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcclxuXHJcbnR5cGUgTGlzdExpbmsgPSB7XHJcbiAgbGluaz86IHN0cmluZ1xyXG4gIHRleHQ6IHN0cmluZ1xyXG4gIGlkPzogc3RyaW5nXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERyb3Bkb3duV2luZG93KHtcclxuICBjaGlsZHJlbixcclxuICBsaXN0LFxyXG4gIHNob3cgPSBmYWxzZSxcclxuICBvbkNsaWNrID0gKCkgPT4ge30sXHJcbiAgb25DbGlja01hc2sgPSAoKSA9PiB7fSxcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxuICBsaXN0OiBBcnJheTxMaXN0TGluaz5cclxuICBzaG93OiBib29sZWFuXHJcbiAgb25DbGljazogRnVuY3Rpb25cclxuICBvbkNsaWNrTWFzaz86IEZ1bmN0aW9uXHJcbn0+KSB7XHJcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoaWQ/OiBzdHJpbmcpID0+IHtcclxuICAgIG9uQ2xpY2soaWQpXHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBzdHlsZT17eyBwb3NpdGlvbjogJ3JlbGF0aXZlJyB9fT5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgICB7c2hvdyAmJiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZHJvcGRvd24td2luZG93XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZHJvcGRvd24td2luZG93X19hYm92ZVwiPlxyXG4gICAgICAgICAgICAgIDxJbWFnZVxyXG4gICAgICAgICAgICAgICAgc3JjPXsnL2Ryb3Bkb3duLXdpbmRvdy1hYm92ZS5zdmcnfVxyXG4gICAgICAgICAgICAgICAgd2lkdGg9ezE1fVxyXG4gICAgICAgICAgICAgICAgaGVpZ2h0PXs4fVxyXG4gICAgICAgICAgICAgICAgYWx0PVwiZHJvcFwiXHJcbiAgICAgICAgICAgICAgPjwvSW1hZ2U+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLXdpbmRvd19fbGlzdFwiPlxyXG4gICAgICAgICAgICAgIHtsaXN0Lm1hcCgoaXRlbSwgaW5kZXgpID0+IHtcclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0+XHJcbiAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGluayA/IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2l0ZW0ubGlua30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkcm9wZG93bi13aW5kb3dfX2xpc3RfX2l0ZW1cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNsaWNrKGl0ZW0ubGluaylcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0udGV4dH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZHJvcGRvd24td2luZG93X19saXN0X19pdGVtXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNsaWNrKGl0ZW0/LmlkKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS50ZXh0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImRyb3Bkb3duLXdpbmRvd19fcGxhY2Vob2xkZXJcIj48L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJkcm9wZG93bi13aW5kb3dfX21hc2sgaGlkZS1vbi1tZWRpdW0gaGlkZS1vbi1sYXJnZVwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICBvbkNsaWNrTWFzaygpXHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICA+PC9kaXY+XHJcbiAgICAgICAgPC8+XHJcbiAgICAgICl9XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIkltYWdlIiwiTGluayIsIkRyb3Bkb3duV2luZG93IiwiY2hpbGRyZW4iLCJsaXN0Iiwic2hvdyIsIm9uQ2xpY2siLCJvbkNsaWNrTWFzayIsImhhbmRsZUNsaWNrIiwiaWQiLCJkaXYiLCJzdHlsZSIsInBvc2l0aW9uIiwiY2xhc3NOYW1lIiwic3JjIiwid2lkdGgiLCJoZWlnaHQiLCJhbHQiLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJsaW5rIiwiaHJlZiIsInRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/dropdown-window.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/ui/components/flex-4items-box.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Flex4ItemsBox),\n/* harmony export */   itemsMode: () => (/* binding */ itemsMode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\nvar itemsMode;\n(function(itemsMode) {\n    itemsMode[\"normal\"] = \"\";\n    itemsMode[\"product\"] = \"product\";\n    itemsMode[\"pride\"] = \"pride\";\n})(itemsMode || (itemsMode = {}));\nfunction Flex4ItemsBox({ infos, imageSize, imageBox, mode = \"\", gap = 0, isDetail = false }) {\n    const renderCard = ({ imageSrc, title, tip = \"\", link = \"\", videoSrc = \"\" })=>{\n        const isLinkCard = !!link;\n        const isVideoCard = !!videoSrc;\n        const cardClassName = `flex-box-with-4items__card ${mode === \"product\" ? \"flex-box-with-4items__card--product\" : \"\"} ${isLinkCard ? \"flex-box-with-4items__card--link\" : \"\"} ${isVideoCard ? \"flex-box-with-4items__card--video\" : \"\"}`;\n        const infoClassName = `${mode === \"\" ? \"\" : `flex-box-with-4items__card__info--${mode}`} flex-box-with-4items__card__info`;\n        const cardContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-box-with-4items__card__image\",\n                    style: {\n                        aspectRatio: imageBox.width / imageBox.height\n                    },\n                    children: isVideoCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        controls: true,\n                        poster: imageSrc,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: videoSrc,\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        unoptimized: true,\n                        src: imageSrc,\n                        width: imageSize.width,\n                        height: imageSize.height,\n                        alt: \"image\",\n                        style: {\n                            objectFit: \"fill\",\n                            width: \"100%\",\n                            height: \"auto\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: infoClassName,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this),\n                        mode === \"pride\" ? \"\" : tip ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: tip\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 50\n                        }, this) : \"\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n        return isLinkCard ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            href: link,\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: cardClassName,\n            children: cardContent\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${`flex-box-with-4items`} ${mode === \"product\" ? \"flex-box-with-4items--product\" : \"\"} ${isDetail ? \"flex-box-with-4items--detail\" : \"\"}`,\n        style: gap ? {\n            gap\n        } : {},\n        children: infos.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_2___default().Fragment), {\n                children: renderCard(info)\n            }, `${info.link}-${index}`, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\flex-4items-box.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/nav-list.tsx":
/*!*************************************************!*\
  !*** ./app/[locale]/ui/components/nav-list.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavListLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction NavListLayout({ isFooter = false, onClick = ()=>{} }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_4__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavList, {\n            isFooter: isFooter,\n            onClick: onClick\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction NavList({ isFooter = false, onClick }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_4__.useI18n)();\n    const _items = [\n        {\n            iconSrc: \"/menu-home-icon.svg\",\n            title: t(\"home\"),\n            id: 0,\n            link: \"/\"\n        },\n        {\n            iconSrc: \"/menu-product-icon.svg\",\n            title: t(\"productCenter\"),\n            id: 1,\n            active: false\n        },\n        {\n            subNavs: [\n                {\n                    title: t(\"productCamera\"),\n                    link: \"/product\",\n                    id: 1\n                }\n            ]\n        },\n        // {\n        //   iconSrc: '/menu-videos-icon.svg',\n        //   title: '产品视频',\n        //   link: '/videos',\n        //   id: 2,\n        // },\n        {\n            iconSrc: \"/menu-support-icon.svg\",\n            title: t(\"support\"),\n            link: \"/support\",\n            id: 3\n        },\n        // {\n        //   iconSrc: '/menu-news-icon.svg',\n        //   title: '新闻资讯',\n        //   link: '/news',\n        //   id: 4,\n        // },\n        {\n            iconSrc: \"/menu-about-icon.svg\",\n            title: t(\"aboutCylan\"),\n            link: \"/about\",\n            id: 5\n        }\n    ];\n    const [items, setItems] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(_items);\n    const handleClick = (id, isLink)=>{\n        if (isLink) {\n            onClick && onClick();\n            return;\n        }\n        const newItems = [\n            ...items\n        ];\n        newItems.forEach((item)=>{\n            if (item.id === id) item.active = !item.active;\n        });\n        setItems(newItems);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `nav-list ${isFooter ? \"nav-list--footer\" : \"\"}`,\n        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavItem, {\n                navItemPros: item,\n                navItemStatus: items,\n                onClick: handleClick\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\nfunction NavItem({ navItemPros, navItemStatus, onClick }) {\n    if (navItemPros.subNavs) {\n        const subNavs = navItemPros.subNavs;\n        const id = subNavs[0].id;\n        const mainItem = navItemStatus.find((item)=>item?.id === id);\n        const handleClick = ()=>{\n            const isLink = true;\n            onClick(id, isLink);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: mainItem?.active && subNavs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: item.link,\n                    onClick: handleClick,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item nav-list__item--sub\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"nav-list__item__icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"nav-list__item__title\",\n                                children: item.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 15\n                    }, this)\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false);\n    } else {\n        const { iconSrc, title, link, id, active } = navItemPros;\n        const handleClick = ()=>{\n            const isLink = !!link;\n            onClick(id, isLink);\n        };\n        const Item = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item__icon\",\n                        children: iconSrc && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: iconSrc,\n                            width: 24,\n                            height: 24,\n                            alt: \"\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"nav-list__item__title\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    !link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"nav-list__item__button\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: `/menu-arrow-${active ? \"up\" : \"down\"}.svg`,\n                            width: 32,\n                            height: 32,\n                            alt: \"\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: handleClick,\n                className: \"nav-list__item\",\n                children: [\n                    !link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Item, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 21\n                    }, this),\n                    link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\"\n                        },\n                        className: \"nav-list__item\",\n                        href: link,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Item, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\nav-list.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this)\n        }, void 0, false);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/nav-list.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/page-tabs.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/ui/components/page-tabs.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components.scss */ \"(ssr)/./app/[locale]/ui/components/components.scss\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _banner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./banner */ \"(ssr)/./app/[locale]/ui/components/banner.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction PageTabs({ title = \"\", iconSrc = \"\", tabs = [], currentTab, bannerSrc = \"/pic_shipinbg@2x (1).webp\", bannerMobileSrc = \"/pic_shipinbg@2x (1).webp\", background = \"rgb(179, 220, 252)\", onTabChange = ()=>{}, showBanner = true, isSearch = false, isLink = false }) {\n    const handleTabChange = (tab)=>{\n        onTabChange(tab);\n    };\n    const Title = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"page-tabs__content\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `page-tabs__content__title`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: iconSrc,\n                        width: 34,\n                        height: 34,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n            lineNumber: 43,\n            columnNumber: 5\n        }, this);\n    const TabItems = ({ isLink = false })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `page-tabs__content__items hide-on-small`,\n            children: tabs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_4___default().Fragment), {\n                    children: isLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        href: `${item.id}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: `${`page-tabs__content__items__item`} ${currentTab === item.id ? `page-tabs__content__items__item--active` : \"\"}`,\n                            onClick: ()=>handleTabChange(item.id),\n                            children: item.text\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `${`page-tabs__content__items__item`} ${currentTab === item.id ? `page-tabs__content__items__item--active` : \"\"}`,\n                        onClick: ()=>handleTabChange(item.id),\n                        children: item.text\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 15\n                    }, this)\n                }, index, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"page-tabs\",\n                style: background ? {\n                    background\n                } : {},\n                children: [\n                    showBanner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_banner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        src: bannerSrc,\n                        mobileSrc: bannerMobileSrc\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 24\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    !isSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabItems, {\n                        isLink: isLink\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 23\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmallTabs, {\n                onTabChange: onTabChange,\n                tabs: tabs,\n                currentTab: currentTab,\n                isLink: isLink\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction SearchArea() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `page-tabs__search hide-on-small`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"text\",\n                placeholder: \"请输入关键字，例如：看家王C31、大屏摄像机\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/search-icon-white.svg\",\n                        width: 20,\n                        height: 20,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    \"搜索\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\nfunction SmallTabs({ tabs, currentTab, onTabChange, isLink }) {\n    const Tab = ({ id, text })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `${currentTab === id ? \"page-tabs__tabs-small__tab--active\" : \"\"} page-tabs__tabs-small__tab`,\n            onClick: ()=>{\n                onTabChange(id);\n            },\n            children: text\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"page-tabs__tabs-small hide-on-medium hide-on-large\",\n        children: tabs.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_4___default().Fragment), {\n                children: isLink ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    href: item.id,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tab, {\n                        ...item\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tab, {\n                    ...item\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 13\n                }, this)\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\page-tabs.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/page-tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/pagination.tsx":
/*!***************************************************!*\
  !*** ./app/[locale]/ui/components/pagination.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Pagination)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Pagination({ currentPage = 1, pageSize = 8, count = 0, onChange = ()=>{} }) {\n    let JumperType;\n    (function(JumperType) {\n        JumperType[JumperType[\"first\"] = 0] = \"first\";\n        JumperType[JumperType[\"last\"] = 1] = \"last\";\n    })(JumperType || (JumperType = {}));\n    const maxPage = Math.ceil(count / pageSize);\n    const PageJumper = ({ type = 0 })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>{\n                if (currentPage === 1 && type === 0) {} else if (currentPage === maxPage && type === 1) {} else {\n                    onChange(type === 0 ? 1 : maxPage);\n                }\n            },\n            className: \"pagination__pagejumper\",\n            disabled: type === 0 && currentPage === 1 || type === 1 && currentPage === maxPage,\n            children: type === 0 ? \"首页\" : \"尾页\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\pagination.tsx\",\n            lineNumber: 23,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: count > 8 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pagination hide-on-small\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageJumper, {\n                    type: 0\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\pagination.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, this),\n                Array(maxPage).fill(0).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            if (currentPage !== index + 1) onChange(index + 1);\n                        },\n                        className: `pagination__page ${currentPage === index + 1 ? \"pagination__page--active\" : \"\"}`,\n                        children: index + 1\n                    }, index, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\pagination.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 15\n                    }, this)),\n                maxPage > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: `pagination__page`,\n                    children: \"...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\pagination.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageJumper, {\n                    type: 1\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\pagination.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\components\\\\pagination.tsx\",\n            lineNumber: 43,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/pagination.tsx\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!*****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FooterLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_nav_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/nav-list */ \"(ssr)/./app/[locale]/ui/components/nav-list.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction FooterLayout() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nfunction Footer() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const renderLink = ({ link, text })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: link,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: text\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    };\n    const FooterItem = ({ title, links })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__item),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: title\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 7\n                }, this),\n                links.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                        children: renderLink({\n                            link: item.link,\n                            text: item.text\n                        })\n                    }, `${item.link}-${index}`, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this))\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, this);\n    const productLinks = [\n        {\n            link: \"/product?tab=01\",\n            text: t(\"productCamera\")\n        },\n        {\n            link: \"/product?tab=02\",\n            text: t(\"productTranslator\")\n        }\n    ];\n    const supportLinks = [\n        {\n            link: \"/support/download_client\",\n            text: t(\"downloadClient\")\n        },\n        {\n            link: \"/support/help\",\n            text: t(\"help\")\n        }\n    ];\n    const aboutLinks = [\n        {\n            link: \"/about#about-cylan\",\n            text: t(\"aboutCylan\")\n        },\n        {\n            link: \"/about#prides\",\n            text: t(\"cylanPrides\")\n        },\n        {\n            link: \"/about#contacts\",\n            text: t(\"contactUs\")\n        }\n    ];\n    const Follow = ()=>{\n        const [isShowPop, setIsShowpop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__follow),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: t(\"followUs\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onMouseEnter: ()=>{\n                        setIsShowpop(true);\n                    },\n                    onMouseLeave: ()=>{\n                        setIsShowpop(false);\n                    },\n                    className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links__follow__weixin),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/weixin.svg\",\n                            width: 20,\n                            height: 20,\n                            alt: \"weixin\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        isShowPop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/support/imcam-gongzhonghao.jpg\",\n                                    width: 140,\n                                    height: 140,\n                                    alt: \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t(\"imcamGongzhonghao\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    className: \"hide-on-medium hide-on-large\",\n                                    href: \"/support/imcam-gongzhonghao.jpg\",\n                                    download: true,\n                                    children: t(\"downloadQRcode\")\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__logo)} hide-on-medium hide-on-large`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    href: \"/\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/cylan_logo-white.png\",\n                        width: 125,\n                        height: 44,\n                        alt: \"logo\",\n                        unoptimized: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__links)} hide-on-small`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__logo)}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/cylan_logo-white.png\",\n                                width: 125,\n                                height: 44,\n                                alt: \"logo\",\n                                unoptimized: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"productCenter\"),\n                        links: productLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"support\"),\n                        links: supportLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FooterItem, {\n                        title: t(\"aboutUs\"),\n                        links: aboutLinks\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Follow, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hide-on-large hide-on-medium\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_list__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        isFooter: true\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Follow, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__copyright),\n                children: [\n                    t(\"copyrightText\"),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer__copyright__link),\n                        href: \"https://beian.miit.gov.cn/\",\n                        target: \"_blank\",\n                        children: t(\"copyrightLink\")\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\footer.tsx\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!**************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./home.module.scss */ \"(ssr)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_dropdown_window__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/dropdown-window */ \"(ssr)/./app/[locale]/ui/components/dropdown-window.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_nav_list__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/nav-list */ \"(ssr)/./app/[locale]/ui/components/nav-list.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction NavLayout() {\n    const selectedLayoutSegment = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSelectedLayoutSegment)();\n    const isNotFoundPage = selectedLayoutSegment === \"__DEFAULT__\";\n    if (isNotFoundPage) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 21,\n        columnNumber: 30\n    }, this);\n    else return(// eslint-disable-next-line react-hooks/rules-of-hooks\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_7__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Nav, {}, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 26,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 25,\n        columnNumber: 7\n    }, this));\n}\nfunction Nav() {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [isShowMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        // 监听滚动事件\n        const handleScroll = ()=>{\n            // 获取当前滚动位置\n            const scrollTop = window.scrollY;\n            // 根据滚动位置是否大于0来判断是否添加投影效果\n            setIsScrolled(scrollTop > 0);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>{\n            window.removeEventListener(\"scroll\", handleScroll);\n        };\n    }, []);\n    const handleNavListClick = ()=>{\n        setShowMenu(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            position: \"relative\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__placeholder)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav)} ${isScrolled ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav--scrolled\"]) : \"\"} ${isShowMenu ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav--scrolled\"]) : \"\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            style: {\n                                height: 44\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: \"/cylan_logo.png\",\n                                width: 125,\n                                height: 44,\n                                alt: \"Cylan Logo\",\n                                unoptimized: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list)} hide-on-small hide-on-medium`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"home\"),\n                                    link: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"productCenter\"),\n                                    showArrow: true,\n                                    link: \"/product\",\n                                    links: [\n                                        {\n                                            link: \"/product?tab=01\",\n                                            text: t(\"productCamera\")\n                                        },\n                                        {\n                                            link: \"/product?tab=02\",\n                                            text: t(\"productTranslator\")\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"support\"),\n                                    showArrow: true,\n                                    link: \"/support\",\n                                    links: [\n                                        {\n                                            link: \"/support/download_client\",\n                                            text: t(\"downloadClient\")\n                                        },\n                                        {\n                                            link: \"/support/help\",\n                                            text: t(\"help\")\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavListItem, {\n                                    title: t(\"aboutUs\"),\n                                    link: \"/about\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Language, {}, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowMenu(!isShowMenu);\n                                    },\n                                    className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__menu)} hide-on-large`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: \"/menu.svg\",\n                                        width: 49,\n                                        height: 50,\n                                        alt: \"menu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            isShowMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__drop)} hide-on-large`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_list__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onClick: ()=>handleNavListClick()\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__mask),\n                        onClick: ()=>{\n                            setShowMenu(false);\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\nfunction ArrowDown() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        src: \"/arrow-down.svg\",\n        height: 10,\n        width: 16,\n        alt: \"\"\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 150,\n        columnNumber: 10\n    }, this);\n}\nfunction NavListItem({ title, link = \"/\", showArrow = false, links = [] }) {\n    // 鼠标悬停\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const handleMouseEnter = ()=>{\n        setIsHovered(true);\n    };\n    const handleMouseLeave = ()=>{\n        setIsHovered(false);\n    };\n    let pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    if (pathname.includes(\"/zh/\") || pathname.includes(\"/en/\")) {\n        pathname = pathname.replace(/\\/zh|\\/en/, \"\");\n    } else {\n        pathname = \"/\";\n    }\n    pathname = \"/\" + pathname.split(\"/\")[1];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        children: showArrow ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dropdown_window__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onClick: ()=>setIsHovered(false),\n            list: links,\n            show: isHovered,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                href: link,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list__item)} ${pathname === link.split(\"?\")[0] ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--active\"]) : \"\"}`,\n                    children: [\n                        title,\n                        \" \",\n                        showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowDown, {}, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 37\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 195,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 190,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            href: link,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__list__item)} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--link\"])} ${pathname === `/${link.split(\"/\")[1]}` ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__list__item--active\"]) : \"\"}`,\n                children: [\n                    title,\n                    \" \",\n                    showArrow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowDown, {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 35\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 208,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 207,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\nfunction Language() {\n    const changeLocale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useChangeLocale)();\n    const currentLocal = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useCurrentLocale)();\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_7__.useI18n)();\n    const LangItem = ({ lang, isActive = false })=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            onClick: ()=>{\n                if (isActive) return;\n                changeLocale(lang);\n            },\n            className: `${isActive ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default()[\"nav__right__language__text--active\"]) : \"\"} ${(_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__language__text)}`,\n            children: lang === \"zh\" ? t(\"chinese\") : t(\"english\")\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__language),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LangItem, {\n                lang: \"zh\",\n                isActive: currentLocal === \"zh\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \"/\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LangItem, {\n                lang: \"en\",\n                isActive: currentLocal === \"en\"\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 249,\n        columnNumber: 5\n    }, this);\n}\nfunction Search() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_8___default().nav__right__search),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: \"/search-icon.svg\",\n            width: 20,\n            height: 20,\n            alt: \"Search icon\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n            lineNumber: 260,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\nav.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvaG9tZS9uYXYudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ3VDO0FBQ1g7QUFDRTtBQUM0QjtBQUNmO0FBQ0U7QUFDRDtBQU1uQjtBQUNpQztBQUUzQyxTQUFTYTtJQUN0QixNQUFNQyx3QkFBd0JGLHlFQUF3QkE7SUFDdEQsTUFBTUcsaUJBQWlCRCwwQkFBMEI7SUFFakQsSUFBSUMsZ0JBQWdCLHFCQUFPLDhEQUFDQzs7Ozs7U0FFMUIsT0FDRSxzREFBc0Q7a0JBQ3RELDhEQUFDTCwrREFBa0JBO1FBQUNNLFFBQVFSLGlFQUFnQkE7a0JBQzFDLDRFQUFDUzs7Ozs7Ozs7OztBQUdUO0FBRUEsU0FBU0E7SUFDUCxNQUFNQyxJQUFJVCx3REFBT0E7SUFDakIsTUFBTSxDQUFDVSxZQUFZQyxjQUFjLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNpQixZQUFZQyxZQUFZLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUUzQ0QsZ0RBQVNBLENBQUM7UUFDUixTQUFTO1FBQ1QsTUFBTW9CLGVBQWU7WUFDbkIsV0FBVztZQUNYLE1BQU1DLFlBQVlDLE9BQU9DLE9BQU87WUFDaEMseUJBQXlCO1lBQ3pCTixjQUFjSSxZQUFZO1FBQzVCO1FBRUFDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVKO1FBQ2xDLE9BQU87WUFDTEUsT0FBT0csbUJBQW1CLENBQUMsVUFBVUw7UUFDdkM7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNTSxxQkFBcUI7UUFDekJQLFlBQVk7SUFDZDtJQUVBLHFCQUNFLDhEQUFDUDtRQUFJZSxPQUFPO1lBQUVDLFVBQVU7UUFBVzs7MEJBQ2pDLDhEQUFDaEI7Z0JBQUlpQixXQUFXakMsMkVBQXVCOzs7Ozs7MEJBQ3ZDLDhEQUFDZ0I7Z0JBQ0NpQixXQUFXLENBQUMsRUFBRWpDLDhEQUFVLENBQUMsQ0FBQyxFQUFFb0IsYUFBYXBCLDJFQUF1QixHQUFHLEdBQ2hFLENBQUMsRUFBRXNCLGFBQWF0QiwyRUFBdUIsR0FBRyxHQUFHLENBQUM7MEJBRWpELDRFQUFDZ0I7b0JBQUlpQixXQUFXakMsdUVBQW1COztzQ0FFakMsOERBQUNDLGlEQUFJQTs0QkFBQ29DLE1BQU07NEJBQUtOLE9BQU87Z0NBQUVPLFFBQVE7NEJBQUc7c0NBQ25DLDRFQUFDcEMsa0RBQUtBO2dDQUNKcUMsS0FBSztnQ0FDTEMsT0FBTztnQ0FDUEYsUUFBUTtnQ0FDUkcsS0FBSTtnQ0FDSkMsV0FBVzs7Ozs7Ozs7Ozs7c0NBS2YsOERBQUMxQjs0QkFBSWlCLFdBQVcsQ0FBQyxFQUFFakMsb0VBQWdCLENBQUMsNkJBQTZCLENBQUM7OzhDQUNoRSw4REFBQzRDO29DQUFZQyxPQUFPMUIsRUFBRTtvQ0FBUzJCLE1BQUs7Ozs7Ozs4Q0FDcEMsOERBQUNGO29DQUNDQyxPQUFPMUIsRUFBRTtvQ0FDVDRCLFNBQVM7b0NBQ1RELE1BQUs7b0NBQ0xFLE9BQU87d0NBQ0w7NENBQ0VGLE1BQU07NENBQ05HLE1BQU05QixFQUFFO3dDQUNWO3dDQUNBOzRDQUNFMkIsTUFBTTs0Q0FDTkcsTUFBTTlCLEVBQUU7d0NBQ1Y7cUNBQ0Q7Ozs7Ozs4Q0FHSCw4REFBQ3lCO29DQUNDQyxPQUFPMUIsRUFBRTtvQ0FDVDRCLFNBQVM7b0NBQ1RELE1BQUs7b0NBQ0xFLE9BQU87d0NBQ0w7NENBQ0VGLE1BQU07NENBQ05HLE1BQU05QixFQUFFO3dDQUNWO3dDQUNBOzRDQUNFMkIsTUFBTTs0Q0FDTkcsTUFBTTlCLEVBQUU7d0NBQ1Y7cUNBQ0Q7Ozs7Ozs4Q0FHSCw4REFBQ3lCO29DQUFZQyxPQUFPMUIsRUFBRTtvQ0FBWTJCLE1BQUs7Ozs7Ozs7Ozs7OztzQ0FJekMsOERBQUM5Qjs0QkFBSWlCLFdBQVdqQyxxRUFBaUI7OzhDQUMvQiw4REFBQ21EOzs7Ozs4Q0FFRCw4REFBQ0M7b0NBQ0NDLFNBQVM7d0NBQ1A5QixZQUFZLENBQUNEO29DQUNmO29DQUNBVyxXQUFXLENBQUMsRUFBRWpDLDJFQUF1QixDQUFDLGNBQWMsQ0FBQzs4Q0FFckQsNEVBQUNFLGtEQUFLQTt3Q0FDSnFDLEtBQUs7d0NBQ0xDLE9BQU87d0NBQ1BGLFFBQVE7d0NBQ1JHLEtBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNYm5CLDRCQUNDOztrQ0FDRSw4REFBQ047d0JBQUlpQixXQUFXLENBQUMsRUFBRWpDLG9FQUFnQixDQUFDLGNBQWMsQ0FBQztrQ0FDakQsNEVBQUNPLDREQUFPQTs0QkFBQzhDLFNBQVMsSUFBTXZCOzs7Ozs7Ozs7OztrQ0FFMUIsOERBQUNkO3dCQUNDaUIsV0FBV2pDLG9FQUFnQjt3QkFDM0JxRCxTQUFTOzRCQUNQOUIsWUFBWTt3QkFDZDs7Ozs7Ozs7Ozs7Ozs7QUFNWjtBQUVBLFNBQVNrQztJQUNQLHFCQUFPLDhEQUFDdkQsa0RBQUtBO1FBQUNxQyxLQUFLO1FBQW1CRCxRQUFRO1FBQUlFLE9BQU87UUFBSUMsS0FBSTs7Ozs7O0FBQ25FO0FBRUEsU0FBU0csWUFBWSxFQUNuQkMsS0FBSyxFQUNMQyxPQUFPLEdBQUcsRUFDVkMsWUFBWSxLQUFLLEVBQ2pCQyxRQUFRLEVBQUUsRUFTWDtJQUNDLE9BQU87SUFDUCxNQUFNLENBQUNVLFdBQVdDLGFBQWEsR0FBR3RELCtDQUFRQSxDQUFDO0lBRTNDLE1BQU11RCxtQkFBbUI7UUFDdkJELGFBQWE7SUFDZjtJQUVBLE1BQU1FLG1CQUFtQjtRQUN2QkYsYUFBYTtJQUNmO0lBRUEsSUFBSUcsV0FBV3hELDREQUFXQTtJQUMxQixJQUFJd0QsU0FBU0MsUUFBUSxDQUFDLFdBQVdELFNBQVNDLFFBQVEsQ0FBQyxTQUFTO1FBQzFERCxXQUFXQSxTQUFTRSxPQUFPLENBQUMsYUFBYTtJQUMzQyxPQUFPO1FBQ0xGLFdBQVc7SUFDYjtJQUVBQSxXQUFXLE1BQU1BLFNBQVNHLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtJQUV2QyxxQkFDRSw4REFBQ2pEO1FBQUlrRCxjQUFjTjtRQUFrQk8sY0FBY047a0JBQ2hEZCwwQkFDQyw4REFBQzVDLG1FQUFjQTtZQUNia0QsU0FBUyxJQUFNTSxhQUFhO1lBQzVCUyxNQUFNcEI7WUFDTnFCLE1BQU1YO3NCQUVOLDRFQUFDekQsaURBQUlBO2dCQUFDb0MsTUFBTVM7MEJBQ1YsNEVBQUM5QjtvQkFDQ2lCLFdBQVcsQ0FBQyxFQUFFakMsMEVBQXNCLENBQUMsQ0FBQyxFQUFFOEQsYUFBYWhCLEtBQUttQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FDakVqRSxxRkFBaUMsR0FDakMsR0FDSCxDQUFDOzt3QkFFSDZDO3dCQUFNO3dCQUFFRSwyQkFBYSw4REFBQ1U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2lDQUs3Qiw4REFBQ3hELGlEQUFJQTtZQUFDb0MsTUFBTVM7c0JBQ1YsNEVBQUM5QjtnQkFDQ2lCLFdBQVcsQ0FBQyxFQUFFakMsMEVBQXNCLENBQUMsQ0FBQyxFQUFFQSxtRkFBK0IsQ0FDcEUsQ0FBQyxFQUFFOEQsYUFBYSxDQUFDLENBQUMsRUFBRWhCLEtBQUttQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQ3JDakUscUZBQWlDLEdBQ2pDLEdBQ0gsQ0FBQzs7b0JBRUg2QztvQkFBTTtvQkFBRUUsMkJBQWEsOERBQUNVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNbkM7QUFFQSxTQUFTTjtJQUNQLE1BQU1vQixlQUFlL0QsZ0VBQWVBO0lBQ3BDLE1BQU1nRSxlQUFlL0QsaUVBQWdCQTtJQUNyQyxNQUFNVSxJQUFJVCx3REFBT0E7SUFFakIsTUFBTStELFdBQVcsQ0FBQyxFQUNoQkMsSUFBSSxFQUNKQyxXQUFXLEtBQUssRUFJakI7UUFDQyxxQkFDRSw4REFBQ0M7WUFDQ3ZCLFNBQVM7Z0JBQ1AsSUFBSXNCLFVBQVU7Z0JBQ2RKLGFBQWFHO1lBQ2Y7WUFDQXpDLFdBQVcsQ0FBQyxFQUFFMEMsV0FBVzNFLGdHQUE0QyxHQUFHLEdBQ3JFLENBQUMsRUFBRUEscUZBQW9DLENBQUMsQ0FBQztzQkFFM0MwRSxTQUFTLE9BQU92RCxFQUFFLGFBQWFBLEVBQUU7Ozs7OztJQUd4QztJQUNBLHFCQUNFLDhEQUFDSDtRQUFJaUIsV0FBV2pDLCtFQUEyQjs7MEJBQ3pDLDhEQUFDeUU7Z0JBQVNDLE1BQUs7Z0JBQUtDLFVBQVVILGlCQUFpQjs7Ozs7OzBCQUMvQyw4REFBQ0k7MEJBQUs7Ozs7OzswQkFDTiw4REFBQ0g7Z0JBQVNDLE1BQUs7Z0JBQUtDLFVBQVVILGlCQUFpQjs7Ozs7Ozs7Ozs7O0FBR3JEO0FBRUEsU0FBU007SUFDUCxxQkFDRSw4REFBQzlEO1FBQUlpQixXQUFXakMsNkVBQXlCO2tCQUN2Qyw0RUFBQ0Usa0RBQUtBO1lBQ0pxQyxLQUFLO1lBQ0xDLE9BQU87WUFDUEYsUUFBUTtZQUNSRyxLQUFJOzs7Ozs7Ozs7OztBQUlaIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS91aS9ob21lL25hdi50c3g/MGY1OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuaW1wb3J0IHN0eWxlcyBmcm9tICcuL2hvbWUubW9kdWxlLnNjc3MnXHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcclxuaW1wb3J0IEltYWdlIGZyb20gJ25leHQvaW1hZ2UnXHJcbmltcG9ydCBEcm9wZG93bldpbmRvdyBmcm9tICcuLi9jb21wb25lbnRzL2Ryb3Bkb3duLXdpbmRvdydcclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xyXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcclxuaW1wb3J0IE5hdkxpc3QgZnJvbSAnLi4vY29tcG9uZW50cy9uYXYtbGlzdCdcclxuaW1wb3J0IHtcclxuICB1c2VDaGFuZ2VMb2NhbGUsXHJcbiAgdXNlQ3VycmVudExvY2FsZSxcclxuICB1c2VJMThuLFxyXG4gIEkxOG5Qcm92aWRlckNsaWVudCxcclxufSBmcm9tICdAL2xvY2FsZXMvY2xpZW50J1xyXG5pbXBvcnQgeyB1c2VTZWxlY3RlZExheW91dFNlZ21lbnQgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOYXZMYXlvdXQoKSB7XHJcbiAgY29uc3Qgc2VsZWN0ZWRMYXlvdXRTZWdtZW50ID0gdXNlU2VsZWN0ZWRMYXlvdXRTZWdtZW50KClcclxuICBjb25zdCBpc05vdEZvdW5kUGFnZSA9IHNlbGVjdGVkTGF5b3V0U2VnbWVudCA9PT0gJ19fREVGQVVMVF9fJ1xyXG5cclxuICBpZiAoaXNOb3RGb3VuZFBhZ2UpIHJldHVybiA8ZGl2PjwvZGl2PlxyXG4gIGVsc2VcclxuICAgIHJldHVybiAoXHJcbiAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1ob29rcy9ydWxlcy1vZi1ob29rc1xyXG4gICAgICA8STE4blByb3ZpZGVyQ2xpZW50IGxvY2FsZT17dXNlQ3VycmVudExvY2FsZSgpfT5cclxuICAgICAgICA8TmF2IC8+XHJcbiAgICAgIDwvSTE4blByb3ZpZGVyQ2xpZW50PlxyXG4gICAgKVxyXG59XHJcblxyXG5mdW5jdGlvbiBOYXYoKSB7XHJcbiAgY29uc3QgdCA9IHVzZUkxOG4oKVxyXG4gIGNvbnN0IFtpc1Njcm9sbGVkLCBzZXRJc1Njcm9sbGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG4gIGNvbnN0IFtpc1Nob3dNZW51LCBzZXRTaG93TWVudV0gPSB1c2VTdGF0ZShmYWxzZSlcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIOebkeWQrOa7muWKqOS6i+S7tlxyXG4gICAgY29uc3QgaGFuZGxlU2Nyb2xsID0gKCkgPT4ge1xyXG4gICAgICAvLyDojrflj5blvZPliY3mu5rliqjkvY3nva5cclxuICAgICAgY29uc3Qgc2Nyb2xsVG9wID0gd2luZG93LnNjcm9sbFlcclxuICAgICAgLy8g5qC55o2u5rua5Yqo5L2N572u5piv5ZCm5aSn5LqOMOadpeWIpOaWreaYr+WQpua3u+WKoOaKleW9seaViOaenFxyXG4gICAgICBzZXRJc1Njcm9sbGVkKHNjcm9sbFRvcCA+IDApXHJcbiAgICB9XHJcblxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIGhhbmRsZVNjcm9sbClcclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGhhbmRsZU5hdkxpc3RDbGljayA9ICgpID0+IHtcclxuICAgIHNldFNob3dNZW51KGZhbHNlKVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgc3R5bGU9e3sgcG9zaXRpb246ICdyZWxhdGl2ZScgfX0+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubmF2X19wbGFjZWhvbGRlcn0+PC9kaXY+XHJcbiAgICAgIDxkaXZcclxuICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5uYXZ9ICR7aXNTY3JvbGxlZCA/IHN0eWxlc1snbmF2LS1zY3JvbGxlZCddIDogJydcclxuICAgICAgICAgIH0gJHtpc1Nob3dNZW51ID8gc3R5bGVzWyduYXYtLXNjcm9sbGVkJ10gOiAnJ31gfVxyXG4gICAgICA+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5uYXZfX2NvbnRlbnR9PlxyXG4gICAgICAgICAgey8qIOWumOe9keWbvueJhyAqL31cclxuICAgICAgICAgIDxMaW5rIGhyZWY9eycvJ30gc3R5bGU9e3sgaGVpZ2h0OiA0NCB9fT5cclxuICAgICAgICAgICAgPEltYWdlXHJcbiAgICAgICAgICAgICAgc3JjPXsnL2N5bGFuX2xvZ28ucG5nJ31cclxuICAgICAgICAgICAgICB3aWR0aD17MTI1fVxyXG4gICAgICAgICAgICAgIGhlaWdodD17NDR9XHJcbiAgICAgICAgICAgICAgYWx0PVwiQ3lsYW4gTG9nb1wiXHJcbiAgICAgICAgICAgICAgdW5vcHRpbWl6ZWRcclxuICAgICAgICAgICAgPjwvSW1hZ2U+XHJcbiAgICAgICAgICA8L0xpbms+XHJcblxyXG4gICAgICAgICAgey8qIOWvvOiIquWIl+ihqCAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzdHlsZXMubmF2X19saXN0fSBoaWRlLW9uLXNtYWxsIGhpZGUtb24tbWVkaXVtYH0+XHJcbiAgICAgICAgICAgIDxOYXZMaXN0SXRlbSB0aXRsZT17dCgnaG9tZScpfSBsaW5rPVwiL1wiIC8+XHJcbiAgICAgICAgICAgIDxOYXZMaXN0SXRlbVxyXG4gICAgICAgICAgICAgIHRpdGxlPXt0KCdwcm9kdWN0Q2VudGVyJyl9XHJcbiAgICAgICAgICAgICAgc2hvd0Fycm93XHJcbiAgICAgICAgICAgICAgbGluaz1cIi9wcm9kdWN0XCJcclxuICAgICAgICAgICAgICBsaW5rcz17W1xyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICBsaW5rOiAnL3Byb2R1Y3Q/dGFiPTAxJyxcclxuICAgICAgICAgICAgICAgICAgdGV4dDogdCgncHJvZHVjdENhbWVyYScpLFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgbGluazogJy9wcm9kdWN0P3RhYj0wMicsXHJcbiAgICAgICAgICAgICAgICAgIHRleHQ6IHQoJ3Byb2R1Y3RUcmFuc2xhdG9yJyksXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIHsvKiA8TmF2TGlzdEl0ZW0gdGl0bGU9eyfkuqflk4Hop4bpopEnfSBzaG93QXJyb3cgbGluaz1cIi92aWRlb3NcIiAvPiAqL31cclxuICAgICAgICAgICAgPE5hdkxpc3RJdGVtXHJcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ3N1cHBvcnQnKX1cclxuICAgICAgICAgICAgICBzaG93QXJyb3dcclxuICAgICAgICAgICAgICBsaW5rPVwiL3N1cHBvcnRcIlxyXG4gICAgICAgICAgICAgIGxpbmtzPXtbXHJcbiAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgIGxpbms6ICcvc3VwcG9ydC9kb3dubG9hZF9jbGllbnQnLFxyXG4gICAgICAgICAgICAgICAgICB0ZXh0OiB0KCdkb3dubG9hZENsaWVudCcpLFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgbGluazogJy9zdXBwb3J0L2hlbHAnLFxyXG4gICAgICAgICAgICAgICAgICB0ZXh0OiB0KCdoZWxwJyksXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIHsvKiA8TmF2TGlzdEl0ZW0gdGl0bGU9eyfmlrDpl7votYTorq8nfSBsaW5rPVwiL25ld3NcIiAvPiAqL31cclxuICAgICAgICAgICAgPE5hdkxpc3RJdGVtIHRpdGxlPXt0KCdhYm91dFVzJyl9IGxpbms9XCIvYWJvdXRcIiAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgey8qIOaQnOe0ouWSjOivreiogCAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubmF2X19yaWdodH0+XHJcbiAgICAgICAgICAgIDxMYW5ndWFnZSAvPlxyXG4gICAgICAgICAgICB7LyogPFNlYXJjaCAvPiAqL31cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgIHNldFNob3dNZW51KCFpc1Nob3dNZW51KVxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtzdHlsZXMubmF2X19yaWdodF9fbWVudX0gaGlkZS1vbi1sYXJnZWB9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8SW1hZ2VcclxuICAgICAgICAgICAgICAgIHNyYz17Jy9tZW51LnN2Zyd9XHJcbiAgICAgICAgICAgICAgICB3aWR0aD17NDl9XHJcbiAgICAgICAgICAgICAgICBoZWlnaHQ9ezUwfVxyXG4gICAgICAgICAgICAgICAgYWx0PVwibWVudVwiXHJcbiAgICAgICAgICAgICAgPjwvSW1hZ2U+XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICB7aXNTaG93TWVudSAmJiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzdHlsZXMubmF2X19kcm9wfSBoaWRlLW9uLWxhcmdlYH0+XHJcbiAgICAgICAgICAgIDxOYXZMaXN0IG9uQ2xpY2s9eygpID0+IGhhbmRsZU5hdkxpc3RDbGljaygpfSAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17c3R5bGVzLm5hdl9fbWFza31cclxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgIHNldFNob3dNZW51KGZhbHNlKVxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgPjwvZGl2PlxyXG4gICAgICAgIDwvPlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcblxyXG5mdW5jdGlvbiBBcnJvd0Rvd24oKSB7XHJcbiAgcmV0dXJuIDxJbWFnZSBzcmM9eycvYXJyb3ctZG93bi5zdmcnfSBoZWlnaHQ9ezEwfSB3aWR0aD17MTZ9IGFsdD1cIlwiPjwvSW1hZ2U+XHJcbn1cclxuXHJcbmZ1bmN0aW9uIE5hdkxpc3RJdGVtKHtcclxuICB0aXRsZSxcclxuICBsaW5rID0gJy8nLFxyXG4gIHNob3dBcnJvdyA9IGZhbHNlLFxyXG4gIGxpbmtzID0gW10sXHJcbn06IHtcclxuICB0aXRsZTogc3RyaW5nXHJcbiAgc2hvd0Fycm93PzogYm9vbGVhblxyXG4gIGxpbms6IHN0cmluZ1xyXG4gIGxpbmtzPzogQXJyYXk8e1xyXG4gICAgbGluazogc3RyaW5nXHJcbiAgICB0ZXh0OiBzdHJpbmdcclxuICB9PlxyXG59KSB7XHJcbiAgLy8g6byg5qCH5oKs5YGcXHJcbiAgY29uc3QgW2lzSG92ZXJlZCwgc2V0SXNIb3ZlcmVkXSA9IHVzZVN0YXRlKGZhbHNlKVxyXG5cclxuICBjb25zdCBoYW5kbGVNb3VzZUVudGVyID0gKCkgPT4ge1xyXG4gICAgc2V0SXNIb3ZlcmVkKHRydWUpXHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVNb3VzZUxlYXZlID0gKCkgPT4ge1xyXG4gICAgc2V0SXNIb3ZlcmVkKGZhbHNlKVxyXG4gIH1cclxuXHJcbiAgbGV0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxyXG4gIGlmIChwYXRobmFtZS5pbmNsdWRlcygnL3poLycpIHx8IHBhdGhuYW1lLmluY2x1ZGVzKCcvZW4vJykpIHtcclxuICAgIHBhdGhuYW1lID0gcGF0aG5hbWUucmVwbGFjZSgvXFwvemh8XFwvZW4vLCAnJylcclxuICB9IGVsc2Uge1xyXG4gICAgcGF0aG5hbWUgPSAnLydcclxuICB9XHJcblxyXG4gIHBhdGhuYW1lID0gJy8nICsgcGF0aG5hbWUuc3BsaXQoJy8nKVsxXVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBvbk1vdXNlRW50ZXI9e2hhbmRsZU1vdXNlRW50ZXJ9IG9uTW91c2VMZWF2ZT17aGFuZGxlTW91c2VMZWF2ZX0+XHJcbiAgICAgIHtzaG93QXJyb3cgPyAoXHJcbiAgICAgICAgPERyb3Bkb3duV2luZG93XHJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc0hvdmVyZWQoZmFsc2UpfVxyXG4gICAgICAgICAgbGlzdD17bGlua3N9XHJcbiAgICAgICAgICBzaG93PXtpc0hvdmVyZWR9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPExpbmsgaHJlZj17bGlua30+XHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5uYXZfX2xpc3RfX2l0ZW19ICR7cGF0aG5hbWUgPT09IGxpbmsuc3BsaXQoJz8nKVswXVxyXG4gICAgICAgICAgICAgICAgICA/IHN0eWxlc1snbmF2X19saXN0X19pdGVtLS1hY3RpdmUnXVxyXG4gICAgICAgICAgICAgICAgICA6ICcnXHJcbiAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHt0aXRsZX0ge3Nob3dBcnJvdyAmJiA8QXJyb3dEb3duIC8+fVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICA8L0Ryb3Bkb3duV2luZG93PlxyXG4gICAgICApIDogKFxyXG4gICAgICAgIDxMaW5rIGhyZWY9e2xpbmt9PlxyXG4gICAgICAgICAgPGRpdlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5uYXZfX2xpc3RfX2l0ZW19ICR7c3R5bGVzWyduYXZfX2xpc3RfX2l0ZW0tLWxpbmsnXVxyXG4gICAgICAgICAgICAgIH0gJHtwYXRobmFtZSA9PT0gYC8ke2xpbmsuc3BsaXQoJy8nKVsxXX1gXHJcbiAgICAgICAgICAgICAgICA/IHN0eWxlc1snbmF2X19saXN0X19pdGVtLS1hY3RpdmUnXVxyXG4gICAgICAgICAgICAgICAgOiAnJ1xyXG4gICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7dGl0bGV9IHtzaG93QXJyb3cgJiYgPEFycm93RG93biAvPn1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvTGluaz5cclxuICAgICAgKX1cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG5cclxuZnVuY3Rpb24gTGFuZ3VhZ2UoKSB7XHJcbiAgY29uc3QgY2hhbmdlTG9jYWxlID0gdXNlQ2hhbmdlTG9jYWxlKClcclxuICBjb25zdCBjdXJyZW50TG9jYWwgPSB1c2VDdXJyZW50TG9jYWxlKClcclxuICBjb25zdCB0ID0gdXNlSTE4bigpXHJcblxyXG4gIGNvbnN0IExhbmdJdGVtID0gKHtcclxuICAgIGxhbmcsXHJcbiAgICBpc0FjdGl2ZSA9IGZhbHNlLFxyXG4gIH06IHtcclxuICAgIGxhbmc6ICd6aCcgfCAnZW4nXHJcbiAgICBpc0FjdGl2ZT86IGJvb2xlYW5cclxuICB9KSA9PiB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8c3BhblxyXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgIGlmIChpc0FjdGl2ZSkgcmV0dXJuXHJcbiAgICAgICAgICBjaGFuZ2VMb2NhbGUobGFuZylcclxuICAgICAgICB9fVxyXG4gICAgICAgIGNsYXNzTmFtZT17YCR7aXNBY3RpdmUgPyBzdHlsZXNbJ25hdl9fcmlnaHRfX2xhbmd1YWdlX190ZXh0LS1hY3RpdmUnXSA6ICcnXHJcbiAgICAgICAgICB9ICR7c3R5bGVzWyduYXZfX3JpZ2h0X19sYW5ndWFnZV9fdGV4dCddfWB9XHJcbiAgICAgID5cclxuICAgICAgICB7bGFuZyA9PT0gJ3poJyA/IHQoJ2NoaW5lc2UnKSA6IHQoJ2VuZ2xpc2gnKX1cclxuICAgICAgPC9zcGFuPlxyXG4gICAgKVxyXG4gIH1cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5uYXZfX3JpZ2h0X19sYW5ndWFnZX0+XHJcbiAgICAgIDxMYW5nSXRlbSBsYW5nPVwiemhcIiBpc0FjdGl2ZT17Y3VycmVudExvY2FsID09PSAnemgnfSAvPlxyXG4gICAgICA8c3Bhbj4vPC9zcGFuPlxyXG4gICAgICA8TGFuZ0l0ZW0gbGFuZz1cImVuXCIgaXNBY3RpdmU9e2N1cnJlbnRMb2NhbCA9PT0gJ2VuJ30gLz5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG5cclxuZnVuY3Rpb24gU2VhcmNoKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5hdl9fcmlnaHRfX3NlYXJjaH0+XHJcbiAgICAgIDxJbWFnZVxyXG4gICAgICAgIHNyYz17Jy9zZWFyY2gtaWNvbi5zdmcnfVxyXG4gICAgICAgIHdpZHRoPXsyMH1cclxuICAgICAgICBoZWlnaHQ9ezIwfVxyXG4gICAgICAgIGFsdD1cIlNlYXJjaCBpY29uXCJcclxuICAgICAgPjwvSW1hZ2U+XHJcbiAgICA8L2Rpdj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbInN0eWxlcyIsIkxpbmsiLCJJbWFnZSIsIkRyb3Bkb3duV2luZG93IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VQYXRobmFtZSIsIk5hdkxpc3QiLCJ1c2VDaGFuZ2VMb2NhbGUiLCJ1c2VDdXJyZW50TG9jYWxlIiwidXNlSTE4biIsIkkxOG5Qcm92aWRlckNsaWVudCIsInVzZVNlbGVjdGVkTGF5b3V0U2VnbWVudCIsIk5hdkxheW91dCIsInNlbGVjdGVkTGF5b3V0U2VnbWVudCIsImlzTm90Rm91bmRQYWdlIiwiZGl2IiwibG9jYWxlIiwiTmF2IiwidCIsImlzU2Nyb2xsZWQiLCJzZXRJc1Njcm9sbGVkIiwiaXNTaG93TWVudSIsInNldFNob3dNZW51IiwiaGFuZGxlU2Nyb2xsIiwic2Nyb2xsVG9wIiwid2luZG93Iiwic2Nyb2xsWSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiaGFuZGxlTmF2TGlzdENsaWNrIiwic3R5bGUiLCJwb3NpdGlvbiIsImNsYXNzTmFtZSIsIm5hdl9fcGxhY2Vob2xkZXIiLCJuYXYiLCJuYXZfX2NvbnRlbnQiLCJocmVmIiwiaGVpZ2h0Iiwic3JjIiwid2lkdGgiLCJhbHQiLCJ1bm9wdGltaXplZCIsIm5hdl9fbGlzdCIsIk5hdkxpc3RJdGVtIiwidGl0bGUiLCJsaW5rIiwic2hvd0Fycm93IiwibGlua3MiLCJ0ZXh0IiwibmF2X19yaWdodCIsIkxhbmd1YWdlIiwiYnV0dG9uIiwib25DbGljayIsIm5hdl9fcmlnaHRfX21lbnUiLCJuYXZfX2Ryb3AiLCJuYXZfX21hc2siLCJBcnJvd0Rvd24iLCJpc0hvdmVyZWQiLCJzZXRJc0hvdmVyZWQiLCJoYW5kbGVNb3VzZUVudGVyIiwiaGFuZGxlTW91c2VMZWF2ZSIsInBhdGhuYW1lIiwiaW5jbHVkZXMiLCJyZXBsYWNlIiwic3BsaXQiLCJvbk1vdXNlRW50ZXIiLCJvbk1vdXNlTGVhdmUiLCJsaXN0Iiwic2hvdyIsIm5hdl9fbGlzdF9faXRlbSIsImNoYW5nZUxvY2FsZSIsImN1cnJlbnRMb2NhbCIsIkxhbmdJdGVtIiwibGFuZyIsImlzQWN0aXZlIiwic3BhbiIsIm5hdl9fcmlnaHRfX2xhbmd1YWdlIiwiU2VhcmNoIiwibmF2X19yaWdodF9fc2VhcmNoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/product/product-list.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/ui/product/product-list.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductListLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _product_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./product.module.scss */ \"(ssr)/./app/[locale]/ui/product/product.module.scss\");\n/* harmony import */ var _product_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_product_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_page_tabs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/page-tabs */ \"(ssr)/./app/[locale]/ui/components/page-tabs.tsx\");\n/* harmony import */ var _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/flex-4items-box */ \"(ssr)/./app/[locale]/ui/components/flex-4items-box.tsx\");\n/* harmony import */ var _components_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/pagination */ \"(ssr)/./app/[locale]/ui/components/pagination.tsx\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/locales/client */ \"(ssr)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction ProductListLayout({ groups, products }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_locales_client__WEBPACK_IMPORTED_MODULE_5__.I18nProviderClient, {\n        locale: (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductList, {\n            groups: groups,\n            products: products\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\nfunction ProductList({ groups, products, initialTab }) {\n    const t = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useI18n)();\n    const tabs = [\n        ...groups\n    ];\n    tabs.unshift({\n        id: \"00\",\n        name: t(\"all\"),\n        nameEn: t(\"all\")\n    });\n    const _tabs = [\n        {\n            id: \"0\",\n            text: \"全部\"\n        },\n        {\n            id: \"1\",\n            text: \"摄像机类型\"\n        }\n    ];\n    // 先定义辅助函数\n    const getCurrentProducts = (page, groupId)=>{\n        let datas = products.filter((_, index)=>index >= (page - 1) * 8 && index < page * 8);\n        if (groupId !== tabs[0].id) datas = datas.filter((item)=>item.groupId === groupId);\n        return datas;\n    };\n    const getTabProducts = (groupId)=>{\n        if (groupId === tabs[0].id) return products;\n        else {\n            return products.filter((item)=>item.groupId === groupId);\n        }\n    };\n    // 根据initialTab设置初始选中的tab\n    const getInitialTab = ()=>{\n        if (initialTab) {\n            // 检查initialTab是否存在于tabs中\n            const foundTab = tabs.find((tab)=>tab.id === initialTab);\n            if (foundTab) {\n                return initialTab;\n            }\n        }\n        return tabs[0].id;\n    };\n    const initialTabValue = getInitialTab();\n    const [currentTab, setCurrentTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTabValue);\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getTabProducts(initialTabValue).length);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentProducts, setCurrentProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getCurrentProducts(1, initialTabValue));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_page_tabs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: t(\"productCenter\"),\n                iconSrc: \"/product-center-icon.svg\",\n                currentTab: currentTab,\n                tabs: _tabs,\n                background: \"rgb(200,228,250)\",\n                bannerSrc: \"/product/banner-pc.jpg\",\n                bannerMobileSrc: \"/product/banner-mobile.png\",\n                isSearch: true,\n                onTabChange: (tab)=>{\n                    setCurrentTab(tab);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list\"]),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                        tabs: tabs,\n                        currentTab: currentTab,\n                        onClick: (id)=>{\n                            setCurrentTab(id);\n                            setCurrentPage(1);\n                            const products = getCurrentProducts(1, id);\n                            setCount(getTabProducts(id).length);\n                            setCurrentProducts(products);\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(List, {\n                        products: currentProducts\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListPagination, {\n                        currentPage: currentPage,\n                        count: count,\n                        onChange: (page)=>{\n                            setCurrentPage(page);\n                            setCurrentProducts(getCurrentProducts(page, currentTab));\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction Tabs({ tabs, currentTab, onClick }) {\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const TabItem = ({ name, id, nameEn })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: ()=>{\n                if (id !== currentTab) onClick(id);\n            },\n            className: `${(_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs__item\"])} ${currentTab === id ? (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs__item--active\"]) : \"\"}`,\n            children: locale === \"zh\" ? name : nameEn\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 161,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__tabs\"])} hide-on-small`,\n        children: tabs.map((item, index)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(TabItem, {\n                ...item,\n                key: index,\n                __source: {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                },\n                __self: this\n            }))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\nfunction List({ products }) {\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_5__.useCurrentLocale)();\n    const infos = products.map((item)=>{\n        return {\n            imageSrc: item.imageSrc,\n            title: locale === \"zh\" ? item.name : item.nameEn,\n            tip: locale === \"zh\" ? item.description : item.descriptionEn,\n            link: item.id === \"c31\" ? `/product/c31` : \"\"\n        };\n    });\n    const productsInfo = {\n        infos,\n        imageSize: {\n            width: 200,\n            height: 200\n        },\n        imageBox: {\n            width: 300,\n            height: 300\n        },\n        mode: _components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__.itemsMode.product\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default()[\"product-list__items\"]),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flex_4items_box__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            ...productsInfo\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, this);\n}\nfunction ListPagination({ currentPage, count, onChange }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_product_module_scss__WEBPACK_IMPORTED_MODULE_6___default().pagination),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pagination__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                currentPage: currentPage,\n                count: count,\n                onChange: (page)=>{\n                    onChange(page);\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\product\\\\product-list.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/product/product-list.tsx\n");

/***/ }),

/***/ "(ssr)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/error */ \"(ssr)/./node_modules/next/error.js\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// Render the default Next.js 404 page when a route\n// is requested that doesn't match the middleware and\n// therefore doesn't have a locale associated with it.\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_1___default()), {\n                statusCode: 404\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFOEI7QUFFOUIsbURBQW1EO0FBQ25ELHFEQUFxRDtBQUNyRCxzREFBc0Q7QUFFdkMsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUNDLDRFQUFDSixtREFBS0E7Z0JBQUNLLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL25vdC1mb3VuZC50c3g/NWM4MCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCBFcnJvciBmcm9tICduZXh0L2Vycm9yJ1xyXG5cclxuLy8gUmVuZGVyIHRoZSBkZWZhdWx0IE5leHQuanMgNDA0IHBhZ2Ugd2hlbiBhIHJvdXRlXHJcbi8vIGlzIHJlcXVlc3RlZCB0aGF0IGRvZXNuJ3QgbWF0Y2ggdGhlIG1pZGRsZXdhcmUgYW5kXHJcbi8vIHRoZXJlZm9yZSBkb2Vzbid0IGhhdmUgYSBsb2NhbGUgYXNzb2NpYXRlZCB3aXRoIGl0LlxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8RXJyb3Igc3RhdHVzQ29kZT17NDA0fSAvPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJFcnJvciIsIk5vdEZvdW5kIiwiaHRtbCIsImxhbmciLCJib2R5Iiwic3RhdHVzQ29kZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/not-found.tsx\n");

/***/ }),

/***/ "(ssr)/./locales/client.ts":
/*!***************************!*\
  !*** ./locales/client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProviderClient: () => (/* binding */ I18nProviderClient),\n/* harmony export */   useChangeLocale: () => (/* binding */ useChangeLocale),\n/* harmony export */   useCurrentLocale: () => (/* binding */ useCurrentLocale),\n/* harmony export */   useI18n: () => (/* binding */ useI18n),\n/* harmony export */   useScopedI18n: () => (/* binding */ useScopedI18n)\n/* harmony export */ });\n/* harmony import */ var next_international_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-international/client */ \"(ssr)/./node_modules/next-international/dist/app/client/index.js\");\n/* harmony import */ var next_international_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_international_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst { useI18n, useScopedI18n, I18nProviderClient, useCurrentLocale, useChangeLocale } = (0,next_international_client__WEBPACK_IMPORTED_MODULE_0__.createI18nClient)({\n    en: ()=>__webpack_require__.e(/*! import() */ \"_ssr_locales_en_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./en */ \"(ssr)/./locales/en.ts\")),\n    zh: ()=>__webpack_require__.e(/*! import() */ \"_ssr_locales_zh_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./zh */ \"(ssr)/./locales/zh.ts\"))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9sb2NhbGVzL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZEO0FBRXRELE1BQU0sRUFDWEMsT0FBTyxFQUNQQyxhQUFhLEVBQ2JDLGtCQUFrQixFQUNsQkMsZ0JBQWdCLEVBQ2hCQyxlQUFlLEVBQ2hCLEdBQUdMLDJFQUFnQkEsQ0FBQztJQUNuQk0sSUFBSSxJQUFNLG9KQUFjO0lBQ3hCQyxJQUFJLElBQU0sb0pBQWM7QUFDMUIsR0FBRyIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9sb2NhbGVzL2NsaWVudC50cz9jZjQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUkxOG5DbGllbnQgfSBmcm9tIFwibmV4dC1pbnRlcm5hdGlvbmFsL2NsaWVudFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IHtcclxuICB1c2VJMThuLFxyXG4gIHVzZVNjb3BlZEkxOG4sXHJcbiAgSTE4blByb3ZpZGVyQ2xpZW50LFxyXG4gIHVzZUN1cnJlbnRMb2NhbGUsXHJcbiAgdXNlQ2hhbmdlTG9jYWxlLFxyXG59ID0gY3JlYXRlSTE4bkNsaWVudCh7XHJcbiAgZW46ICgpID0+IGltcG9ydChcIi4vZW5cIiksXHJcbiAgemg6ICgpID0+IGltcG9ydChcIi4vemhcIiksXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlSTE4bkNsaWVudCIsInVzZUkxOG4iLCJ1c2VTY29wZWRJMThuIiwiSTE4blByb3ZpZGVyQ2xpZW50IiwidXNlQ3VycmVudExvY2FsZSIsInVzZUNoYW5nZUxvY2FsZSIsImVuIiwiemgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./locales/client.ts\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/components/components.scss":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/components.scss ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e6ee3d37fbc5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvW2xvY2FsZV0vdWkvY29tcG9uZW50cy9jb21wb25lbnRzLnNjc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vYXBwL1tsb2NhbGVdL3VpL2NvbXBvbmVudHMvY29tcG9uZW50cy5zY3NzPzQ4MzAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlNmVlM2QzN2ZiYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/components/components.scss\n");

/***/ }),

/***/ "(rsc)/./app/globals.scss":
/*!**************************!*\
  !*** ./app/globals.scss ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dabe1292f855\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5zY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9nbG9iYWxzLnNjc3M/YWZhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImRhYmUxMjkyZjg1NVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.scss\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/page.module.scss":
/*!***************************************!*\
  !*** ./app/[locale]/page.module.scss ***!
  \***************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"page404container\": \"page_page404container__c1LIB\",\n\t\"page404\": \"page_page404__1cE3u\",\n\t\"page404__content\": \"page_page404__content__oco9m\",\n\t\"page404__buttons\": \"page_page404__buttons__8oWnx\",\n\t\"page404__buttons__back\": \"page_page404__buttons__back__z0MU7\",\n\t\"page404__buttons__home\": \"page_page404__buttons__home__sx9Sv\"\n};\n\nmodule.exports.__checksum = \"346715d5ce1e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vcGFnZS5tb2R1bGUuc2NzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9bbG9jYWxlXS9wYWdlLm1vZHVsZS5zY3NzPzQ0MWUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicGFnZTQwNGNvbnRhaW5lclwiOiBcInBhZ2VfcGFnZTQwNGNvbnRhaW5lcl9fYzFMSUJcIixcblx0XCJwYWdlNDA0XCI6IFwicGFnZV9wYWdlNDA0X18xY0UzdVwiLFxuXHRcInBhZ2U0MDRfX2NvbnRlbnRcIjogXCJwYWdlX3BhZ2U0MDRfX2NvbnRlbnRfX29jbzltXCIsXG5cdFwicGFnZTQwNF9fYnV0dG9uc1wiOiBcInBhZ2VfcGFnZTQwNF9fYnV0dG9uc19fOG9XbnhcIixcblx0XCJwYWdlNDA0X19idXR0b25zX19iYWNrXCI6IFwicGFnZV9wYWdlNDA0X19idXR0b25zX19iYWNrX196ME1VN1wiLFxuXHRcInBhZ2U0MDRfX2J1dHRvbnNfX2hvbWVcIjogXCJwYWdlX3BhZ2U0MDRfX2J1dHRvbnNfX2hvbWVfX3N4OVN2XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcIjM0NjcxNWQ1Y2UxZVwiXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/page.module.scss\n");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/home/<USER>":
/*!***********************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \***********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"nav--scrolled\": \"home_nav--scrolled__f5oaX\",\n\t\"nav\": \"home_nav__gr65i\",\n\t\"nav__placeholder\": \"home_nav__placeholder__R_bDj\",\n\t\"nav__content\": \"home_nav__content__gXoig\",\n\t\"nav__list\": \"home_nav__list__dmRBz\",\n\t\"nav__list__item\": \"home_nav__list__item__Ti9E4\",\n\t\"nav__list__item--link\": \"home_nav__list__item--link__dx88I\",\n\t\"nav__list__item--active\": \"home_nav__list__item--active__oPRJX\",\n\t\"nav__right\": \"home_nav__right__4GRXj\",\n\t\"nav__right__language\": \"home_nav__right__language__YzU4O\",\n\t\"nav__right__language__text\": \"home_nav__right__language__text__yUNmB\",\n\t\"nav__right__language__text--active\": \"home_nav__right__language__text--active__e4h1y\",\n\t\"nav__right__search\": \"home_nav__right__search__QAvd_\",\n\t\"nav__right__menu\": \"home_nav__right__menu__tMG4s\",\n\t\"nav__drop\": \"home_nav__drop__RNd3y\",\n\t\"nav__mask\": \"home_nav__mask__YVj5E\",\n\t\"banner-slider\": \"home_banner-slider__UBj9I\",\n\t\"banner-slider__list\": \"home_banner-slider__list__ZtAvw\",\n\t\"banner-slider__slide\": \"home_banner-slider__slide__2U7Uu\",\n\t\"banner-slider__switcher\": \"home_banner-slider__switcher__SoaxS\",\n\t\"banner-slider__switcher--right\": \"home_banner-slider__switcher--right___84yN\",\n\t\"banner-slider__switcher--disabled\": \"home_banner-slider__switcher--disabled__4Jzfw\",\n\t\"banner-slider__indicator\": \"home_banner-slider__indicator__0OOU4\",\n\t\"banner-slider__indicator__item\": \"home_banner-slider__indicator__item__f8vBh\",\n\t\"banner-slider__indicator__item--active\": \"home_banner-slider__indicator__item--active__Pkcak\",\n\t\"hot-spot\": \"home_hot-spot__HmXBc\",\n\t\"hot-spot__captain\": \"home_hot-spot__captain__P7sAg\",\n\t\"hot-spot__captain__more\": \"home_hot-spot__captain__more__hoe30\",\n\t\"hot-spot__news\": \"home_hot-spot__news__mFPbX\",\n\t\"hot-spot__news__left\": \"home_hot-spot__news__left__bYNbF\",\n\t\"hot-spot__news__right\": \"home_hot-spot__news__right__IYxxG\",\n\t\"hot-spot__news__item\": \"home_hot-spot__news__item__i6svw\",\n\t\"hot-spot__news__item__info\": \"home_hot-spot__news__item__info__GSDkz\",\n\t\"hot-spot__news__item__image\": \"home_hot-spot__news__item__image__0Dj0A\",\n\t\"hot-spot__news__item__image--right\": \"home_hot-spot__news__item__image--right__scey9\",\n\t\"hot-spot__news__item--left\": \"home_hot-spot__news__item--left__W7YL9\",\n\t\"about\": \"home_about__vPbFi\",\n\t\"about__cover\": \"home_about__cover__SPvuD\",\n\t\"about__content\": \"home_about__content__EA9EW\",\n\t\"about__content__time\": \"home_about__content__time__HcHq6\",\n\t\"about__content__time__item\": \"home_about__content__time__item__n4W8C\",\n\t\"about__content__time--page\": \"home_about__content__time--page__Azkeq\",\n\t\"about__content__prides\": \"home_about__content__prides__zHCpT\",\n\t\"contacts\": \"home_contacts__TRH4N\",\n\t\"contacts--page\": \"home_contacts--page__0BV0w\",\n\t\"contacts__info\": \"home_contacts__info__pIGy0\",\n\t\"contacts__info__items\": \"home_contacts__info__items__qUSi9\",\n\t\"contacts__info__title\": \"home_contacts__info__title__3_UHT\",\n\t\"contacts__info__item\": \"home_contacts__info__item__eDIm0\",\n\t\"contacts__address\": \"home_contacts__address___ZQdr\",\n\t\"footer\": \"home_footer__qefFZ\",\n\t\"footer__logo\": \"home_footer__logo__jG71u\",\n\t\"footer__links\": \"home_footer__links__q5uiZ\",\n\t\"footer__links__item\": \"home_footer__links__item__gB0TO\",\n\t\"footer__links__follow\": \"home_footer__links__follow__jv8nP\",\n\t\"footer__links__follow__weixin\": \"home_footer__links__follow__weixin__yeCNp\",\n\t\"footer__copyright\": \"home_footer__copyright__M6lua\",\n\t\"footer__copyright__link\": \"home_footer__copyright__link__PBT0B\"\n};\n\nmodule.exports.__checksum = \"f6db86301aa8\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/home/<USER>");

/***/ }),

/***/ "(ssr)/./app/[locale]/ui/product/product.module.scss":
/*!*****************************************************!*\
  !*** ./app/[locale]/ui/product/product.module.scss ***!
  \*****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"product-list\": \"product_product-list__nFwZu\",\n\t\"product-list__tabs\": \"product_product-list__tabs__EGq1z\",\n\t\"product-list__tabs__item\": \"product_product-list__tabs__item__hKEh0\",\n\t\"product-list__tabs__item--active\": \"product_product-list__tabs__item--active__L1eXV\",\n\t\"product-list__items\": \"product_product-list__items__EZwk3\",\n\t\"pagination\": \"product_pagination__RiZRT\",\n\t\"product-preview\": \"product_product-preview__JUk6N\",\n\t\"product-preview__left__image\": \"product_product-preview__left__image__sZxBA\",\n\t\"product-preview__left__switch\": \"product_product-preview__left__switch__jxy85\",\n\t\"product-preview__left__switch__image-list\": \"product_product-preview__left__switch__image-list__f0lfQ\",\n\t\"product-preview__left__switch__switcher\": \"product_product-preview__left__switch__switcher__ZGIgH\",\n\t\"product-preview__left__switch__image\": \"product_product-preview__left__switch__image__2TIUy\",\n\t\"product-preview__right\": \"product_product-preview__right__pd9wO\",\n\t\"product-preview__right__line\": \"product_product-preview__right__line__HwV3B\",\n\t\"product-preview__right__types\": \"product_product-preview__right__types__aeNfH\",\n\t\"product-preview__right__types__item\": \"product_product-preview__right__types__item__rwDrW\",\n\t\"product-preview__right__types__item--active\": \"product_product-preview__right__types__item--active__rCWIZ\",\n\t\"product-preview__right__properties\": \"product_product-preview__right__properties__HEUmq\",\n\t\"product-preview__right__buttons\": \"product_product-preview__right__buttons__bNbx9\",\n\t\"product-info\": \"product_product-info__SI8WL\",\n\t\"product-info__tabs\": \"product_product-info__tabs__qiGK6\",\n\t\"product-info__tabs__button\": \"product_product-info__tabs__button__O9Yio\",\n\t\"product-info__tabs__button--active\": \"product_product-info__tabs__button--active__kBKmC\",\n\t\"product-info__description\": \"product_product-info__description__unz1S\",\n\t\"product-info__description__image\": \"product_product-info__description__image__0SqVD\",\n\t\"product-info__spec__item\": \"product_product-info__spec__item__ok_g_\",\n\t\"product-info__spec__item--white\": \"product_product-info__spec__item--white__X6XxS\",\n\t\"product-info__videos\": \"product_product-info__videos__rLEpl\",\n\t\"product-info__helps\": \"product_product-info__helps__klLFQ\",\n\t\"product-info__content\": \"product_product-info__content__x3cOl\"\n};\n\nmodule.exports.__checksum = \"e7b69e442c4e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/[locale]/ui/product/product.module.scss\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/layout.tsx":
/*!*********************************!*\
  !*** ./app/[locale]/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\[locale]\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\[locale]\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_globals_scss__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.scss */ \"(rsc)/./app/globals.scss\");\n/* harmony import */ var _app_locale_ui_home_footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_home_nav__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/[locale]/ui/home/<USER>/ \"(rsc)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _app_locale_ui_components_back_to_top__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/[locale]/ui/components/back-to-top */ \"(rsc)/./app/[locale]/ui/components/back-to-top.tsx\");\n\n\n\n\n\n\nasync function generateMetadata({ params, searchParams }, parent) {\n    const locale = params.locale;\n    return {\n        description: \"赛蓝科技 引领生活\",\n        icons: {\n            icon: \"/favicon.ico\"\n        }\n    };\n}\nasync function RootLayout({ children, params }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: params.locale,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_nav__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_home_footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_locale_ui_components_back_to_top__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/not-found.tsx":
/*!************************************!*\
  !*** ./app/[locale]/not-found.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/[locale]/page.module.scss */ \"(rsc)/./app/[locale]/page.module.scss\");\n/* harmony import */ var _app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(rsc)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/locales/server */ \"(rsc)/./locales/server.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n\n\n\nasync function NotFound() {\n    const t = await (0,_locales_server__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__image),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/404.png\",\n                        width: 331,\n                        height: 151,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: t(\"page404Description\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"page404Tip1\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"page404Tip2\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__buttons),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_app_locale_page_module_scss__WEBPACK_IMPORTED_MODULE_4___default().page404__buttons__home),\n                            children: t(\"backToHome\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\not-found.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/product/(overview)/page.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/product/(overview)/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ page),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ui_product_product_list__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../ui/product/product-list */ \"(rsc)/./app/[locale]/ui/product/product-list.tsx\");\n/* harmony import */ var _data_products__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/products */ \"(rsc)/./data/products.ts\");\n\n\n\nasync function generateMetadata({ params, searchParams }, parent) {\n    const locale = params.locale;\n    return {\n        title: locale === \"zh\" ? \"产品中心\" : \"Product\",\n        description: \"赛蓝科技 引领生活\",\n        icons: {\n            icon: \"/favicon.ico\"\n        }\n    };\n}\nasync function page({ searchParams }) {\n    const groups = await (0,_data_products__WEBPACK_IMPORTED_MODULE_2__.getGroupDatas)();\n    const products = await (0,_data_products__WEBPACK_IMPORTED_MODULE_2__.getProductDatas)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_product_product_list__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            groups: groups,\n            products: products,\n            initialTab: searchParams.tab\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\product\\\\(overview)\\\\page.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\product\\\\(overview)\\\\page.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/product/(overview)/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/product/layout.tsx":
/*!*****************************************!*\
  !*** ./app/[locale]/product/layout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\product\\\\layout.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvW2xvY2FsZV0vcHJvZHVjdC9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQSxPQUFPLEVBQzdCQyxRQUFRLEVBR1I7SUFDQSxxQkFBTyw4REFBQ0M7a0JBQUtEOzs7Ozs7QUFDZiIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9hcHAvW2xvY2FsZV0vcHJvZHVjdC9sYXlvdXQudHN4P2I1YjAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufT4pIHtcclxuICByZXR1cm4gPGRpdj57Y2hpbGRyZW59PC9kaXY+XHJcbn1cclxuIl0sIm5hbWVzIjpbIkxheW91dCIsImNoaWxkcmVuIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/product/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/components/back-to-top.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/ui/components/back-to-top.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\components\back-to-top.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!*****************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/home/<USER>":
/*!**************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\nav.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/[locale]/ui/product/product-list.tsx":
/*!**************************************************!*\
  !*** ./app/[locale]/ui/product/product-list.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\product\product-list.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\nasync function RootLayout({ children, params }) {\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsZUFBZUEsV0FBVyxFQUN2Q0MsUUFBUSxFQUNSQyxNQUFNLEVBSU47SUFDQSxPQUFPRDtBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW1jYW1fb2ZmaWNpYWxfc2l0ZS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbiAgcGFyYW1zLFxyXG59OiBSZWFkb25seTx7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxyXG4gIHBhcmFtczogeyBsb2NhbGU6IHN0cmluZyB9XHJcbn0+KSB7XHJcbiAgcmV0dXJuIGNoaWxkcmVuXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsInBhcmFtcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\not-found.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./data/products.ts":
/*!**************************!*\
  !*** ./data/products.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getGroupDatas: () => (/* binding */ getGroupDatas),\n/* harmony export */   getProductData: () => (/* binding */ getProductData),\n/* harmony export */   getProductDatas: () => (/* binding */ getProductDatas)\n/* harmony export */ });\n/* harmony import */ var _type__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./type */ \"(rsc)/./data/type.ts\");\n\nconst productDatas = [\n    {\n        id: \"c31\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-c31.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机C31\",\n        nameEn: \"Video Call Smart Camera C31\",\n        model: \"C31\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"外观升级版双向视频通话设备\",\n        descriptionEn: \"Appearance-Upgraded Two-Way Video Calling Device\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.title,\n                text: \"只为了更好的产品\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"02\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-c41.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机C41\",\n        nameEn: \"Video Call Smart Camera C41\",\n        model: \"C41\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"触屏AI版双向视频通话设备\",\n        descriptionEn: \"Touchscreen AI Version Two-Way Video Calling Device\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"04\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-t30.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机T30-T\",\n        nameEn: \"Video Call Smart Camera T30-T\",\n        model: \"C34\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"涂鸦版双向视频通话设备\",\n        descriptionEn: \"Tuya Version Two-Way Video Calling Device\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"05\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-c21.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机C21\",\n        nameEn: \"Video Call Smart Camera C21\",\n        model: \"C34\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"双向视频通话初代设备\",\n        descriptionEn: \"First-Generation Two-Way Video Calling Device\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"06\",\n        groupId: \"01\",\n        groupName: \"摄像头产品\",\n        imageSrc: \"/product/product-wx1.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"视频通话智能摄像机C31-WX\",\n        nameEn: \"Video Call Smart Camera C31-WX\",\n        model: \"C34\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"腾讯云微通话 (TWeCall) 版双向视频通话设备\",\n        descriptionEn: \"Tencent Cloud WeCall (TWeCall) Version Two-Way Video Calling Device\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    },\n    {\n        id: \"07\",\n        groupId: \"02\",\n        groupName: \"翻译机\",\n        imageSrc: \"/product/product-c21.jpg\",\n        subImageSrcs: [\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\",\n            \"/product/product-c31.jpg\"\n        ],\n        name: \"翻译机\",\n        nameEn: \"Video Call Smart Camera C21\",\n        model: \"C34\",\n        properties: [\n            \"产品重要属性1\",\n            \"产品重要属性2\",\n            \"产品重要属性3\"\n        ],\n        description: \"双向视频通话初代设备\",\n        descriptionEn: \"First-Generation Two-Way Video Calling Device\",\n        types: [\n            {\n                name: \"普通版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"中级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            },\n            {\n                name: \"高级版\",\n                properties: [\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性1\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\",\n                    \"产品重要属性2\",\n                    \"产品重要属性3\"\n                ]\n            }\n        ],\n        information: [\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.paragraph,\n                text: \"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。\"\n            },\n            {\n                type: _type__WEBPACK_IMPORTED_MODULE_0__.ArticleContentType.image,\n                src: \"/product/product-c31.jpg\"\n            }\n        ],\n        spec: [\n            {\n                type: \"尺寸与重量\",\n                detail: \"高度：约132.6mm；宽度：73mm；厚度：16mm；重量：275g\"\n            },\n            {\n                type: \"存储\",\n                detail: \"8G+128GB；8G+256GB；12G+128GB；12G+256GB\"\n            },\n            {\n                type: \"显示\",\n                detail: \"17.02CM/6.70英寸\"\n            },\n            {\n                type: \"摄像头\",\n                detail: \"前置：3200万像素，f/2.4光圈，FOV81，5P镜头；后置：5000万像素，广角与超广角摄像；\"\n            },\n            {\n                type: \"处理平台\",\n                detail: \"高通骁龙888；2.842GHz；8核\"\n            },\n            {\n                type: \"电池\",\n                detail: \"4500mAh；支持快充，最大功率65W；支持无线充电\"\n            },\n            {\n                type: \"生物识别\",\n                detail: \"支持指纹识别；支持面部解锁\"\n            },\n            {\n                type: \"数据功能\",\n                detail: \"双卡双待；2G3G4G5G；蓝牙5.2；USB3.1；全功能NFC；\"\n            }\n        ],\n        videos: [\n            {\n                videoId: \"01\"\n            }\n        ],\n        helps: [\n            {\n                helpId: \"01\"\n            }\n        ]\n    }\n];\nconst productGroups = [\n    {\n        id: \"01\",\n        name: \"摄像头产品\",\n        nameEn: \"Camera\"\n    },\n    {\n        id: \"02\",\n        name: \"翻译机\",\n        nameEn: \"Translator\"\n    }\n];\nasync function getProductDatas() {\n    return new Promise((resolve)=>{\n        resolve(productDatas);\n    });\n}\nasync function getProductData(id) {\n    return new Promise((resolve)=>{\n        const productData = productDatas.find((product)=>product.id === id);\n        if (productData) {\n            resolve(productData);\n        } else {\n            resolve(false);\n        }\n    });\n}\nasync function getGroupDatas() {\n    return new Promise((resolve)=>{\n        resolve(productGroups);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./data/products.ts\n");

/***/ }),

/***/ "(rsc)/./data/type.ts":
/*!**********************!*\
  !*** ./data/type.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArticleContentType: () => (/* binding */ ArticleContentType),\n/* harmony export */   ArticleType: () => (/* binding */ ArticleType),\n/* harmony export */   NewsCategory: () => (/* binding */ NewsCategory)\n/* harmony export */ });\n/* 文章相关 */ var ArticleContentType;\n(function(ArticleContentType) {\n    ArticleContentType[ArticleContentType[\"paragraph\"] = 0] = \"paragraph\";\n    ArticleContentType[ArticleContentType[\"title\"] = 1] = \"title\";\n    ArticleContentType[ArticleContentType[\"image\"] = 2] = \"image\";\n})(ArticleContentType || (ArticleContentType = {}));\nvar ArticleType;\n(function(ArticleType) {\n    ArticleType[\"news\"] = \"/news\";\n    ArticleType[\"support\"] = \"/support\";\n    ArticleType[\"product\"] = \"/product\";\n})(ArticleType || (ArticleType = {}));\nvar NewsCategory;\n(function(NewsCategory) {\n    NewsCategory[NewsCategory[\"cylan\"] = 0] = \"cylan\";\n    NewsCategory[NewsCategory[\"industry\"] = 1] = \"industry\";\n})(NewsCategory || (NewsCategory = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./data/type.ts\n");

/***/ }),

/***/ "(rsc)/./locales/server.ts":
/*!***************************!*\
  !*** ./locales/server.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCurrentLocale: () => (/* binding */ getCurrentLocale),\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   getScopedI18n: () => (/* binding */ getScopedI18n),\n/* harmony export */   getStaticParams: () => (/* binding */ getStaticParams)\n/* harmony export */ });\n/* harmony import */ var next_international_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-international/server */ \"(rsc)/./node_modules/next-international/dist/app/server/index.js\");\n/* harmony import */ var next_international_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_international_server__WEBPACK_IMPORTED_MODULE_0__);\n\nconst { getI18n, getScopedI18n, getStaticParams, getCurrentLocale } = (0,next_international_server__WEBPACK_IMPORTED_MODULE_0__.createI18nServer)({\n    en: ()=>__webpack_require__.e(/*! import() */ \"_rsc_locales_en_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./en */ \"(rsc)/./locales/en.ts\")),\n    zh: ()=>__webpack_require__.e(/*! import() */ \"_rsc_locales_zh_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./zh */ \"(rsc)/./locales/zh.ts\"))\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9sb2NhbGVzL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFdEQsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLGFBQWEsRUFBRUMsZUFBZSxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHSiwyRUFBZ0JBLENBQUM7SUFDNUZLLElBQUksSUFBTSxvSkFBYztJQUN4QkMsSUFBSSxJQUFNLG9KQUFjO0FBQzFCLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbWNhbV9vZmZpY2lhbF9zaXRlLy4vbG9jYWxlcy9zZXJ2ZXIudHM/MmZlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVJMThuU2VydmVyIH0gZnJvbSBcIm5leHQtaW50ZXJuYXRpb25hbC9zZXJ2ZXJcIjtcclxuXHJcbmV4cG9ydCBjb25zdCB7IGdldEkxOG4sIGdldFNjb3BlZEkxOG4sIGdldFN0YXRpY1BhcmFtcywgZ2V0Q3VycmVudExvY2FsZSB9ID0gY3JlYXRlSTE4blNlcnZlcih7XHJcbiAgZW46ICgpID0+IGltcG9ydChcIi4vZW5cIiksXHJcbiAgemg6ICgpID0+IGltcG9ydChcIi4vemhcIiksXHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlSTE4blNlcnZlciIsImdldEkxOG4iLCJnZXRTY29wZWRJMThuIiwiZ2V0U3RhdGljUGFyYW1zIiwiZ2V0Q3VycmVudExvY2FsZSIsImVuIiwiemgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./locales/server.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"32x32\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2ltY2FtX29mZmljaWFsX3NpdGUvLi9hcHAvZmF2aWNvbi5pY28/ZTEwMyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIzMngzMlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-international"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&page=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&appPaths=%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Fproduct%2F(overview)%2Fpage.tsx&appDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5C---AProjects---%5Cimcam-officialsite%5Cfastoffical&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();