import './components.scss'
import Image from 'next/image'
import Link from 'next/link'
import { getI18n } from '@/locales/server'

export type BreadLink = {
  link: string
  name: string
}

export default async function BreadCrumbs({
  navs,
}: {
  navs: Array<BreadLink>
}) {
  const t = await getI18n()

  return (
    <div className="bread-crumbs">
      <Image
        src={'/position.svg'}
        width={10}
        height={13}
        alt="position"
      ></Image>
      <Link className="bread-crumbs__item" href={'/'}>
        {t('home')}
      </Link>
      {navs.map((item, index) => (
        <>
          <Image
            src={'/arrow-right.svg'}
            width={11}
            height={17}
            alt=">"
          ></Image>
          <Link key={index} className="bread-crumbs__item" href={item.link}>
            {item.name}
          </Link>
        </>
      ))}
      <Image src={'/arrow-right.svg'} width={11} height={17} alt=">"></Image>
      <span className="bread-crumbs__item--current">{t('detail')}</span>
    </div>
  )
}
