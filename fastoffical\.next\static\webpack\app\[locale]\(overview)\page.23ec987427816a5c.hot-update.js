"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(overview)/page",{

/***/ "(app-pages-browser)/./app/[locale]/ui/home/<USER>":
/*!************************************************!*\
  !*** ./app/[locale]/ui/home/<USER>
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BannerSlider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./home.module.scss */ \"(app-pages-browser)/./app/[locale]/ui/home/<USER>");\n/* harmony import */ var _home_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_home_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _locales_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/locales/client */ \"(app-pages-browser)/./locales/client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction BannerSlider() {\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPaused, setIsPaused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const locale = (0,_locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale)();\n    const images = [\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>",\n        locale === \"zh\" ? \"/home/<USER>" : \"/home/<USER>"\n    ];\n    // 自动播放功能\n    const startAutoPlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (images.length <= 1) return;\n        autoPlayRef.current = setInterval(()=>{\n            if (!isPaused) {\n                setCurrentIndex((prev)=>(prev + 1) % images.length);\n            }\n        }, 5000) // 5秒切换一次\n        ;\n    }, [\n        images.length,\n        isPaused\n    ]);\n    const stopAutoPlay = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (autoPlayRef.current) {\n            clearInterval(autoPlayRef.current);\n            autoPlayRef.current = null;\n        }\n    }, []);\n    // 切换到指定索引\n    const goToSlide = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((index)=>{\n        if (index === currentIndex || isTransitioning) return;\n        setCurrentIndex(index);\n    }, [\n        currentIndex,\n        isTransitioning\n    ]);\n    // 上一张\n    const goToPrev = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isTransitioning) return;\n        const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;\n        goToSlide(newIndex);\n    }, [\n        currentIndex,\n        images.length,\n        isTransitioning,\n        goToSlide\n    ]);\n    // 下一张\n    const goToNext = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (isTransitioning) return;\n        const newIndex = (currentIndex + 1) % images.length;\n        goToSlide(newIndex);\n    }, [\n        currentIndex,\n        images.length,\n        isTransitioning,\n        goToSlide\n    ]);\n    // 触摸事件处理\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleTouchStart = (e)=>{\n        setTouchStart({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY\n        });\n        setIsPaused(true) // 暂停自动播放\n        ;\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd({\n            x: e.targetTouches[0].clientX,\n            y: e.targetTouches[0].clientY\n        });\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const deltaX = touchEnd.x - touchStart.x;\n        const deltaY = touchEnd.y - touchStart.y;\n        const minSwipeDistance = 50;\n        // 判断是否为水平滑动\n        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {\n            if (deltaX > 0) {\n                goToPrev();\n            } else {\n                goToNext();\n            }\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n        setIsPaused(false) // 恢复自动播放\n        ;\n    };\n    // 鼠标事件处理\n    const handleMouseEnter = ()=>setIsPaused(true);\n    const handleMouseLeave = ()=>setIsPaused(false);\n    // 生命周期管理\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        startAutoPlay();\n        return ()=>stopAutoPlay();\n    }, [\n        startAutoPlay,\n        stopAutoPlay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isPaused) {\n            stopAutoPlay();\n        } else {\n            startAutoPlay();\n        }\n    }, [\n        isPaused,\n        startAutoPlay,\n        stopAutoPlay\n    ]);\n    // 过渡动画控制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsTransitioning(true);\n        const timer = setTimeout(()=>setIsTransitioning(false), 300);\n        return ()=>clearTimeout(timer);\n    }, [\n        currentIndex\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider\"]),\n        onMouseEnter: handleMouseEnter,\n        onMouseLeave: handleMouseLeave,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: containerRef,\n                className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__list\"]),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                style: {\n                    transform: \"translateX(-\".concat(currentIndex * 100, \"%)\"),\n                    transition: isTransitioning ? \"transform 0.3s ease-in-out\" : \"none\"\n                },\n                children: images.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__slide\"]),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: item,\n                            width: 1920,\n                            height: 500,\n                            alt: \"Banner \".concat(index + 1),\n                            unoptimized: true,\n                            style: {\n                                width: \"100%\",\n                                height: \"100%\",\n                                objectFit: \"cover\",\n                                objectPosition: \"center\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, index, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switcher, {\n                        position: \"left\",\n                        disabled: currentIndex === 0,\n                        onClick: goToPrev\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Switcher, {\n                        position: \"right\",\n                        disabled: currentIndex === images.length - 1,\n                        onClick: goToNext\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Indicator, {\n                        count: images.length,\n                        currentIndex: currentIndex,\n                        onIndicatorClick: goToSlide\n                    }, void 0, false, {\n                        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(BannerSlider, \"42xz7GIKgMnLmVHtnJ3qRJYf70o=\", false, function() {\n    return [\n        _locales_client__WEBPACK_IMPORTED_MODULE_3__.useCurrentLocale\n    ];\n});\n_c = BannerSlider;\nfunction Switcher(param) {\n    let { position = \"left\", disabled = false, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: onClick,\n        disabled: disabled,\n        className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default())[\"banner-slider__switcher\".concat(position === \"left\" ? \"\" : \"--right\")], \" \").concat(disabled ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__switcher--disabled\"]) : \"\"),\n        \"aria-label\": \"\".concat(position === \"left\" ? \"Previous\" : \"Next\", \" slide\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            src: \"/slider-\".concat(position === \"left\" ? \"left\" : \"right\", \".svg\"),\n            width: 44,\n            height: 44,\n            alt: \"\"\n        }, void 0, false, {\n            fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Switcher;\nfunction Indicator(param) {\n    let { count, currentIndex, onIndicatorClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator\"]),\n        children: Array(count).fill(\"\").map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onIndicatorClick(index),\n                className: \"\".concat((_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item\"]), \" \").concat(index === currentIndex ? (_home_module_scss__WEBPACK_IMPORTED_MODULE_4___default()[\"banner-slider__indicator__item--active\"]) : \"\"),\n                \"aria-label\": \"Go to slide \".concat(index + 1)\n            }, index, false, {\n                fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n                lineNumber: 228,\n                columnNumber: 11\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\---AProjects---\\\\imcam-officialsite\\\\fastoffical\\\\app\\\\[locale]\\\\ui\\\\home\\\\banner-slider.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_c2 = Indicator;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"BannerSlider\");\n$RefreshReg$(_c1, \"Switcher\");\n$RefreshReg$(_c2, \"Indicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/ui/home/<USER>"));

/***/ })

});