.flex-box-with-4items {
  display: flex;
  gap: 25.5px;
  flex-wrap: wrap;
  &__card {
    border-radius: 6px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 300px;
    position: relative;
    &__image {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      width: 100%;
      border-radius: 6px;
      overflow: hidden;
      position: relative;
      &__play {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 1;
      }
    }
    &__info {
      padding: 20px 16px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 6px;
      width: 100%;
      div {
        font-size: var(--font-large);
      }
      span {
        color: var(--text-mark);
      }
      &--product {
        text-align: center;
        padding: 10px 16px 20px;
        span {
          color: var(--text-description);
        }
      }
      &--pride {
        gap: 0;
      }
    }
    &--video:hover,
    &--link:hover {
      box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.1);
      .flex-box-with-4items__card__info {
        > div {
          color: var(--color-theme);
        }
      }
    }
  }
  &--detail {
    .flex-box-with-4items__card {
      width: 290px;
    }
  }
}

.flex-box-with-2items {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  &__item {
    width: 625px;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    &:hover {
      cursor: pointer;
    }
    > div:first-of-type {
      border-radius: 4px;
      overflow: hidden;
      width: 240px;
      height: 160px;
    }
    > div:last-of-type {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding: 20px;
      h6 {
        font-size: var(--font-large);
        font-weight: var(--font-bold);
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      p {
        color: var(--text-description);
        margin-top: 6px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      span {
        color: var(--text-mark);
        margin-top: 8px;
      }
    }
  }
  &--product-detail {
    gap: 0;
    column-gap: 20px;
    row-gap: 30px;
    .flex-box-with-2items__item {
      width: 600px;
    }
  }
}

.dropdown-window {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translate(-50%, 100%);
  z-index: 5;
  &__above {
    display: flex;
    justify-content: center;
  }
  &__list {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    padding: 10px 1.5px;
    &__item {
      min-width: 122px;
      text-align: center;
      height: 40px;
      line-height: 40px;
      color: var(--text-description);
      &:hover {
        background-color: #f6f6f6;
        color: var(--color-theme);
        cursor: pointer;
      }
      &__link {
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 40px;
      }
    }
  }
  &__placeholder {
    position: absolute;
    width: 100%;
    height: 24px;
    top: -24px;
    left: 0;
  }
  &__mask {
    position: fixed;
    width: 100vw;
    height: 200vh;
    top: 0;
    left: 0;
    z-index: 4;
  }
}

.bread-crumbs {
  display: flex;
  gap: 5.5px;
  align-items: center;
  &__item {
    color: var(--text-description);
    font-size: var(--font-small);
  }
  &__item--current {
    composes: bread-crumbs__item;
    color: var(--text-mark);
  }
}

.banner {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;
  width: calc(100vw - 10px);
  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 保持图像原始尺寸 */
    object-position: center; /* 将图像的中心点显示在容器的中心 */
  }
}

.pagination {
  display: flex;
  gap: 10px;
  justify-content: center;
  &__page,
  &__pagejumper {
    padding: 0 14px;
    height: 32px;
    color: var(--text-description);
    border-radius: 4px;
    background-color: #fff;
    &--active {
      color: #fff;
      background-color: var(--color-theme);
    }
    &:disabled {
      opacity: 0.6;
    }
  }
}

.page-tabs {
  height: 220px;
  position: relative;
  background-color: rgb(179, 220, 252);
  &__content {
    width: var(--width-content);
    margin: auto;
    height: 100%;
    position: relative;
    &__title {
      display: flex;
      position: absolute;
      top: 31px;
      left: 20px;
      gap: 8px;
      h1 {
        color: #fff;
      }
    }
    &__items {
      display: flex;
      gap: 14px;
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      justify-content: center;
      transform: translateY(-50%);
      &__item {
        height: 42px;
        padding: 0 30px;
        border-radius: 4px;
        font-size: var(--font-medium);
        color: var(--text-description);
        line-height: 42px;
        background-color: #fff;
        &--active {
          background-color: var(--color-theme);
          color: #fff;
        }
      }
    }
  }
  &__search {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -59%);
    width: 680px;
    height: 44px;
    display: flex;
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffffcc;
    input {
      border: none;
      flex: 1;
      padding: 0 20px;
      color: var(--text-dark);
      &::placeholder {
        color: var(--text-mark);
      }
    }
    button {
      padding: 0 20px;
      height: 44px;
      line-height: 44px;
      border-radius: 6px;
      color: #fff;
      background-color: var(--color-theme);
      font-size: var(--font-medium);
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }
}

.nav-list {
  padding: 20px;
  background-color: #fff;
  width: 100vw;
  &__item {
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--gray-4);
    height: 50px;
    line-height: 50px;
    &:hover {
      cursor: pointer;
    }
    &:last-of-type {
      border-bottom: none;
    }
    &__icon {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &__title {
      font-weight: var(--font-bolder);
      margin-left: 10px;
    }
    &__button {
      width: 32px;
      height: 32px;
    }
    &--sub {
      .nav-list__item__title {
        font-weight: normal;
      }
    }
  }
  &--footer {
    background-color: #282c30;
    .nav-list__item {
      border-bottom: none;
      box-shadow: 0px 1px 0px 0px #3a3e42;
      &__title {
        color: var(--gray-4);
      }
      &--sub {
        .nav-list__item__title {
          color: var(--gray-2);
          padding-left: 20px;
        }
      }
      &__icon {
        display: none;
      }
    }
  }
}

.back-top {
  position: fixed;
  bottom: 267px;
  right: 20px;
  z-index: 5;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 6px;
  span {
    font-size: var(--font-small);
    color: var(--text-description);
  }
  &--hide {
    display: none;
  }
}

.show-more {
  width: 100%;
  height: 49px;
  text-align: center;
  line-height: 49px;
  color: var(--text-description);
  background-color: #fff;
  margin-top: 15px;
  border-radius: 6px;
}

.cylan-certificates {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  overflow-x: scroll;
  margin-top: 20px;
  padding: 0 16px;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  overflow: -moz-scrollbars-none; /* Firefox */
  ::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
  > div {
    display: inline-block;
    margin-right: 10px;
    > div :first-of-type {
      box-shadow: 0px 6px 20px 0px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      overflow: hidden;
    }
    > div:last-of-type {
      margin-top: 15px;
      text-align: center;
      font-size: var(--font-medium);
      color: var(--text-description);
      white-space: wrap;
      display: -webkit-box;
      -webkit-line-clamp: 2; /* 最多显示 3 行 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 40px;
    }
  }
}

@media (min-width: 451px) and (max-width: 1280px) {
  .flex-box-with-4items {
    gap: 20px;
    &--product {
      justify-content: space-between;
      gap: 0;
      row-gap: 23px;
      column-gap: 0;
    }
    &__card {
      width: calc(25% - 15px);
      &__info {
        div {
          font-size: var(--font-normal);
        }
        &--pride {
          div {
            font-size: var(--font-medium);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      &--product {
        width: calc(50% - 10px);
      }
    }
    &--detail {
      .flex-box-with-4items__card {
        width: calc(25% - 15px);
      }
    }
  }
  .flex-box-with-2items {
    flex-direction: column;
    flex-wrap: nowrap;
    gap: 20px;
    &__item {
      width: 100%;
      height: 160px;
      > div:first-of-type {
        width: 240px;
        height: 100%;
      }
      > div:last-of-type {
        flex: 1;
        height: 100%;
        padding: 20px;
        display: flex;
        flex-direction: column;
      }
    }
    &--product-detail {
      .flex-box-with-2items__item {
        width: 100%;
      }
    }
  }
  .banner {
    width: calc(100vw - 10px);
  }
  .page-tabs {
    height: 176px;
    &__search {
      top: auto;
      bottom: 46px;
      transform: translate(-50%);
    }
  }
}

@media (max-width: 450px) {
  .flex-box-with-4items {
    gap: 15px;
    &__card {
      width: calc(100%);
      &__info {
        &--product {
          padding: 0 20px 10px;
        }
      }
    }
    &--detail {
      .flex-box-with-4items__card {
        width: calc(100%);
      }
    }
  }
  .flex-box-with-2items {
    flex-direction: column;
    gap: 15px;
    &__item {
      width: 100%;
      height: 93px;
      > div:first-of-type {
        width: 140px;
        height: 100%;
      }
      > div:last-of-type {
        flex: 1;
        height: 100%;
        padding: 7px 8px;
        flex-direction: column;
        gap: 0;
        justify-content: space-between;
      }
    }
    &--product-detail {
      .flex-box-with-2items__item {
        width: 100%;
      }
    }
  }
  .back-top {
    bottom: 50px;
  }
  .banner {
    width: 100vw;
  }
  .page-tabs {
    height: 120px;
    &__search {
      top: auto;
      bottom: 46px;
      transform: translate(-50%);
    }
    &__content {
      &__title {
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    &__tabs-small {
      background-color: #fff;
      display: flex;
      padding: 0 16px;
      gap: 10px;
      width: 100vw;
      overflow-x: scroll;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
      overflow: -moz-scrollbars-none; /* Firefox */
      ::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
      &__tab {
        height: 48px;
        text-align: center;
        line-height: 48px;
        padding: 0 10px;
        flex-shrink: 0;
        color: var(--text-description);
        font-size: var(--font-medium);
        &--active {
          color: var(--color-theme);
          position: relative;
          &::after {
            content: " ";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 26px;
            height: 3px;
            border-radius: 2px;
            background-color: var(--color-theme);
          }
        }
      }
    }
  }
}
