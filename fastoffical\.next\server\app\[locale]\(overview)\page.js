(()=>{var e={};e.id=545,e.ids=[545],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},5296:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=i(482),r=i(9108),a=i(2563),n=i.n(a),l=i(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);i.d(t,o);let d=["",{children:["[locale]",{children:["(overview)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,9845)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\(overview)\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\(overview)\\page.tsx"],p="/[locale]/(overview)/page",u={require:i,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/(overview)/page",pathname:"/[locale]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7021:(e,t,i)=>{Promise.resolve().then(i.bind(i,3898)),Promise.resolve().then(i.bind(i,4897)),Promise.resolve().then(i.t.bind(i,1900,23)),Promise.resolve().then(i.t.bind(i,1476,23))},6112:(e,t,i)=>{"use strict";i.d(t,{Z:()=>n});var s=i(5344),r=i(9410),a=i(2716);function n(){return s.jsx(a.e4,{locale:(0,a.eV)(),children:s.jsx(l,{})})}function l(){let e=(0,a.QT)(),t=[{imageSrc:"/pride-image-1.jpg",title:e("pride1"),imageWidth:120},{imageSrc:"/pride-image-2.jpg",title:e("pride2"),imageWidth:242},{imageSrc:"/pride-image-3.jpg",title:e("pride3"),imageWidth:120},{imageSrc:"/pride-image-4.jpg",title:e("pride4"),imageWidth:120},{imageSrc:"/pride-image-5.jpg",title:e("pride5"),imageWidth:240},{imageSrc:"/pride-image-6.jpg",title:e("pride6"),imageWidth:240},{imageSrc:"/pride-image-7.jpg",title:e("pride7"),imageWidth:240},{imageSrc:"/pride-image-8.jpg",title:e("pride8"),imageWidth:240},{imageSrc:"/pride-image-9.jpg",title:e("pride9"),imageWidth:240}],i=({imageSrc:e,title:t,imageWidth:i})=>(0,s.jsxs)("div",{style:{width:i},children:[s.jsx("div",{children:s.jsx(r.default,{src:e,height:160,width:i,alt:"",unoptimized:!0})}),s.jsx("div",{children:t})]});return s.jsx("div",{className:"cylan-certificates hide-on-medium hide-on-large",children:t.map((e,t)=>s.jsx(i,{...e},t))})}},7572:(e,t,i)=>{"use strict";i.d(t,{H:()=>s,Z:()=>d});var s,r=i(5344),a=i(9410),n=i(3729),l=i.n(n);i(4507);var o=i(6506);function d({infos:e,imageSize:t,imageBox:i,mode:s="",gap:n=0,isDetail:d=!1}){let c=({imageSrc:e,title:n,tip:l="",link:d="",videoSrc:c=""})=>{let p=!!d,u=!!c,h=`flex-box-with-4items__card ${"product"===s?"flex-box-with-4items__card--product":""} ${p?"flex-box-with-4items__card--link":""} ${u?"flex-box-with-4items__card--video":""}`,m=`${""===s?"":`flex-box-with-4items__card__info--${s}`} flex-box-with-4items__card__info`,f=(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"flex-box-with-4items__card__image",style:{aspectRatio:i.width/i.height},children:u?(0,r.jsxs)("video",{width:t.width,height:t.height,controls:!0,poster:e,children:[r.jsx("source",{src:c,type:"video/mp4"}),"Your browser does not support the video tag."]}):r.jsx(a.default,{unoptimized:!0,src:e,width:t.width,height:t.height,alt:"image",style:{objectFit:"fill",width:"100%",height:"auto"}})}),(0,r.jsxs)("div",{className:m,children:[r.jsx("div",{children:n}),"pride"===s?"":l?r.jsx("span",{children:l}):""]})]});return p?r.jsx(o.default,{href:d,className:h,children:f}):r.jsx("div",{className:h,children:f})};return r.jsx("div",{className:`flex-box-with-4items ${"product"===s?"flex-box-with-4items--product":""} ${d?"flex-box-with-4items--detail":""}`,style:n?{gap:n}:{},children:e.map((e,t)=>r.jsx(l().Fragment,{children:c(e)},`${e.link}-${t}`))})}!function(e){e.normal="",e.product="product",e.pride="pride"}(s||(s={}))},3898:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CompanyTime:()=>h,Contacts:()=>f,default:()=>p});var s=i(5344),r=i(5620),a=i.n(r),n=i(6506),l=i(9410),o=i(7572),d=i(6112),c=i(2716);function p(){return s.jsx(c.e4,{locale:(0,c.eV)(),children:s.jsx(u,{})})}function u(){let e=(0,c.QT)();return(0,s.jsxs)("div",{className:a().about,children:[s.jsx("div",{className:a().about__cover}),(0,s.jsxs)("div",{className:a().about__content,children:[s.jsx("h3",{children:e("aboutUs")}),s.jsx(h,{}),s.jsx(m,{}),s.jsx(f,{})]})]})}function h({isAboutPage:e=!1}){let t=(0,c.QT)(),i=(0,c.eV)(),r=({num:e,unit:t,text:i})=>(0,s.jsxs)("div",{className:a().about__content__time__item,children:[(0,s.jsxs)("div",{children:[s.jsx("span",{children:e}),s.jsx("span",{children:t})]}),s.jsx("div",{children:i})]}),n=()=>s.jsx("div",{style:{height:46,width:0,opacity:.1,border:`1px solid ${e?"var(--gray)":"#fff"}`}});return(0,s.jsxs)("div",{className:`${a().about__content__time} ${e?a()["about__content__time--page"]:""}`,children:[s.jsx(r,{num:2005,unit:t("year"),text:t("companiyTime")}),s.jsx(n,{}),s.jsx(r,{num:"zh"===i?45:450,unit:"zh"===i?"万+":"K+",text:t("deviceSells")}),s.jsx(n,{}),s.jsx(r,{num:"zh"===i?90:900,unit:"zh"===i?"万+":"K+",text:t("activeUsers")})]})}function m(){let e=(0,c.QT)(),t={infos:[{imageSrc:"/pride-image-6.jpg",title:e("pride6")},{imageSrc:"/pride-image-9.jpg",title:e("pride9")},{imageSrc:"/pride-image-5.jpg",title:e("pride5")},{imageSrc:"/pride-image-2.jpg",title:e("pride2")}],imageSize:{width:300,height:200},imageBox:{width:300,height:200},mode:o.H.pride};return(0,s.jsxs)("div",{className:a().about__prides,children:[s.jsx("div",{className:"hide-on-small",style:{marginTop:30},children:s.jsx(o.Z,{...t})}),s.jsx(d.Z,{})]})}function f({isAboutPage:e=!1}){let t=(0,c.QT)(),i=({iconUrl:e,text:t})=>(0,s.jsxs)("div",{className:a().contacts__info__item,children:[s.jsx(l.default,{src:e,width:18,height:20,alt:"icon"}),s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:4},children:t.split("/n").map((e,t)=>s.jsx("div",{children:e},t))})]});return(0,s.jsxs)("div",{className:`${a().contacts} ${e?a()["contacts--page"]:""}`,children:[(0,s.jsxs)("div",{className:a().contacts__info,children:[s.jsx("div",{className:a().contacts__info__title,children:t("contactUs")}),(0,s.jsxs)("div",{className:a().contacts__info__items,children:[s.jsx(i,{iconUrl:"/contact-position.svg",text:t("cylanAddress")}),s.jsx(i,{iconUrl:"/contact-email.svg",text:"<EMAIL>"}),s.jsx(i,{iconUrl:"/contact-phone.svg",text:"+86-0755-83073491"})]})]}),s.jsx("div",{className:a().contacts__address,children:s.jsx(n.default,{href:"https://map.baidu.com/poi/%E5%90%88%E6%88%90%E5%8F%B7%E6%B7%B1%E5%9C%B3%E6%B0%91%E4%BF%97%E6%96%87%E5%8C%96%E4%BA%A7%E4%B8%9A%E5%9B%AD/@12682764.738888016,2566016.5718568345,14z?uid=eb63e5cd850d1ef4a3acc4a1&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo",children:s.jsx(l.default,{src:"/address-image.webp",width:625,height:249,alt:"address"})})})]})}},4897:(e,t,i)=>{"use strict";let s,r,a;i.r(t),i.d(t,{default:()=>eE});var n=i(5344),l=i(3729);function o(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function d(e,t){void 0===e&&(e={}),void 0===t&&(t={});let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:o(t[i])&&o(e[i])&&Object.keys(t[i]).length>0&&d(e[i],t[i])})}let c={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function p(){let e="undefined"!=typeof document?document:{};return d(e,c),e}let u={document:c,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function h(){let e="undefined"!=typeof window?window:{};return d(e,u),e}function m(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function f(){return Date.now()}function g(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function v(){let e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){let s=i<0||arguments.length<=i?void 0:arguments[i];if(null!=s&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(s instanceof HTMLElement):!s||1!==s.nodeType&&11!==s.nodeType)){let i=Object.keys(Object(s)).filter(e=>0>t.indexOf(e));for(let t=0,r=i.length;t<r;t+=1){let r=i[t],a=Object.getOwnPropertyDescriptor(s,r);void 0!==a&&a.enumerable&&(g(e[r])&&g(s[r])?s[r].__swiper__?e[r]=s[r]:v(e[r],s[r]):!g(e[r])&&g(s[r])?(e[r]={},s[r].__swiper__?e[r]=s[r]:v(e[r],s[r])):e[r]=s[r])}}}return e}function _(e,t,i){e.style.setProperty(t,i)}function w(e){let t,{swiper:i,targetPosition:s,side:r}=e,a=h(),n=-i.translate,l=null,o=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);let d=s>n?"next":"prev",c=(e,t)=>"next"===d&&e>=t||"prev"===d&&e<=t,p=()=>{t=new Date().getTime(),null===l&&(l=t);let e=n+(.5-Math.cos(Math.max(Math.min((t-l)/o,1),0)*Math.PI)/2)*(s-n);if(c(e,s)&&(e=s),i.wrapperEl.scrollTo({[r]:e}),c(e,s)){i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout(()=>{i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo({[r]:e})}),a.cancelAnimationFrame(i.cssModeFrameID);return}i.cssModeFrameID=a.requestAnimationFrame(p)};p()}function b(e,t){void 0===t&&(t="");let i=h(),s=[...e.children];return(i.HTMLSlotElement&&e instanceof HTMLSlotElement&&s.push(...e.assignedElements()),t)?s.filter(e=>e.matches(t)):s}function y(e){try{console.warn(e);return}catch(e){}}function x(e,t){var i;void 0===t&&(t=[]);let s=document.createElement(e);return s.classList.add(...Array.isArray(t)?t:(void 0===(i=t)&&(i=""),i.trim().split(" ").filter(e=>!!e.trim()))),s}function S(e,t){return h().getComputedStyle(e,null).getPropertyValue(t)}function E(e){let t,i=e;if(i){for(t=0;null!==(i=i.previousSibling);)1===i.nodeType&&(t+=1);return t}}function T(e,t){let i=[],s=e.parentElement;for(;s;)t?s.matches(t)&&i.push(s):i.push(s),s=s.parentElement;return i}function C(e,t,i){let s=h();return i?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function P(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function M(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}function k(){return s||(s=function(){let e=h(),t=p();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),s}function j(e){return void 0===e&&(e={}),r||(r=function(e){let{userAgent:t}=void 0===e?{}:e,i=k(),s=h(),r=s.navigator.platform,a=t||s.navigator.userAgent,n={ios:!1,android:!1},l=s.screen.width,o=s.screen.height,d=a.match(/(Android);?[\s\/]+([\d.]+)?/),c=a.match(/(iPad).*OS\s([\d_]+)/),p=a.match(/(iPod)(.*OS\s([\d_]+))?/),u=!c&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="MacIntel"===r;return!c&&m&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${o}`)>=0&&((c=a.match(/(Version)\/([\d.]+)/))||(c=[0,1,"13_0_0"]),m=!1),d&&"Win32"!==r&&(n.os="android",n.android=!0),(c||u||p)&&(n.os="ios",n.ios=!0),n}(e)),r}function L(){return a||(a=function(){let e=h(),t=j(),i=!1;function s(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(s()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,s]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));i=e<16||16===e&&s<2}}let r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),a=s(),n=a||r&&t.ios;return{isSafari:i||a,needPerspectiveFix:i,need3dFix:n,isWebView:r}}()),a}let O=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},A=(e,t,i)=>{t&&!e.classList.contains(i)?e.classList.add(i):!t&&e.classList.contains(i)&&e.classList.remove(i)},I=(e,t)=>{if(!e||e.destroyed||!e.params)return;let i=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(i){let t=i.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(i.shadowRoot?t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(t=i.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},D=(e,t)=>{if(!e.slides[t])return;let i=e.slides[t].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},z=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,i=e.slides.length;if(!i||!t||t<0)return;t=Math.min(t,i);let s="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let i=[r-t];i.push(...Array.from({length:t}).map((e,t)=>r+s+t)),e.slides.forEach((t,s)=>{i.includes(t.column)&&D(e,s)});return}let a=r+s-1;if(e.params.rewind||e.params.loop)for(let s=r-t;s<=a+t;s+=1){let t=(s%i+i)%i;(t<r||t>a)&&D(e,t)}else for(let s=Math.max(r-t,0);s<=Math.min(a+t,i-1);s+=1)s!==r&&(s>a||s<r)&&D(e,s)};function $(e){let{swiper:t,runCallbacks:i,direction:s,step:r}=e,{activeIndex:a,previousIndex:n}=t,l=s;l||(l=a>n?"next":a<n?"prev":"reset"),t.emit(`transition${r}`),i&&"reset"===l?t.emit(`slideResetTransition${r}`):i&&a!==n&&(t.emit(`slideChangeTransition${r}`),"next"===l?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}function N(e,t,i){let s=h(),{params:r}=e,a=r.edgeSwipeDetection,n=r.edgeSwipeThreshold;return!a||!(i<=n)&&!(i>=s.innerWidth-n)||"prevent"===a&&(t.preventDefault(),!0)}function B(e){let t=p(),i=e;i.originalEvent&&(i=i.originalEvent);let s=this.touchEventsData;if("pointerdown"===i.type){if(null!==s.pointerId&&s.pointerId!==i.pointerId)return;s.pointerId=i.pointerId}else"touchstart"===i.type&&1===i.targetTouches.length&&(s.touchId=i.targetTouches[0].identifier);if("touchstart"===i.type){N(this,i,i.targetTouches[0].pageX);return}let{params:r,touches:a,enabled:n}=this;if(!n||!r.simulateTouch&&"mouse"===i.pointerType||this.animating&&r.preventInteractionOnTransition)return;!this.animating&&r.cssMode&&r.loop&&this.loopFix();let l=i.target;if("wrapper"===r.touchEventsTarget&&!function(e,t){let i=h(),s=t.contains(e);return!s&&i.HTMLSlotElement&&t instanceof HTMLSlotElement&&!(s=[...t.assignedElements()].includes(e))&&(s=function(e,t){let i=[t];for(;i.length>0;){let t=i.shift();if(e===t)return!0;i.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t)),s}(l,this.wrapperEl)||"which"in i&&3===i.which||"button"in i&&i.button>0||s.isTouched&&s.isMoved)return;let o=!!r.noSwipingClass&&""!==r.noSwipingClass,d=i.composedPath?i.composedPath():i.path;o&&i.target&&i.target.shadowRoot&&d&&(l=d[0]);let c=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,u=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(u?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===p()||i===h())return null;i.assignedSlot&&(i=i.assignedSlot);let s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(c,l):l.closest(c))){this.allowClick=!0;return}if(r.swipeHandler&&!l.closest(r.swipeHandler))return;a.currentX=i.pageX,a.currentY=i.pageY;let m=a.currentX,g=a.currentY;if(!N(this,i,m))return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=m,a.startY=g,s.touchStartTime=f(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,r.threshold>0&&(s.allowThresholdMove=!1);let v=!0;l.matches(s.focusableElements)&&(v=!1,"SELECT"===l.nodeName&&(s.isTouched=!1)),t.activeElement&&t.activeElement.matches(s.focusableElements)&&t.activeElement!==l&&("mouse"===i.pointerType||"mouse"!==i.pointerType&&!l.matches(s.focusableElements))&&t.activeElement.blur();let _=v&&this.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||_)&&!l.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&this.freeMode&&this.animating&&!r.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",i)}function G(e){let t,i;let s=p(),r=this.touchEventsData,{params:a,touches:n,rtlTranslate:l,enabled:o}=this;if(!o||!a.simulateTouch&&"mouse"===e.pointerType)return;let d=e;if(d.originalEvent&&(d=d.originalEvent),"pointermove"===d.type&&(null!==r.touchId||d.pointerId!==r.pointerId))return;if("touchmove"===d.type){if(!(t=[...d.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else t=d;if(!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",d);return}let c=t.pageX,u=t.pageY;if(d.preventedByNestedSwiper){n.startX=c,n.startY=u;return}if(!this.allowTouchMove){d.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u}),r.touchStartTime=f());return}if(a.touchReleaseOnEdges&&!a.loop){if(this.isVertical()){if(u<n.startY&&this.translate<=this.maxTranslate()||u>n.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(l&&(c>n.startX&&-this.translate<=this.maxTranslate()||c<n.startX&&-this.translate>=this.minTranslate()))return;else if(!l&&(c<n.startX&&this.translate<=this.maxTranslate()||c>n.startX&&this.translate>=this.minTranslate()))return}if(s.activeElement&&s.activeElement.matches(r.focusableElements)&&s.activeElement!==d.target&&"mouse"!==d.pointerType&&s.activeElement.blur(),s.activeElement&&d.target===s.activeElement&&d.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}r.allowTouchCallbacks&&this.emit("touchMove",d),n.previousX=n.currentX,n.previousY=n.currentY,n.currentX=c,n.currentY=u;let h=n.currentX-n.startX,m=n.currentY-n.startY;if(this.params.threshold&&Math.sqrt(h**2+m**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&n.currentY===n.startY||this.isVertical()&&n.currentX===n.startX?r.isScrolling=!1:h*h+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(h))/Math.PI,r.isScrolling=this.isHorizontal()?e>a.touchAngle:90-e>a.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",d),void 0===r.startMoving&&(n.currentX!==n.startX||n.currentY!==n.startY)&&(r.startMoving=!0),r.isScrolling||"touchmove"===d.type&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!a.cssMode&&d.cancelable&&d.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&d.stopPropagation();let g=this.isHorizontal()?h:m,v=this.isHorizontal()?n.currentX-n.previousX:n.currentY-n.previousY;a.oneWayMovement&&(g=Math.abs(g)*(l?1:-1),v=Math.abs(v)*(l?1:-1)),n.diff=g,g*=a.touchRatio,l&&(g=-g,v=-v);let _=this.touchesDirection;this.swipeDirection=g>0?"prev":"next",this.touchesDirection=v>0?"prev":"next";let w=this.params.loop&&!a.cssMode,b="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!r.isMoved){if(w&&b&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,a.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",d)}if(new Date().getTime(),!1!==a._loopSwapReset&&r.isMoved&&r.allowThresholdMove&&_!==this.touchesDirection&&w&&b&&Math.abs(g)>=1){Object.assign(n,{startX:c,startY:u,currentX:c,currentY:u,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}this.emit("sliderMove",d),r.isMoved=!0,r.currentTranslate=g+r.startTranslate;let y=!0,x=a.resistanceRatio;if(a.touchReleaseOnEdges&&(x=0),g>0?(w&&b&&!i&&r.allowThresholdMove&&r.currentTranslate>(a.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==a.slidesPerView&&this.slides.length-a.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(y=!1,a.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+g)**x))):g<0&&(w&&b&&!i&&r.allowThresholdMove&&r.currentTranslate<(a.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==a.slidesPerView&&this.slides.length-a.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===a.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(a.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(y=!1,a.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-g)**x))),y&&(d.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),a.threshold>0){if(Math.abs(g)>a.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,r.currentTranslate=r.startTranslate,n.diff=this.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY;return}}else{r.currentTranslate=r.startTranslate;return}}a.followFinger&&!a.cssMode&&((a.freeMode&&a.freeMode.enabled&&this.freeMode||a.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),a.freeMode&&a.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function R(e){let t,i;let s=this,r=s.touchEventsData,a=e;if(a.originalEvent&&(a=a.originalEvent),"touchend"===a.type||"touchcancel"===a.type){if(!(t=[...a.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else{if(null!==r.touchId||a.pointerId!==r.pointerId)return;t=a}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(a.type)&&!(["pointercancel","contextmenu"].includes(a.type)&&(s.browser.isSafari||s.browser.isWebView)))return;r.pointerId=null,r.touchId=null;let{params:n,touches:l,rtlTranslate:o,slidesGrid:d,enabled:c}=s;if(!c||!n.simulateTouch&&"mouse"===a.pointerType)return;if(r.allowTouchCallbacks&&s.emit("touchEnd",a),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&n.grabCursor&&s.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}n.grabCursor&&r.isMoved&&r.isTouched&&(!0===s.allowSlideNext||!0===s.allowSlidePrev)&&s.setGrabCursor(!1);let p=f(),u=p-r.touchStartTime;if(s.allowClick){let e=a.path||a.composedPath&&a.composedPath();s.updateClickedSlide(e&&e[0]||a.target,e),s.emit("tap click",a),u<300&&p-r.lastClickTime<300&&s.emit("doubleTap doubleClick",a)}if(r.lastClickTime=f(),m(()=>{s.destroyed||(s.allowClick=!0)}),!r.isTouched||!r.isMoved||!s.swipeDirection||0===l.diff&&!r.loopSwapReset||r.currentTranslate===r.startTranslate&&!r.loopSwapReset){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,i=n.followFinger?o?s.translate:-s.translate:-r.currentTranslate,n.cssMode)return;if(n.freeMode&&n.freeMode.enabled){s.freeMode.onTouchEnd({currentPos:i});return}let h=i>=-s.maxTranslate()&&!s.params.loop,g=0,v=s.slidesSizesGrid[0];for(let e=0;e<d.length;e+=e<n.slidesPerGroupSkip?1:n.slidesPerGroup){let t=e<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;void 0!==d[e+t]?(h||i>=d[e]&&i<d[e+t])&&(g=e,v=d[e+t]-d[e]):(h||i>=d[e])&&(g=e,v=d[d.length-1]-d[d.length-2])}let _=null,w=null;n.rewind&&(s.isBeginning?w=n.virtual&&n.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1:s.isEnd&&(_=0));let b=(i-d[g])/v,y=g<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;if(u>n.longSwipesMs){if(!n.longSwipes){s.slideTo(s.activeIndex);return}"next"===s.swipeDirection&&(b>=n.longSwipesRatio?s.slideTo(n.rewind&&s.isEnd?_:g+y):s.slideTo(g)),"prev"===s.swipeDirection&&(b>1-n.longSwipesRatio?s.slideTo(g+y):null!==w&&b<0&&Math.abs(b)>n.longSwipesRatio?s.slideTo(w):s.slideTo(g))}else{if(!n.shortSwipes){s.slideTo(s.activeIndex);return}s.navigation&&(a.target===s.navigation.nextEl||a.target===s.navigation.prevEl)?a.target===s.navigation.nextEl?s.slideTo(g+y):s.slideTo(g):("next"===s.swipeDirection&&s.slideTo(null!==_?_:g+y),"prev"===s.swipeDirection&&s.slideTo(null!==w?w:g))}}function F(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:r,snapGrid:a}=e,n=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let l=n&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||l?e.params.loop&&!n?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=s,e.params.watchOverflow&&a!==e.snapGrid&&e.checkOverflow()}function V(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function H(){let{wrapperEl:e,rtlTranslate:t,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let s=this.maxTranslate()-this.minTranslate();(0===s?0:(this.translate-this.minTranslate())/s)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function q(e){I(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function W(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let X=(e,t)=>{let i=p(),{params:s,el:r,wrapperEl:a,device:n}=e,l=!!s.nested,o="on"===t?"addEventListener":"removeEventListener";r&&"string"!=typeof r&&(i[o]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:l}),r[o]("touchstart",e.onTouchStart,{passive:!1}),r[o]("pointerdown",e.onTouchStart,{passive:!1}),i[o]("touchmove",e.onTouchMove,{passive:!1,capture:l}),i[o]("pointermove",e.onTouchMove,{passive:!1,capture:l}),i[o]("touchend",e.onTouchEnd,{passive:!0}),i[o]("pointerup",e.onTouchEnd,{passive:!0}),i[o]("pointercancel",e.onTouchEnd,{passive:!0}),i[o]("touchcancel",e.onTouchEnd,{passive:!0}),i[o]("pointerout",e.onTouchEnd,{passive:!0}),i[o]("pointerleave",e.onTouchEnd,{passive:!0}),i[o]("contextmenu",e.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&r[o]("click",e.onClick,!0),s.cssMode&&a[o]("scroll",e.onScroll),s.updateOnWindowResize?e[t](n.ios||n.android?"resize orientationchange observerUpdate":"resize observerUpdate",F,!0):e[t]("observerUpdate",F,!0),r[o]("load",e.onLoad,{capture:!0}))},Y=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var K={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let U={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let r=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][r](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function r(){s.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var i=arguments.length,a=Array(i),n=0;n<i;n++)a[n]=arguments[n];t.apply(s,a)}return r.__emitterProxy=t,s.on(e,r,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,r)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(r,1)})}),i},emit(){let e,t,i;let s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;for(var r=arguments.length,a=Array(r),n=0;n<r;n++)a[n]=arguments[n];return"string"==typeof a[0]||Array.isArray(a[0])?(e=a[0],t=a.slice(1,a.length),i=s):(e=a[0].events,t=a[0].data,i=a[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(s=>{s.apply(i,[e,...t])}),s.eventsListeners&&s.eventsListeners[e]&&s.eventsListeners[e].forEach(e=>{e.apply(i,t)})}),s}},update:{updateSize:function(){let e,t;let i=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt(S(i,"padding-left")||0,10)-parseInt(S(i,"padding-right")||0,10),t=t-parseInt(S(i,"padding-top")||0,10)-parseInt(S(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e;let t=this;function i(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}let s=t.params,{wrapperEl:r,slidesEl:a,size:n,rtlTranslate:l,wrongRTL:o}=t,d=t.virtual&&s.virtual.enabled,c=d?t.virtual.slides.length:t.slides.length,p=b(a,`.${t.params.slideClass}, swiper-slide`),u=d?t.virtual.slides.length:p.length,h=[],m=[],f=[],g=s.slidesOffsetBefore;"function"==typeof g&&(g=s.slidesOffsetBefore.call(t));let v=s.slidesOffsetAfter;"function"==typeof v&&(v=s.slidesOffsetAfter.call(t));let w=t.snapGrid.length,y=t.slidesGrid.length,x=s.spaceBetween,E=-g,T=0,P=0;if(void 0===n)return;"string"==typeof x&&x.indexOf("%")>=0?x=parseFloat(x.replace("%",""))/100*n:"string"==typeof x&&(x=parseFloat(x)),t.virtualSize=-x,p.forEach(e=>{l?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),s.centeredSlides&&s.cssMode&&(_(r,"--swiper-centered-offset-before",""),_(r,"--swiper-centered-offset-after",""));let M=s.grid&&s.grid.rows>1&&t.grid;M?t.grid.initSlides(p):t.grid&&t.grid.unsetSlides();let k="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter(e=>void 0!==s.breakpoints[e].slidesPerView).length>0;for(let r=0;r<u;r+=1){let a;if(e=0,p[r]&&(a=p[r]),M&&t.grid.updateSlide(r,a,p),!p[r]||"none"!==S(a,"display")){if("auto"===s.slidesPerView){k&&(p[r].style[t.getDirectionLabel("width")]="");let n=getComputedStyle(a),l=a.style.transform,o=a.style.webkitTransform;if(l&&(a.style.transform="none"),o&&(a.style.webkitTransform="none"),s.roundLengths)e=t.isHorizontal()?C(a,"width",!0):C(a,"height",!0);else{let t=i(n,"width"),s=i(n,"padding-left"),r=i(n,"padding-right"),l=i(n,"margin-left"),o=i(n,"margin-right"),d=n.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+l+o;else{let{clientWidth:i,offsetWidth:n}=a;e=t+s+r+l+o+(n-i)}}l&&(a.style.transform=l),o&&(a.style.webkitTransform=o),s.roundLengths&&(e=Math.floor(e))}else e=(n-(s.slidesPerView-1)*x)/s.slidesPerView,s.roundLengths&&(e=Math.floor(e)),p[r]&&(p[r].style[t.getDirectionLabel("width")]=`${e}px`);p[r]&&(p[r].swiperSlideSize=e),f.push(e),s.centeredSlides?(E=E+e/2+T/2+x,0===T&&0!==r&&(E=E-n/2-x),0===r&&(E=E-n/2-x),.001>Math.abs(E)&&(E=0),s.roundLengths&&(E=Math.floor(E)),P%s.slidesPerGroup==0&&h.push(E),m.push(E)):(s.roundLengths&&(E=Math.floor(E)),(P-Math.min(t.params.slidesPerGroupSkip,P))%t.params.slidesPerGroup==0&&h.push(E),m.push(E),E=E+e+x),t.virtualSize+=e+x,T=e,P+=1}}if(t.virtualSize=Math.max(t.virtualSize,n)+v,l&&o&&("slide"===s.effect||"coverflow"===s.effect)&&(r.style.width=`${t.virtualSize+x}px`),s.setWrapperSize&&(r.style[t.getDirectionLabel("width")]=`${t.virtualSize+x}px`),M&&t.grid.updateWrapperSize(e,h),!s.centeredSlides){let e=[];for(let i=0;i<h.length;i+=1){let r=h[i];s.roundLengths&&(r=Math.floor(r)),h[i]<=t.virtualSize-n&&e.push(r)}h=e,Math.floor(t.virtualSize-n)-Math.floor(h[h.length-1])>1&&h.push(t.virtualSize-n)}if(d&&s.loop){let e=f[0]+x;if(s.slidesPerGroup>1){let i=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/s.slidesPerGroup),r=e*s.slidesPerGroup;for(let e=0;e<i;e+=1)h.push(h[h.length-1]+r)}for(let i=0;i<t.virtual.slidesBefore+t.virtual.slidesAfter;i+=1)1===s.slidesPerGroup&&h.push(h[h.length-1]+e),m.push(m[m.length-1]+e),t.virtualSize+=e}if(0===h.length&&(h=[0]),0!==x){let e=t.isHorizontal()&&l?"marginLeft":t.getDirectionLabel("marginRight");p.filter((e,t)=>!s.cssMode||!!s.loop||t!==p.length-1).forEach(t=>{t.style[e]=`${x}px`})}if(s.centeredSlides&&s.centeredSlidesBounds){let e=0;f.forEach(t=>{e+=t+(x||0)});let t=(e-=x)>n?e-n:0;h=h.map(e=>e<=0?-g:e>t?t+v:e)}if(s.centerInsufficientSlides){let e=0;f.forEach(t=>{e+=t+(x||0)}),e-=x;let t=(s.slidesOffsetBefore||0)+(s.slidesOffsetAfter||0);if(e+t<n){let i=(n-e-t)/2;h.forEach((e,t)=>{h[t]=e-i}),m.forEach((e,t)=>{m[t]=e+i})}}if(Object.assign(t,{slides:p,snapGrid:h,slidesGrid:m,slidesSizesGrid:f}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){_(r,"--swiper-centered-offset-before",`${-h[0]}px`),_(r,"--swiper-centered-offset-after",`${t.size/2-f[f.length-1]/2}px`);let e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+i)}if(u!==c&&t.emit("slidesLengthChange"),h.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),m.length!==y&&t.emit("slidesGridLengthChange"),s.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!d&&!s.cssMode&&("slide"===s.effect||"fade"===s.effect)){let e=`${s.containerModifierClass}backface-hidden`,i=t.el.classList.contains(e);u<=s.maxBackfaceHiddenSlides?i||t.el.classList.add(e):i&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t;let i=this,s=[],r=i.virtual&&i.params.virtual.enabled,a=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let n=e=>r?i.slides[i.getSlideIndexByData(e)]:i.slides[e];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1){if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(e=>{s.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let e=i.activeIndex+t;if(e>i.slides.length&&!r)break;s.push(n(e))}}else s.push(n(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){let e=s[t].offsetHeight;a=e>a?e:a}(a||0===a)&&(i.wrapperEl.style.height=`${a}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(this.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:i,rtlTranslate:s,snapGrid:r}=this;if(0===i.length)return;void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();let a=-e;s&&(a=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let n=t.spaceBetween;"string"==typeof n&&n.indexOf("%")>=0?n=parseFloat(n.replace("%",""))/100*this.size:"string"==typeof n&&(n=parseFloat(n));for(let e=0;e<i.length;e+=1){let l=i[e],o=l.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=i[0].swiperSlideOffset);let d=(a+(t.centeredSlides?this.minTranslate():0)-o)/(l.swiperSlideSize+n),c=(a-r[0]+(t.centeredSlides?this.minTranslate():0)-o)/(l.swiperSlideSize+n),p=-(a-o),u=p+this.slidesSizesGrid[e],h=p>=0&&p<=this.size-this.slidesSizesGrid[e],m=p>=0&&p<this.size-1||u>1&&u<=this.size||p<=0&&u>=this.size;m&&(this.visibleSlides.push(l),this.visibleSlidesIndexes.push(e)),O(l,m,t.slideVisibleClass),O(l,h,t.slideFullyVisibleClass),l.progress=s?-d:d,l.originalProgress=s?-c:c}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,i=this.maxTranslate()-this.minTranslate(),{progress:s,isBeginning:r,isEnd:a,progressLoop:n}=this,l=r,o=a;if(0===i)s=0,r=!0,a=!0;else{s=(e-this.minTranslate())/i;let t=1>Math.abs(e-this.minTranslate()),n=1>Math.abs(e-this.maxTranslate());r=t||s<=0,a=n||s>=1,t&&(s=0),n&&(s=1)}if(t.loop){let t=this.getSlideIndexByData(0),i=this.getSlideIndexByData(this.slides.length-1),s=this.slidesGrid[t],r=this.slidesGrid[i],a=this.slidesGrid[this.slidesGrid.length-1],l=Math.abs(e);(n=l>=s?(l-s)/a:(l+a-r)/a)>1&&(n-=1)}Object.assign(this,{progress:s,progressLoop:n,isBeginning:r,isEnd:a}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),r&&!l&&this.emit("reachBeginning toEdge"),a&&!o&&this.emit("reachEnd toEdge"),(l&&!r||o&&!a)&&this.emit("fromEdge"),this.emit("progress",s)},updateSlidesClasses:function(){let e,t,i;let{slides:s,params:r,slidesEl:a,activeIndex:n}=this,l=this.virtual&&r.virtual.enabled,o=this.grid&&r.grid&&r.grid.rows>1,d=e=>b(a,`.${r.slideClass}${e}, swiper-slide${e}`)[0];if(l){if(r.loop){let t=n-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=d(`[data-swiper-slide-index="${t}"]`)}else e=d(`[data-swiper-slide-index="${n}"]`)}else o?(e=s.find(e=>e.column===n),i=s.find(e=>e.column===n+1),t=s.find(e=>e.column===n-1)):e=s[n];e&&!o&&(i=function(e,t){let i=[];for(;e.nextElementSibling;){let s=e.nextElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}(e,`.${r.slideClass}, swiper-slide`)[0],r.loop&&!i&&(i=s[0]),t=function(e,t){let i=[];for(;e.previousElementSibling;){let s=e.previousElementSibling;t?s.matches(t)&&i.push(s):i.push(s),e=s}return i}(e,`.${r.slideClass}, swiper-slide`)[0],r.loop),s.forEach(s=>{A(s,s===e,r.slideActiveClass),A(s,s===i,r.slideNextClass),A(s,s===t,r.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,i;let s=this,r=s.rtlTranslate?s.translate:-s.translate,{snapGrid:a,params:n,activeIndex:l,realIndex:o,snapIndex:d}=s,c=e,p=e=>{let t=e-s.virtual.slidesBefore;return t<0&&(t=s.virtual.slides.length+t),t>=s.virtual.slides.length&&(t-=s.virtual.slides.length),t};if(void 0===c&&(c=function(e){let t;let{slidesGrid:i,params:s}=e,r=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<i.length;e+=1)void 0!==i[e+1]?r>=i[e]&&r<i[e+1]-(i[e+1]-i[e])/2?t=e:r>=i[e]&&r<i[e+1]&&(t=e+1):r>=i[e]&&(t=e);return s.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(s)),a.indexOf(r)>=0)t=a.indexOf(r);else{let e=Math.min(n.slidesPerGroupSkip,c);t=e+Math.floor((c-e)/n.slidesPerGroup)}if(t>=a.length&&(t=a.length-1),c===l&&!s.params.loop){t!==d&&(s.snapIndex=t,s.emit("snapIndexChange"));return}if(c===l&&s.params.loop&&s.virtual&&s.params.virtual.enabled){s.realIndex=p(c);return}let u=s.grid&&n.grid&&n.grid.rows>1;if(s.virtual&&n.virtual.enabled&&n.loop)i=p(c);else if(u){let e=s.slides.find(e=>e.column===c),t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(s.slides.indexOf(e),0)),i=Math.floor(t/n.grid.rows)}else if(s.slides[c]){let e=s.slides[c].getAttribute("data-swiper-slide-index");i=e?parseInt(e,10):c}else i=c;Object.assign(s,{previousSnapIndex:d,snapIndex:t,previousRealIndex:o,realIndex:i,previousIndex:l,activeIndex:c}),s.initialized&&z(s),s.emit("activeIndexChange"),s.emit("snapIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&(o!==i&&s.emit("realIndexChange"),s.emit("slideChange"))},updateClickedSlide:function(e,t){let i;let s=this.params,r=e.closest(`.${s.slideClass}, swiper-slide`);!r&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${s.slideClass}, swiper-slide`)&&(r=e)});let a=!1;if(r){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===r){a=!0,i=e;break}}if(r&&a)this.clickedSlide=r,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=i;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}s.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:i,translate:s,wrapperEl:r}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let a=function(e,t){let i,s,r;void 0===t&&(t="x");let a=h(),n=function(e){let t;let i=h();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((s=n.transform||n.webkitTransform).split(",").length>6&&(s=s.split(", ").map(e=>e.replace(",",".")).join(", ")),r=new a.WebKitCSSMatrix("none"===s?"":s)):i=(r=n.MozTransform||n.OTransform||n.MsTransform||n.msTransform||n.transform||n.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(s=a.WebKitCSSMatrix?r.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(s=a.WebKitCSSMatrix?r.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),s||0}(r,e);return a+=this.cssOverflowAdjustment(),i&&(a=-a),a||0},setTranslate:function(e,t){let{rtlTranslate:i,params:s,wrapperEl:r,progress:a}=this,n=0,l=0;this.isHorizontal()?n=i?-e:e:l=e,s.roundLengths&&(n=Math.floor(n),l=Math.floor(l)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?n:l,s.cssMode?r[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-n:-l:s.virtualTranslate||(this.isHorizontal()?n-=this.cssOverflowAdjustment():l-=this.cssOverflowAdjustment(),r.style.transform=`translate3d(${n}px, ${l}px, 0px)`);let o=this.maxTranslate()-this.minTranslate();(0===o?0:(e-this.minTranslate())/o)!==a&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,r){let a;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);let n=this,{params:l,wrapperEl:o}=n;if(n.animating&&l.preventInteractionOnTransition)return!1;let d=n.minTranslate(),c=n.maxTranslate();if(a=s&&e>d?d:s&&e<c?c:e,n.updateProgress(a),l.cssMode){let e=n.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-a;else{if(!n.support.smoothScroll)return w({swiper:n,targetPosition:-a,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-a,behavior:"smooth"})}return!0}return 0===t?(n.setTransition(0),n.setTranslate(a),i&&(n.emit("beforeTransitionStart",t,r),n.emit("transitionEnd"))):(n.setTransition(t),n.setTranslate(a),i&&(n.emit("beforeTransitionStart",t,r),n.emit("transitionStart")),n.animating||(n.animating=!0,n.onTranslateToWrapperTransitionEnd||(n.onTranslateToWrapperTransitionEnd=function(e){n&&!n.destroyed&&e.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onTranslateToWrapperTransitionEnd),n.onTranslateToWrapperTransitionEnd=null,delete n.onTranslateToWrapperTransitionEnd,n.animating=!1,i&&n.emit("transitionEnd"))}),n.wrapperEl.addEventListener("transitionend",n.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),$({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:i}=this;this.animating=!1,i.cssMode||(this.setTransition(0),$({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,r){let a;void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let n=this,l=e;l<0&&(l=0);let{params:o,snapGrid:d,slidesGrid:c,previousIndex:p,activeIndex:u,rtlTranslate:h,wrapperEl:m,enabled:f}=n;if(!f&&!s&&!r||n.destroyed||n.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=n.params.speed);let g=Math.min(n.params.slidesPerGroupSkip,l),v=g+Math.floor((l-g)/n.params.slidesPerGroup);v>=d.length&&(v=d.length-1);let _=-d[v];if(o.normalizeSlideIndex)for(let e=0;e<c.length;e+=1){let t=-Math.floor(100*_),i=Math.floor(100*c[e]),s=Math.floor(100*c[e+1]);void 0!==c[e+1]?t>=i&&t<s-(s-i)/2?l=e:t>=i&&t<s&&(l=e+1):t>=i&&(l=e)}if(n.initialized&&l!==u&&(!n.allowSlideNext&&(h?_>n.translate&&_>n.minTranslate():_<n.translate&&_<n.minTranslate())||!n.allowSlidePrev&&_>n.translate&&_>n.maxTranslate()&&(u||0)!==l))return!1;l!==(p||0)&&i&&n.emit("beforeSlideChangeStart"),n.updateProgress(_),a=l>u?"next":l<u?"prev":"reset";let b=n.virtual&&n.params.virtual.enabled;if(!(b&&r)&&(h&&-_===n.translate||!h&&_===n.translate))return n.updateActiveIndex(l),o.autoHeight&&n.updateAutoHeight(),n.updateSlidesClasses(),"slide"!==o.effect&&n.setTranslate(_),"reset"!==a&&(n.transitionStart(i,a),n.transitionEnd(i,a)),!1;if(o.cssMode){let e=n.isHorizontal(),i=h?_:-_;if(0===t)b&&(n.wrapperEl.style.scrollSnapType="none",n._immediateVirtual=!0),b&&!n._cssModeVirtualInitialSet&&n.params.initialSlide>0?(n._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[e?"scrollLeft":"scrollTop"]=i})):m[e?"scrollLeft":"scrollTop"]=i,b&&requestAnimationFrame(()=>{n.wrapperEl.style.scrollSnapType="",n._immediateVirtual=!1});else{if(!n.support.smoothScroll)return w({swiper:n,targetPosition:i,side:e?"left":"top"}),!0;m.scrollTo({[e?"left":"top"]:i,behavior:"smooth"})}return!0}let y=L().isSafari;return b&&!r&&y&&n.isElement&&n.virtual.update(!1,!1,l),n.setTransition(t),n.setTranslate(_),n.updateActiveIndex(l),n.updateSlidesClasses(),n.emit("beforeTransitionStart",t,s),n.transitionStart(i,a),0===t?n.transitionEnd(i,a):n.animating||(n.animating=!0,n.onSlideToWrapperTransitionEnd||(n.onSlideToWrapperTransitionEnd=function(e){n&&!n.destroyed&&e.target===this&&(n.wrapperEl.removeEventListener("transitionend",n.onSlideToWrapperTransitionEnd),n.onSlideToWrapperTransitionEnd=null,delete n.onSlideToWrapperTransitionEnd,n.transitionEnd(i,a))}),n.wrapperEl.addEventListener("transitionend",n.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){void 0===e&&(e=0),void 0===i&&(i=!0),"string"==typeof e&&(e=parseInt(e,10));let r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);let a=r.grid&&r.params.grid&&r.params.grid.rows>1,n=e;if(r.params.loop){if(r.virtual&&r.params.virtual.enabled)n+=r.virtual.slidesBefore;else{let e;if(a){let t=n*r.params.grid.rows;e=r.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t).column}else e=r.getSlideIndexByData(n);let t=a?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:i}=r.params,l=r.params.slidesPerView;"auto"===l?l=r.slidesPerViewDynamic():(l=Math.ceil(parseFloat(r.params.slidesPerView,10)),i&&l%2==0&&(l+=1));let o=t-e<l;if(i&&(o=o||e<Math.ceil(l/2)),s&&i&&"auto"!==r.params.slidesPerView&&!a&&(o=!1),o){let s=i?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?e+1:e-t+1,slideRealIndex:"next"===s?r.realIndex:void 0})}if(a){let e=n*r.params.grid.rows;n=r.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e).column}else n=r.getSlideIndexByData(n)}}return requestAnimationFrame(()=>{r.slideTo(n,t,i,s)}),r},slideNext:function(e,t,i){void 0===t&&(t=!0);let s=this,{enabled:r,params:a,animating:n}=s;if(!r||s.destroyed)return s;void 0===e&&(e=s.params.speed);let l=a.slidesPerGroup;"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(l=Math.max(s.slidesPerViewDynamic("current",!0),1));let o=s.activeIndex<a.slidesPerGroupSkip?1:l,d=s.virtual&&a.virtual.enabled;if(a.loop){if(n&&!d&&a.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&a.cssMode)return requestAnimationFrame(()=>{s.slideTo(s.activeIndex+o,e,t,i)}),!0}return a.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===t&&(t=!0);let s=this,{params:r,snapGrid:a,slidesGrid:n,rtlTranslate:l,enabled:o,animating:d}=s;if(!o||s.destroyed)return s;void 0===e&&(e=s.params.speed);let c=s.virtual&&r.virtual.enabled;if(r.loop){if(d&&!c&&r.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let u=p(l?s.translate:-s.translate),h=a.map(e=>p(e)),m=r.freeMode&&r.freeMode.enabled,f=a[h.indexOf(u)-1];if(void 0===f&&(r.cssMode||m)){let e;a.forEach((t,i)=>{u>=t&&(e=i)}),void 0!==e&&(f=m?a[e]:a[e>0?e-1:e])}let g=0;if(void 0!==f&&((g=n.indexOf(f))<0&&(g=s.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(g=Math.max(g=g-s.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&s.isBeginning){let r=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(r,e,t,i)}return r.loop&&0===s.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{s.slideTo(g,e,t,i)}),!0):s.slideTo(g,e,t,i)},slideReset:function(e,t,i){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){if(void 0===t&&(t=!0),void 0===s&&(s=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let r=this.activeIndex,a=Math.min(this.params.slidesPerGroupSkip,r),n=a+Math.floor((r-a)/this.params.slidesPerGroup),l=this.rtlTranslate?this.translate:-this.translate;if(l>=this.snapGrid[n]){let e=this.snapGrid[n];l-e>(this.snapGrid[n+1]-e)*s&&(r+=this.params.slidesPerGroup)}else{let e=this.snapGrid[n-1];l-e<=(this.snapGrid[n]-e)*s&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,e,t,i)},slideToClickedSlide:function(){let e;let t=this;if(t.destroyed)return;let{params:i,slidesEl:s}=t,r="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,a=t.getSlideIndexWhenGrid(t.clickedIndex),n=t.isElement?"swiper-slide":`.${i.slideClass}`,l=t.grid&&t.params.grid&&t.params.grid.rows>1;if(i.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),i.centeredSlides?t.slideToLoop(e):a>(l?(t.slides.length-r)/2-(t.params.grid.rows-1):t.slides.length-r)?(t.loopFix(),a=t.getSlideIndex(b(s,`${n}[data-swiper-slide-index="${e}"]`)[0]),m(()=>{t.slideTo(a)})):t.slideTo(a)}else t.slideTo(a)}},loop:{loopCreate:function(e,t){let i=this,{params:s,slidesEl:r}=i;if(!s.loop||i.virtual&&i.params.virtual.enabled)return;let a=i.grid&&s.grid&&s.grid.rows>1;s.loopAddBlankSlides&&(s.slidesPerGroup>1||a)&&(()=>{let e=b(r,`.${s.slideBlankClass}`);e.forEach(e=>{e.remove()}),e.length>0&&(i.recalcSlides(),i.updateSlides())})();let n=s.slidesPerGroup*(a?s.grid.rows:1),l=i.slides.length%n!=0,o=a&&i.slides.length%s.grid.rows!=0,d=e=>{for(let t=0;t<e;t+=1){let e=i.isElement?x("swiper-slide",[s.slideBlankClass]):x("div",[s.slideClass,s.slideBlankClass]);i.slidesEl.append(e)}};l?s.loopAddBlankSlides?(d(n-i.slides.length%n),i.recalcSlides(),i.updateSlides()):y("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):o&&(s.loopAddBlankSlides?(d(s.grid.rows-i.slides.length%s.grid.rows),i.recalcSlides(),i.updateSlides()):y("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),b(r,`.${s.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),i.loopFix({slideRealIndex:e,direction:s.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:i=!0,direction:s,setTranslate:r,activeSlideIndex:a,initial:n,byController:l,byMousewheel:o}=void 0===e?{}:e,d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");let{slides:c,allowSlidePrev:p,allowSlideNext:u,slidesEl:h,params:m}=d,{centeredSlides:f,initialSlide:g}=m;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&m.virtual.enabled){i&&(m.centeredSlides||0!==d.snapIndex?m.centeredSlides&&d.snapIndex<m.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=p,d.allowSlideNext=u,d.emit("loopFix");return}let v=m.slidesPerView;"auto"===v?v=d.slidesPerViewDynamic():(v=Math.ceil(parseFloat(m.slidesPerView,10)),f&&v%2==0&&(v+=1));let _=m.slidesPerGroupAuto?v:m.slidesPerGroup,w=f?Math.max(_,Math.ceil(v/2)):_;w%_!=0&&(w+=_-w%_),w+=m.loopAdditionalSlides,d.loopedSlides=w;let b=d.grid&&m.grid&&m.grid.rows>1;c.length<v+w||"cards"===d.params.effect&&c.length<v+2*w?y("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):b&&"row"===m.grid.fill&&y("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let x=[],S=[],E=b?Math.ceil(c.length/m.grid.rows):c.length,T=n&&E-g<v&&!f,C=T?g:d.activeIndex;void 0===a?a=d.getSlideIndex(c.find(e=>e.classList.contains(m.slideActiveClass))):C=a;let P="next"===s||!s,M="prev"===s||!s,k=0,j=0,L=(b?c[a].column:a)+(f&&void 0===r?-v/2+.5:0);if(L<w){k=Math.max(w-L,_);for(let e=0;e<w-L;e+=1){let t=e-Math.floor(e/E)*E;if(b){let e=E-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&x.push(t)}else x.push(E-t-1)}}else if(L+v>E-w){j=Math.max(L-(E-2*w),_),T&&(j=Math.max(j,v-E+g+1));for(let e=0;e<j;e+=1){let t=e-Math.floor(e/E)*E;b?c.forEach((e,i)=>{e.column===t&&S.push(i)}):S.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),"cards"===d.params.effect&&c.length<v+2*w&&(S.includes(a)&&S.splice(S.indexOf(a),1),x.includes(a)&&x.splice(x.indexOf(a),1)),M&&x.forEach(e=>{c[e].swiperLoopMoveDOM=!0,h.prepend(c[e]),c[e].swiperLoopMoveDOM=!1}),P&&S.forEach(e=>{c[e].swiperLoopMoveDOM=!0,h.append(c[e]),c[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===m.slidesPerView?d.updateSlides():b&&(x.length>0&&M||S.length>0&&P)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),m.watchSlidesProgress&&d.updateSlidesOffset(),i){if(x.length>0&&M){if(void 0===t){let e=d.slidesGrid[C],t=d.slidesGrid[C+k]-e;o?d.setTranslate(d.translate-t):(d.slideTo(C+Math.ceil(k),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(r){let e=b?x.length/m.grid.rows:x.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(S.length>0&&P){if(void 0===t){let e=d.slidesGrid[C],t=d.slidesGrid[C-j]-e;o?d.setTranslate(d.translate-t):(d.slideTo(C-j,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{let e=b?S.length/m.grid.rows:S.length;d.slideTo(d.activeIndex-e,0,!1,!0)}}}if(d.allowSlidePrev=p,d.allowSlideNext=u,d.controller&&d.controller.control&&!l){let e={slideRealIndex:t,direction:s,setTranslate:r,activeSlideIndex:a,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&i})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===m.slidesPerView&&i})}d.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||!t||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let i=[];this.slides.forEach(e=>{i[void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),i.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let i="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=B.bind(this),this.onTouchMove=G.bind(this),this.onTouchEnd=R.bind(this),this.onDocumentTouchStart=W.bind(this),e.cssMode&&(this.onScroll=H.bind(this)),this.onClick=V.bind(this),this.onLoad=q.bind(this),X(this,"on")},detachEvents:function(){X(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:i,params:s,el:r}=e,a=s.breakpoints;if(!a||a&&0===Object.keys(a).length)return;let n=p(),l="window"!==s.breakpointsBase&&s.breakpointsBase?"container":s.breakpointsBase,o=["window","container"].includes(s.breakpointsBase)||!s.breakpointsBase?e.el:n.querySelector(s.breakpointsBase),d=e.getBreakpoint(a,l,o);if(!d||e.currentBreakpoint===d)return;let c=(d in a?a[d]:void 0)||e.originalParams,u=Y(e,s),h=Y(e,c),m=e.params.grabCursor,f=c.grabCursor,g=s.enabled;u&&!h?(r.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),e.emitContainerClasses()):!u&&h&&(r.classList.add(`${s.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===s.grid.fill)&&r.classList.add(`${s.containerModifierClass}grid-column`),e.emitContainerClasses()),m&&!f?e.unsetGrabCursor():!m&&f&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===c[t])return;let i=s[t]&&s[t].enabled,r=c[t]&&c[t].enabled;i&&!r&&e[t].disable(),!i&&r&&e[t].enable()});let _=c.direction&&c.direction!==s.direction,w=s.loop&&(c.slidesPerView!==s.slidesPerView||_),b=s.loop;_&&i&&e.changeDirection(),v(e.params,c);let y=e.params.enabled,x=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),g&&!y?e.disable():!g&&y&&e.enable(),e.currentBreakpoint=d,e.emit("_beforeBreakpoint",c),i&&(w?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!b&&x?(e.loopCreate(t),e.updateSlides()):b&&!x&&e.loopDestroy()),e.emit("breakpoint",c)},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),!e||"container"===t&&!i)return;let s=!1,r=h(),a="window"===t?r.innerHeight:i.clientHeight,n=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:a*parseFloat(e.substr(1)),point:e}:{value:e,point:e});n.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<n.length;e+=1){let{point:a,value:l}=n[e];"window"===t?r.matchMedia(`(min-width: ${l}px)`).matches&&(s=a):l<=i.clientWidth&&(s=a)}return s||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:i}=t;if(i){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*i;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,el:s,device:r}=this,a=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...a),s.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},J={};class Z{constructor(){let e,t;for(var i=arguments.length,s=Array(i),r=0;r<i;r++)s[r]=arguments[r];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?t=s[0]:[e,t]=s,t||(t={}),t=v({},t),e&&!t.el&&(t.el=e);let a=p();if(t.el&&"string"==typeof t.el&&a.querySelectorAll(t.el).length>1){let e=[];return a.querySelectorAll(t.el).forEach(i=>{let s=v({},t,{el:i});e.push(new Z(s))}),e}let n=this;n.__swiper__=!0,n.support=k(),n.device=j({userAgent:t.userAgent}),n.browser=L(),n.eventsListeners={},n.eventsAnyListeners=[],n.modules=[...n.__modules__],t.modules&&Array.isArray(t.modules)&&n.modules.push(...t.modules);let l={};n.modules.forEach(e=>{e({params:t,swiper:n,extendParams:function(e,t){return function(i){void 0===i&&(i={});let s=Object.keys(i)[0],r=i[s];if("object"!=typeof r||null===r||(!0===e[s]&&(e[s]={enabled:!0}),"navigation"===s&&e[s]&&e[s].enabled&&!e[s].prevEl&&!e[s].nextEl&&(e[s].auto=!0),["pagination","scrollbar"].indexOf(s)>=0&&e[s]&&e[s].enabled&&!e[s].el&&(e[s].auto=!0),!(s in e&&"enabled"in r))){v(t,i);return}"object"!=typeof e[s]||"enabled"in e[s]||(e[s].enabled=!0),e[s]||(e[s]={enabled:!1}),v(t,i)}}(t,l),on:n.on.bind(n),once:n.once.bind(n),off:n.off.bind(n),emit:n.emit.bind(n)})});let o=v({},K,l);return n.params=v({},o,J,t),n.originalParams=v({},n.params),n.passedParams=v({},t),n.params&&n.params.on&&Object.keys(n.params.on).forEach(e=>{n.on(e,n.params.on[e])}),n.params&&n.params.onAny&&n.onAny(n.params.onAny),Object.assign(n,{enabled:n.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===n.params.direction,isVertical:()=>"vertical"===n.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:n.params.allowSlideNext,allowSlidePrev:n.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:n.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:n.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),n.emit("_swiper"),n.params.init&&n.init(),n}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:i}=this,s=E(b(t,`.${i.slideClass}, swiper-slide`)[0]);return E(e)-s}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=b(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=(this.maxTranslate()-i)*e+i;this.translateTo(s,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:i,slides:s,slidesGrid:r,slidesSizesGrid:a,size:n,activeIndex:l}=this,o=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let e,t=s[l]?Math.ceil(s[l].swiperSlideSize):0;for(let i=l+1;i<s.length;i+=1)s[i]&&!e&&(t+=Math.ceil(s[i].swiperSlideSize),o+=1,t>n&&(e=!0));for(let i=l-1;i>=0;i-=1)s[i]&&!e&&(t+=s[i].swiperSlideSize,o+=1,t>n&&(e=!0))}else if("current"===e)for(let e=l+1;e<s.length;e+=1)(t?r[e]+a[e]-r[l]<n:r[e]-r[l]<n)&&(o+=1);else for(let e=l-1;e>=0;e-=1)r[l]-r[e]<n&&(o+=1);return o}update(){let e;let t=this;if(!t||t.destroyed)return;let{snapGrid:i,params:s}=t;function r(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(s.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&I(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),s.freeMode&&s.freeMode.enabled&&!s.cssMode)r(),s.autoHeight&&t.updateAutoHeight();else{if(("auto"===s.slidesPerView||s.slidesPerView>1)&&t.isEnd&&!s.centeredSlides){let i=t.virtual&&s.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(i.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||r()}s.watchOverflow&&i!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${i}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let i=e||t.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,r=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(s()):b(i,s())[0];return!r&&t.params.createElements&&(r=x("div",t.params.wrapperClass),i.append(r),b(i,`.${t.params.slideClass}`).forEach(e=>{r.append(e)})),Object.assign(t,{el:i,wrapperEl:r,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:r,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===S(i,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===S(i,"direction")),wrongRTL:"-webkit-box"===S(r,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();let i=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&i.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(e=>{e.complete?I(t,e):e.addEventListener("load",e=>{I(t,e.target)})}),z(t),t.initialized=!0,z(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let i=this,{params:s,el:r,wrapperEl:a,slides:n}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),a&&a.removeAttribute("style"),n&&n.length&&n.forEach(e=>{e.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.el&&"string"!=typeof i.el&&(i.el.swiper=null),function(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}(i)),i.destroyed=!0),null}static extendDefaults(e){v(J,e)}static get extendedDefaults(){return J}static get defaults(){return K}static installModule(e){Z.prototype.__modules__||(Z.prototype.__modules__=[]);let t=Z.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>Z.installModule(e)):Z.installModule(e),Z}}Object.keys(U).forEach(e=>{Object.keys(U[e]).forEach(t=>{Z.prototype[t]=U[e][t]})}),Z.use([function(e){let{swiper:t,on:i,emit:s}=e,r=h(),a=null,n=null,l=()=>{t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},o=()=>{t&&!t.destroyed&&t.initialized&&(a=new ResizeObserver(e=>{n=r.requestAnimationFrame(()=>{let{width:i,height:s}=t,r=i,a=s;e.forEach(e=>{let{contentBoxSize:i,contentRect:s,target:n}=e;n&&n!==t.el||(r=s?s.width:(i[0]||i).inlineSize,a=s?s.height:(i[0]||i).blockSize)}),(r!==i||a!==s)&&l()})})).observe(t.el)},d=()=>{n&&r.cancelAnimationFrame(n),a&&a.unobserve&&t.el&&(a.unobserve(t.el),a=null)},c=()=>{t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",()=>{if(t.params.resizeObserver&&void 0!==r.ResizeObserver){o();return}r.addEventListener("resize",l),r.addEventListener("orientationchange",c)}),i("destroy",()=>{d(),r.removeEventListener("resize",l),r.removeEventListener("orientationchange",c)})},function(e){let{swiper:t,extendParams:i,on:s,emit:r}=e,a=[],n=h(),l=function(e,i){void 0===i&&(i={});let s=new(n.MutationObserver||n.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length){r("observerUpdate",e[0]);return}let i=function(){r("observerUpdate",e[0])};n.requestAnimationFrame?n.requestAnimationFrame(i):n.setTimeout(i,0)});s.observe(e,{attributes:void 0===i.attributes||i.attributes,childList:t.isElement||(void 0===i.childList||i).childList,characterData:void 0===i.characterData||i.characterData}),a.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=T(t.hostEl);for(let t=0;t<e.length;t+=1)l(e[t])}l(t.hostEl,{childList:t.params.observeSlideChildren}),l(t.wrapperEl,{attributes:!1})}}),s("destroy",()=>{a.forEach(e=>{e.disconnect()}),a.splice(0,a.length)})}]);let Q=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function ee(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function et(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:ee(t[i])&&ee(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:et(e[i],t[i]):e[i]=t[i]})}function ei(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function es(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function er(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function ea(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}let en=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function el(){return(el=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}function eo(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function ed(e,t){return"undefined"==typeof window?(0,l.useEffect)(e,t):(0,l.useLayoutEffect)(e,t)}let ec=(0,l.createContext)(null),ep=(0,l.createContext)(null),eu=(0,l.forwardRef)(function(e,t){var i;let{className:s,tag:r="div",wrapperTag:a="div",children:n,onSwiper:o,...d}=void 0===e?{}:e,c=!1,[p,u]=(0,l.useState)("swiper"),[h,m]=(0,l.useState)(null),[f,g]=(0,l.useState)(!1),v=(0,l.useRef)(!1),_=(0,l.useRef)(null),w=(0,l.useRef)(null),b=(0,l.useRef)(null),y=(0,l.useRef)(null),x=(0,l.useRef)(null),S=(0,l.useRef)(null),E=(0,l.useRef)(null),T=(0,l.useRef)(null),{params:C,passedParams:P,rest:k,events:j}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let i={on:{}},s={},r={};et(i,K),i._emitClasses=!0,i.init=!1;let a={},n=Q.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(l=>{void 0!==e[l]&&(n.indexOf(l)>=0?ee(e[l])?(i[l]={},r[l]={},et(i[l],e[l]),et(r[l],e[l])):(i[l]=e[l],r[l]=e[l]):0===l.search(/on[A-Z]/)&&"function"==typeof e[l]?t?s[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:i.on[`${l[2].toLowerCase()}${l.substr(3)}`]=e[l]:a[l]=e[l])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:r,rest:a,events:s}}(d),{slides:L,slots:O}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return l.Children.toArray(e).forEach(e=>{if(eo(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let s=function e(t){let i=[];return l.Children.toArray(t).forEach(t=>{eo(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);s.length>0?s.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(n),A=()=>{g(!f)};Object.assign(C.on,{_containerClasses(e,t){u(t)}});let I=()=>{Object.assign(C.on,j),c=!0;let e={...C};if(delete e.wrapperClass,w.current=new Z(e),w.current.virtual&&w.current.params.virtual.enabled){w.current.virtual.slides=L;let e={cache:!1,slides:L,renderExternal:m,renderExternalUpdate:!1};et(w.current.params.virtual,e),et(w.current.originalParams.virtual,e)}};_.current||I(),w.current&&w.current.on("_beforeBreakpoint",A);let D=()=>{!c&&j&&w.current&&Object.keys(j).forEach(e=>{w.current.on(e,j[e])})},z=()=>{j&&w.current&&Object.keys(j).forEach(e=>{w.current.off(e,j[e])})};return(0,l.useEffect)(()=>()=>{w.current&&w.current.off("_beforeBreakpoint",A)}),(0,l.useEffect)(()=>{!v.current&&w.current&&(w.current.emitSlidesClasses(),v.current=!0)}),ed(()=>{if(t&&(t.current=_.current),_.current)return w.current.destroyed&&I(),function(e,t){let{el:i,nextEl:s,prevEl:r,paginationEl:a,scrollbarEl:n,swiper:l}=e;ei(t)&&s&&r&&(l.params.navigation.nextEl=s,l.originalParams.navigation.nextEl=s,l.params.navigation.prevEl=r,l.originalParams.navigation.prevEl=r),es(t)&&a&&(l.params.pagination.el=a,l.originalParams.pagination.el=a),er(t)&&n&&(l.params.scrollbar.el=n,l.originalParams.scrollbar.el=n),l.init(i)}({el:_.current,nextEl:x.current,prevEl:S.current,paginationEl:E.current,scrollbarEl:T.current,swiper:w.current},C),o&&!w.current.destroyed&&o(w.current),()=>{w.current&&!w.current.destroyed&&w.current.destroy(!0,!1)}},[]),ed(()=>{D();let e=function(e,t,i,s,r){let a=[];if(!t)return a;let n=e=>{0>a.indexOf(e)&&a.push(e)};if(i&&s){let e=s.map(r),t=i.map(r);e.join("")!==t.join("")&&n("children"),s.length!==i.length&&n("children")}return Q.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(i=>{if(i in e&&i in t){if(ee(e[i])&&ee(t[i])){let s=Object.keys(e[i]),r=Object.keys(t[i]);s.length!==r.length?n(i):(s.forEach(s=>{e[i][s]!==t[i][s]&&n(i)}),r.forEach(s=>{e[i][s]!==t[i][s]&&n(i)}))}else e[i]!==t[i]&&n(i)}}),a}(P,b.current,L,y.current,e=>e.key);return b.current=P,y.current=L,e.length&&w.current&&!w.current.destroyed&&function(e){let t,i,s,r,a,n,l,o,{swiper:d,slides:c,passedParams:p,changedParams:u,nextEl:h,prevEl:m,scrollbarEl:f,paginationEl:g}=e,v=u.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:_,pagination:w,navigation:b,scrollbar:y,virtual:x,thumbs:S}=d;u.includes("thumbs")&&p.thumbs&&p.thumbs.swiper&&!p.thumbs.swiper.destroyed&&_.thumbs&&(!_.thumbs.swiper||_.thumbs.swiper.destroyed)&&(t=!0),u.includes("controller")&&p.controller&&p.controller.control&&_.controller&&!_.controller.control&&(i=!0),u.includes("pagination")&&p.pagination&&(p.pagination.el||g)&&(_.pagination||!1===_.pagination)&&w&&!w.el&&(s=!0),u.includes("scrollbar")&&p.scrollbar&&(p.scrollbar.el||f)&&(_.scrollbar||!1===_.scrollbar)&&y&&!y.el&&(r=!0),u.includes("navigation")&&p.navigation&&(p.navigation.prevEl||m)&&(p.navigation.nextEl||h)&&(_.navigation||!1===_.navigation)&&b&&!b.prevEl&&!b.nextEl&&(a=!0);let E=e=>{d[e]&&(d[e].destroy(),"navigation"===e?(d.isElement&&(d[e].prevEl.remove(),d[e].nextEl.remove()),_[e].prevEl=void 0,_[e].nextEl=void 0,d[e].prevEl=void 0,d[e].nextEl=void 0):(d.isElement&&d[e].el.remove(),_[e].el=void 0,d[e].el=void 0))};u.includes("loop")&&d.isElement&&(_.loop&&!p.loop?n=!0:!_.loop&&p.loop?l=!0:o=!0),v.forEach(e=>{if(ee(_[e])&&ee(p[e]))Object.assign(_[e],p[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in p[e]&&!p[e].enabled&&E(e);else{let t=p[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&E(e):_[e]=p[e]}}),v.includes("controller")&&!i&&d.controller&&d.controller.control&&_.controller&&_.controller.control&&(d.controller.control=_.controller.control),u.includes("children")&&c&&x&&_.virtual.enabled?(x.slides=c,x.update(!0)):u.includes("virtual")&&x&&_.virtual.enabled&&(c&&(x.slides=c),x.update(!0)),u.includes("children")&&c&&_.loop&&(o=!0),t&&S.init()&&S.update(!0),i&&(d.controller.control=_.controller.control),s&&(d.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-pagination"),g.part.add("pagination"),d.el.appendChild(g)),g&&(_.pagination.el=g),w.init(),w.render(),w.update()),r&&(d.isElement&&(!f||"string"==typeof f)&&((f=document.createElement("div")).classList.add("swiper-scrollbar"),f.part.add("scrollbar"),d.el.appendChild(f)),f&&(_.scrollbar.el=f),y.init(),y.updateSize(),y.setTranslate()),a&&(d.isElement&&(h&&"string"!=typeof h||((h=document.createElement("div")).classList.add("swiper-button-next"),M(h,d.hostEl.constructor.nextButtonSvg),h.part.add("button-next"),d.el.appendChild(h)),m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-prev"),M(m,d.hostEl.constructor.prevButtonSvg),m.part.add("button-prev"),d.el.appendChild(m))),h&&(_.navigation.nextEl=h),m&&(_.navigation.prevEl=m),b.init(),b.update()),u.includes("allowSlideNext")&&(d.allowSlideNext=p.allowSlideNext),u.includes("allowSlidePrev")&&(d.allowSlidePrev=p.allowSlidePrev),u.includes("direction")&&d.changeDirection(p.direction,!1),(n||o)&&d.loopDestroy(),(l||o)&&d.loopCreate(),d.update()}({swiper:w.current,slides:L,passedParams:P,changedParams:e,nextEl:x.current,prevEl:S.current,scrollbarEl:T.current,paginationEl:E.current}),()=>{z()}}),ed(()=>{en(w.current)},[h]),l.createElement(r,el({ref:_,className:ea(`${p}${s?` ${s}`:""}`)},k),l.createElement(ep.Provider,{value:w.current},O["container-start"],l.createElement(a,{className:(void 0===(i=C.wrapperClass)&&(i=""),i)?i.includes("swiper-wrapper")?i:`swiper-wrapper ${i}`:"swiper-wrapper"},O["wrapper-start"],C.virtual?function(e,t,i){if(!i)return null;let s=e=>{let i=e;return e<0?i=t.length+e:i>=t.length&&(i-=t.length),i},r=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`},{from:a,to:n}=i,o=e.params.loop?-t.length:0,d=e.params.loop?2*t.length:t.length,c=[];for(let e=o;e<d;e+=1)e>=a&&e<=n&&c.push(t[s(e)]);return c.map((t,i)=>l.cloneElement(t,{swiper:e,style:r,key:t.props.virtualIndex||t.key||`slide-${i}`}))}(w.current,L,h):L.map((e,t)=>l.cloneElement(e,{swiper:w.current,swiperSlideIndex:t})),O["wrapper-end"]),ei(C)&&l.createElement(l.Fragment,null,l.createElement("div",{ref:S,className:"swiper-button-prev"}),l.createElement("div",{ref:x,className:"swiper-button-next"})),er(C)&&l.createElement("div",{ref:T,className:"swiper-scrollbar"}),es(C)&&l.createElement("div",{ref:E,className:"swiper-pagination"}),O["container-end"]))});eu.displayName="Swiper";let eh=(0,l.forwardRef)(function(e,t){let{tag:i="div",children:s,className:r="",swiper:a,zoom:n,lazy:o,virtualIndex:d,swiperSlideIndex:c,...p}=void 0===e?{}:e,u=(0,l.useRef)(null),[h,m]=(0,l.useState)("swiper-slide"),[f,g]=(0,l.useState)(!1);function v(e,t,i){t===u.current&&m(i)}ed(()=>{if(void 0!==c&&(u.current.swiperSlideIndex=c),t&&(t.current=u.current),u.current&&a){if(a.destroyed){"swiper-slide"!==h&&m("swiper-slide");return}return a.on("_slideClass",v),()=>{a&&a.off("_slideClass",v)}}}),ed(()=>{a&&u.current&&!a.destroyed&&m(a.getSlideClasses(u.current))},[a]);let _={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},w=()=>"function"==typeof s?s(_):s;return l.createElement(i,el({ref:u,className:ea(`${h}${r?` ${r}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{g(!0)}},p),n&&l.createElement(ec.Provider,{value:_},l.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof n?n:void 0},w(),o&&!f&&l.createElement("div",{className:"swiper-lazy-preloader"}))),!n&&l.createElement(ec.Provider,{value:_},w(),o&&!f&&l.createElement("div",{className:"swiper-lazy-preloader"})))});function em(e,t,i,s){return e.params.createElements&&Object.keys(s).forEach(r=>{if(!i[r]&&!0===i.auto){let a=b(e.el,`.${s[r]}`)[0];a||((a=x("div",s[r])).className=s[r],e.el.append(a)),i[r]=a,t[r]=a}}),i}function ef(e){let{swiper:t,extendParams:i,on:s,emit:r}=e;function a(e){let i;return e&&"string"==typeof e&&t.isElement&&(i=t.el.querySelector(e)||t.hostEl.querySelector(e))?i:(e&&("string"==typeof e&&(i=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&i&&i.length>1&&1===t.el.querySelectorAll(e).length?i=t.el.querySelector(e):i&&1===i.length&&(i=i[0])),e&&!i)?e:i}function n(e,i){let s=t.params.navigation;(e=P(e)).forEach(e=>{e&&(e.classList[i?"add":"remove"](...s.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=i),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](s.lockClass))})}function l(){let{nextEl:e,prevEl:i}=t.navigation;if(t.params.loop){n(i,!1),n(e,!1);return}n(i,t.isBeginning&&!t.params.rewind),n(e,t.isEnd&&!t.params.rewind)}function o(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),r("navigationPrev"))}function d(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),r("navigationNext"))}function c(){let e=t.params.navigation;if(t.params.navigation=em(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let i=a(e.nextEl),s=a(e.prevEl);Object.assign(t.navigation,{nextEl:i,prevEl:s}),i=P(i),s=P(s);let r=(i,s)=>{i&&i.addEventListener("click","next"===s?d:o),!t.enabled&&i&&i.classList.add(...e.lockClass.split(" "))};i.forEach(e=>r(e,"next")),s.forEach(e=>r(e,"prev"))}function p(){let{nextEl:e,prevEl:i}=t.navigation;e=P(e),i=P(i);let s=(e,i)=>{e.removeEventListener("click","next"===i?d:o),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>s(e,"next")),i.forEach(e=>s(e,"prev"))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},s("init",()=>{!1===t.params.navigation.enabled?u():(c(),l())}),s("toEdge fromEdge lock unlock",()=>{l()}),s("destroy",()=>{p()}),s("enable disable",()=>{let{nextEl:e,prevEl:i}=t.navigation;if(e=P(e),i=P(i),t.enabled){l();return}[...e,...i].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),s("click",(e,i)=>{let{nextEl:s,prevEl:a}=t.navigation;s=P(s),a=P(a);let n=i.target,l=a.includes(n)||s.includes(n);if(t.isElement&&!l){let e=i.path||i.composedPath&&i.composedPath();e&&(l=e.find(e=>s.includes(e)||a.includes(e)))}if(t.params.navigation.hideOnClick&&!l){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===n||t.pagination.el.contains(n)))return;s.length?e=s[0].classList.contains(t.params.navigation.hiddenClass):a.length&&(e=a[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?r("navigationShow"):r("navigationHide"),[...s,...a].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let u=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),l()},disable:u,update:l,init:c,destroy:p})}function eg(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function ev(e){let t,{swiper:i,extendParams:s,on:r,emit:a}=e,n="swiper-pagination";s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${n}-bullet`,bulletActiveClass:`${n}-bullet-active`,modifierClass:`${n}-`,currentClass:`${n}-current`,totalClass:`${n}-total`,hiddenClass:`${n}-hidden`,progressbarFillClass:`${n}-progressbar-fill`,progressbarOppositeClass:`${n}-progressbar-opposite`,clickableClass:`${n}-clickable`,lockClass:`${n}-lock`,horizontalClass:`${n}-horizontal`,verticalClass:`${n}-vertical`,paginationDisabledClass:`${n}-disabled`}}),i.pagination={el:null,bullets:[]};let l=0;function o(){return!i.params.pagination.el||!i.pagination.el||Array.isArray(i.pagination.el)&&0===i.pagination.el.length}function d(e,t){let{bulletActiveClass:s}=i.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${s}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${s}-${t}-${t}`))}function c(e){let t=e.target.closest(eg(i.params.pagination.bulletClass));if(!t)return;e.preventDefault();let s=E(t)*i.params.slidesPerGroup;if(i.params.loop){var r,a,n;if(i.realIndex===s)return;let e=(r=i.realIndex,a=s,(r%=n=i.slides.length,(a%=n)===r+1)?"next":a===r-1?"previous":void 0);"next"===e?i.slideNext():"previous"===e?i.slidePrev():i.slideToLoop(s)}else i.slideTo(s)}function p(){let e,s;let r=i.rtl,n=i.params.pagination;if(o())return;let c=i.pagination.el;c=P(c);let p=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,u=i.params.loop?Math.ceil(p/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?(s=i.previousRealIndex||0,e=i.params.slidesPerGroup>1?Math.floor(i.realIndex/i.params.slidesPerGroup):i.realIndex):void 0!==i.snapIndex?(e=i.snapIndex,s=i.previousSnapIndex):(s=i.previousIndex||0,e=i.activeIndex||0),"bullets"===n.type&&i.pagination.bullets&&i.pagination.bullets.length>0){let a,o,p;let u=i.pagination.bullets;if(n.dynamicBullets&&(t=C(u[0],i.isHorizontal()?"width":"height",!0),c.forEach(e=>{e.style[i.isHorizontal()?"width":"height"]=`${t*(n.dynamicMainBullets+4)}px`}),n.dynamicMainBullets>1&&void 0!==s&&((l+=e-(s||0))>n.dynamicMainBullets-1?l=n.dynamicMainBullets-1:l<0&&(l=0)),p=((o=(a=Math.max(e-l,0))+(Math.min(u.length,n.dynamicMainBullets)-1))+a)/2),u.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${n.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),c.length>1)u.forEach(t=>{let s=E(t);s===e?t.classList.add(...n.bulletActiveClass.split(" ")):i.isElement&&t.setAttribute("part","bullet"),n.dynamicBullets&&(s>=a&&s<=o&&t.classList.add(...`${n.bulletActiveClass}-main`.split(" ")),s===a&&d(t,"prev"),s===o&&d(t,"next"))});else{let t=u[e];if(t&&t.classList.add(...n.bulletActiveClass.split(" ")),i.isElement&&u.forEach((t,i)=>{t.setAttribute("part",i===e?"bullet-active":"bullet")}),n.dynamicBullets){let e=u[a],t=u[o];for(let e=a;e<=o;e+=1)u[e]&&u[e].classList.add(...`${n.bulletActiveClass}-main`.split(" "));d(e,"prev"),d(t,"next")}}if(n.dynamicBullets){let e=Math.min(u.length,n.dynamicMainBullets+4),s=(t*e-t)/2-p*t,a=r?"right":"left";u.forEach(e=>{e.style[i.isHorizontal()?a:"top"]=`${s}px`})}}c.forEach((t,s)=>{if("fraction"===n.type&&(t.querySelectorAll(eg(n.currentClass)).forEach(t=>{t.textContent=n.formatFractionCurrent(e+1)}),t.querySelectorAll(eg(n.totalClass)).forEach(e=>{e.textContent=n.formatFractionTotal(u)})),"progressbar"===n.type){let s;s=n.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";let r=(e+1)/u,a=1,l=1;"horizontal"===s?a=r:l=r,t.querySelectorAll(eg(n.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${a}) scaleY(${l})`,e.style.transitionDuration=`${i.params.speed}ms`})}"custom"===n.type&&n.renderCustom?(M(t,n.renderCustom(i,e+1,u)),0===s&&a("paginationRender",t)):(0===s&&a("paginationRender",t),a("paginationUpdate",t)),i.params.watchOverflow&&i.enabled&&t.classList[i.isLocked?"add":"remove"](n.lockClass)})}function u(){let e=i.params.pagination;if(o())return;let t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.grid&&i.params.grid.rows>1?i.slides.length/Math.ceil(i.params.grid.rows):i.slides.length,s=i.pagination.el;s=P(s);let r="";if("bullets"===e.type){let s=i.params.loop?Math.ceil(t/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&s>t&&(s=t);for(let t=0;t<s;t+=1)e.renderBullet?r+=e.renderBullet.call(i,t,e.bulletClass):r+=`<${e.bulletElement} ${i.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(r=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(r=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),i.pagination.bullets=[],s.forEach(t=>{"custom"!==e.type&&M(t,r||""),"bullets"===e.type&&i.pagination.bullets.push(...t.querySelectorAll(eg(e.bulletClass)))}),"custom"!==e.type&&a("paginationRender",s[0])}function h(){let e;i.params.pagination=em(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});let t=i.params.pagination;t.el&&("string"==typeof t.el&&i.isElement&&(e=i.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(i.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...i.el.querySelectorAll(t.el)]).length>1&&(e=e.find(e=>T(e,".swiper")[0]===i.el)),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(i.pagination,{el:e}),(e=P(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(i.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),l=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",c),i.enabled||e.classList.add(t.lockClass)})))}function m(){let e=i.params.pagination;if(o())return;let t=i.pagination.el;t&&(t=P(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(i.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",c))}),i.pagination.bullets&&i.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}r("changeDirection",()=>{if(!i.pagination||!i.pagination.el)return;let e=i.params.pagination,{el:t}=i.pagination;(t=P(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(i.isHorizontal()?e.horizontalClass:e.verticalClass)})}),r("init",()=>{!1===i.params.pagination.enabled?f():(h(),u(),p())}),r("activeIndexChange",()=>{void 0===i.snapIndex&&p()}),r("snapIndexChange",()=>{p()}),r("snapGridLengthChange",()=>{u(),p()}),r("destroy",()=>{m()}),r("enable disable",()=>{let{el:e}=i.pagination;e&&(e=P(e)).forEach(e=>e.classList[i.enabled?"remove":"add"](i.params.pagination.lockClass))}),r("lock unlock",()=>{p()}),r("click",(e,t)=>{let s=t.target,r=P(i.pagination.el);if(i.params.pagination.el&&i.params.pagination.hideOnClick&&r&&r.length>0&&!s.classList.contains(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&s===i.navigation.nextEl||i.navigation.prevEl&&s===i.navigation.prevEl))return;!0===r[0].classList.contains(i.params.pagination.hiddenClass)?a("paginationShow"):a("paginationHide"),r.forEach(e=>e.classList.toggle(i.params.pagination.hiddenClass))}});let f=()=>{i.el.classList.add(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=P(e)).forEach(e=>e.classList.add(i.params.pagination.paginationDisabledClass)),m()};Object.assign(i.pagination,{enable:()=>{i.el.classList.remove(i.params.pagination.paginationDisabledClass);let{el:e}=i.pagination;e&&(e=P(e)).forEach(e=>e.classList.remove(i.params.pagination.paginationDisabledClass)),h(),u(),p()},disable:f,render:u,update:p,init:h,destroy:m})}function e_(e){let t,i,s,r,a,n,l,o,d,c,{swiper:u,extendParams:h,on:m,emit:f,params:g}=e;u.autoplay={running:!1,paused:!1,timeLeft:0},h({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let v=g&&g.autoplay?g.autoplay.delay:3e3,_=g&&g.autoplay?g.autoplay.delay:3e3,w=new Date().getTime();function b(e){u&&!u.destroyed&&u.wrapperEl&&e.target===u.wrapperEl&&(u.wrapperEl.removeEventListener("transitionend",b),!c&&(!e.detail||!e.detail.bySwiperTouchMove)&&P())}let y=()=>{if(u.destroyed||!u.autoplay.running)return;u.autoplay.paused?r=!0:r&&(_=s,r=!1);let e=u.autoplay.paused?s:w+_-new Date().getTime();u.autoplay.timeLeft=e,f("autoplayTimeLeft",e,e/v),i=requestAnimationFrame(()=>{y()})},x=()=>{let e;if(e=u.virtual&&u.params.virtual.enabled?u.slides.find(e=>e.classList.contains("swiper-slide-active")):u.slides[u.activeIndex])return parseInt(e.getAttribute("data-swiper-autoplay"),10)},S=e=>{if(u.destroyed||!u.autoplay.running)return;cancelAnimationFrame(i),y();let r=void 0===e?u.params.autoplay.delay:e;v=u.params.autoplay.delay,_=u.params.autoplay.delay;let a=x();!Number.isNaN(a)&&a>0&&void 0===e&&(r=a,v=a,_=a),s=r;let n=u.params.speed,l=()=>{u&&!u.destroyed&&(u.params.autoplay.reverseDirection?!u.isBeginning||u.params.loop||u.params.rewind?(u.slidePrev(n,!0,!0),f("autoplay")):u.params.autoplay.stopOnLastSlide||(u.slideTo(u.slides.length-1,n,!0,!0),f("autoplay")):!u.isEnd||u.params.loop||u.params.rewind?(u.slideNext(n,!0,!0),f("autoplay")):u.params.autoplay.stopOnLastSlide||(u.slideTo(0,n,!0,!0),f("autoplay")),u.params.cssMode&&(w=new Date().getTime(),requestAnimationFrame(()=>{S()})))};return r>0?(clearTimeout(t),t=setTimeout(()=>{l()},r)):requestAnimationFrame(()=>{l()}),r},E=()=>{w=new Date().getTime(),u.autoplay.running=!0,S(),f("autoplayStart")},T=()=>{u.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(i),f("autoplayStop")},C=(e,i)=>{if(u.destroyed||!u.autoplay.running)return;clearTimeout(t),e||(d=!0);let r=()=>{f("autoplayPause"),u.params.autoplay.waitForTransition?u.wrapperEl.addEventListener("transitionend",b):P()};if(u.autoplay.paused=!0,i){o&&(s=u.params.autoplay.delay),o=!1,r();return}s=(s||u.params.autoplay.delay)-(new Date().getTime()-w),u.isEnd&&s<0&&!u.params.loop||(s<0&&(s=0),r())},P=()=>{u.isEnd&&s<0&&!u.params.loop||u.destroyed||!u.autoplay.running||(w=new Date().getTime(),d?(d=!1,S(s)):S(),u.autoplay.paused=!1,f("autoplayResume"))},M=()=>{if(u.destroyed||!u.autoplay.running)return;let e=p();"hidden"===e.visibilityState&&(d=!0,C(!0)),"visible"===e.visibilityState&&P()},k=e=>{"mouse"===e.pointerType&&(d=!0,c=!0,u.animating||u.autoplay.paused||C(!0))},j=e=>{"mouse"===e.pointerType&&(c=!1,u.autoplay.paused&&P())},L=()=>{u.params.autoplay.pauseOnMouseEnter&&(u.el.addEventListener("pointerenter",k),u.el.addEventListener("pointerleave",j))},O=()=>{u.el&&"string"!=typeof u.el&&(u.el.removeEventListener("pointerenter",k),u.el.removeEventListener("pointerleave",j))},A=()=>{p().addEventListener("visibilitychange",M)},I=()=>{p().removeEventListener("visibilitychange",M)};m("init",()=>{u.params.autoplay.enabled&&(L(),A(),E())}),m("destroy",()=>{O(),I(),u.autoplay.running&&T()}),m("_freeModeStaticRelease",()=>{(n||d)&&P()}),m("_freeModeNoMomentumRelease",()=>{u.params.autoplay.disableOnInteraction?T():C(!0,!0)}),m("beforeTransitionStart",(e,t,i)=>{!u.destroyed&&u.autoplay.running&&(i||!u.params.autoplay.disableOnInteraction?C(!0,!0):T())}),m("sliderFirstMove",()=>{if(!u.destroyed&&u.autoplay.running){if(u.params.autoplay.disableOnInteraction){T();return}a=!0,n=!1,d=!1,l=setTimeout(()=>{d=!0,n=!0,C(!0)},200)}}),m("touchEnd",()=>{if(!u.destroyed&&u.autoplay.running&&a){if(clearTimeout(l),clearTimeout(t),u.params.autoplay.disableOnInteraction){n=!1,a=!1;return}n&&u.params.cssMode&&P(),n=!1,a=!1}}),m("slideChange",()=>{!u.destroyed&&u.autoplay.running&&(o=!0)}),Object.assign(u.autoplay,{start:E,stop:T,pause:C,resume:P})}eh.displayName="SwiperSlide";var ew=i(9410),eb=i(6506),ey=i(2716);i(3754),i(2119),i(3141);var ex=i(5620),eS=i.n(ex);function eE(){let e=(0,ey.eV)(),t=["zh"===e?"/home/<USER>":"/home/<USER>","zh"===e?"/home/<USER>":"/home/<USER>"],i=["/product/c31","/product/t1pro"];return(0,n.jsxs)("div",{className:eS()["banner-slider"],children:[n.jsx(eu,{modules:[ef,ev,e_],spaceBetween:0,slidesPerView:1,navigation:{nextEl:`.${eS()["banner-slider__button-next"]}`,prevEl:`.${eS()["banner-slider__button-prev"]}`},pagination:{el:`.${eS()["banner-slider__pagination"]}`,clickable:!0,bulletClass:eS()["banner-slider__bullet"],bulletActiveClass:eS()["banner-slider__bullet--active"]},autoplay:{delay:5e3,disableOnInteraction:!1,pauseOnMouseEnter:!0},loop:t.length>1,className:eS()["banner-slider__swiper"],children:t.map((e,t)=>n.jsx(eh,{children:n.jsx("div",{className:eS()["banner-slider__slide"],children:n.jsx(eb.default,{style:{height:"100%",width:"100%"},href:i[t],children:n.jsx(ew.default,{src:e,width:1920,height:500,alt:`Banner ${t+1}`,unoptimized:!0,style:{width:"100%",height:"100%",objectFit:"cover",objectPosition:"center"}})})})},t))}),t.length>1&&(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:`${eS()["banner-slider__button-prev"]} ${eS()["banner-slider__button"]}`,children:n.jsx(ew.default,{src:"/slider-left.svg",width:44,height:44,alt:"Previous"})}),n.jsx("div",{className:`${eS()["banner-slider__button-next"]} ${eS()["banner-slider__button"]}`,children:n.jsx(ew.default,{src:"/slider-right.svg",width:44,height:44,alt:"Next"})}),n.jsx("div",{className:eS()["banner-slider__pagination"]})]})]})}},8748:e=>{e.exports={"nav--scrolled":"home_nav--scrolled__f5oaX",nav:"home_nav__gr65i",nav__placeholder:"home_nav__placeholder__R_bDj",nav__content:"home_nav__content__gXoig",nav__list:"home_nav__list__dmRBz",nav__list__item:"home_nav__list__item__Ti9E4","nav__list__item--link":"home_nav__list__item--link__dx88I","nav__list__item--active":"home_nav__list__item--active__oPRJX",nav__right:"home_nav__right__4GRXj",nav__right__language:"home_nav__right__language__YzU4O",nav__right__language__text:"home_nav__right__language__text__yUNmB","nav__right__language__text--active":"home_nav__right__language__text--active__e4h1y",nav__right__search:"home_nav__right__search__QAvd_",nav__right__menu:"home_nav__right__menu__tMG4s",nav__drop:"home_nav__drop__RNd3y",nav__mask:"home_nav__mask__YVj5E","banner-slider":"home_banner-slider__UBj9I","banner-slider__swiper":"home_banner-slider__swiper__9Bl8q","banner-slider__slide":"home_banner-slider__slide__2U7Uu","banner-slider__button":"home_banner-slider__button__GKjGy","swiper-button-disabled":"home_swiper-button-disabled__siaDk","banner-slider__button-prev":"home_banner-slider__button-prev__VeikB","banner-slider__button-next":"home_banner-slider__button-next__UC5d2","banner-slider__pagination":"home_banner-slider__pagination__J58et","banner-slider__bullet":"home_banner-slider__bullet__c3a9X","banner-slider__bullet--active":"home_banner-slider__bullet--active__5BpSZ","banner-slider__switcher":"home_banner-slider__switcher__SoaxS","banner-slider__switcher--right":"home_banner-slider__switcher--right___84yN","banner-slider__indicator":"home_banner-slider__indicator__0OOU4","banner-slider__indicator__item":"home_banner-slider__indicator__item__f8vBh","hot-spot":"home_hot-spot__HmXBc","hot-spot__captain":"home_hot-spot__captain__P7sAg","hot-spot__captain__more":"home_hot-spot__captain__more__hoe30","hot-spot__news":"home_hot-spot__news__mFPbX","hot-spot__news__left":"home_hot-spot__news__left__bYNbF","hot-spot__news__right":"home_hot-spot__news__right__IYxxG","hot-spot__news__item":"home_hot-spot__news__item__i6svw","hot-spot__news__item__info":"home_hot-spot__news__item__info__GSDkz","hot-spot__news__item__image":"home_hot-spot__news__item__image__0Dj0A","hot-spot__news__item__image--right":"home_hot-spot__news__item__image--right__scey9","hot-spot__news__item--left":"home_hot-spot__news__item--left__W7YL9",about:"home_about__vPbFi",about__cover:"home_about__cover__SPvuD",about__content:"home_about__content__EA9EW",about__content__time:"home_about__content__time__HcHq6",about__content__time__item:"home_about__content__time__item__n4W8C","about__content__time--page":"home_about__content__time--page__Azkeq",about__content__prides:"home_about__content__prides__zHCpT",contacts:"home_contacts__TRH4N","contacts--page":"home_contacts--page__0BV0w",contacts__info:"home_contacts__info__pIGy0",contacts__info__items:"home_contacts__info__items__qUSi9",contacts__info__title:"home_contacts__info__title__3_UHT",contacts__info__item:"home_contacts__info__item__eDIm0",contacts__address:"home_contacts__address___ZQdr",footer:"home_footer__qefFZ",footer__logo:"home_footer__logo__jG71u",footer__links:"home_footer__links__q5uiZ",footer__links__item:"home_footer__links__item__gB0TO",footer__links__follow:"home_footer__links__follow__jv8nP",footer__links__follow__weixin:"home_footer__links__follow__weixin__yeCNp",footer__copyright:"home_footer__copyright__M6lua",footer__copyright__link:"home_footer__copyright__link__PBT0B","banner-slider__indicator__item--active":"home_banner-slider__indicator__item--active__Pkcak"}},9845:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>C,generateMetadata:()=>T});var s=i(5036),r=i(6843);let a=(0,r.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\banner-slider.tsx`),{__esModule:n,$$typeof:l}=a,o=a.default;i(9352);var d=i(8748),c=i.n(d),p=i(6274),u=i(2813),h=i(68);function m({text:e,link:t}){return s.jsx(p.default,{href:t,className:"hide-on-medium hide-on-large",children:s.jsx("button",{className:"show-more",children:e})})}var f=i(1782),g=i(6904);async function v(){let e=await (0,g.nI)();return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:c()["hot-spot"],children:[s.jsx(_,{title:e("productCenter"),link:"/product"}),s.jsx(w,{})]}),(0,s.jsxs)("div",{className:c()["hot-spot"],children:[s.jsx(_,{title:e("prodoctVideos")}),s.jsx(b,{})]})]})}async function _({title:e,link:t}){let i=await (0,g.nI)();return(0,s.jsxs)("div",{className:c()["hot-spot__captain"],children:[s.jsx("h2",{children:e}),t&&(0,s.jsxs)(p.default,{href:t,className:`${c()["hot-spot__captain__more"]} hide-on-small`,children:[i("seeMore"),s.jsx(u.default,{src:"/arrow-right.svg",width:10,height:17,alt:"arrow"})]})]})}async function w(){let e=await (0,g.BH)(),t={infos:(await (0,f.Pb)()).filter((e,t)=>["c31","02","04","t1pro"].includes(e.id)).map(t=>({imageSrc:t.imageSrc,title:"zh"===e?t.name:t.nameEn,tip:"zh"===e?t.description:t.descriptionEn,link:"c31"===t.id?"/product/c31":"t1pro"===t.id?"/product/t1pro":""})),imageSize:{width:200,height:200},imageBox:{width:300,height:300},mode:h.H.product};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(h.Z,{...t}),s.jsx(m,{text:"查看更多产品",link:"/product"})]})}async function b(){await (0,g.nI)();let e=await (0,g.BH)(),t={infos:[{imageSrc:"/videos/phone-call-device-cover.webp",title:"zh"===e?"双向视频通话演示":"Two-Way Video Call Demonstration",videoSrc:"/videos/device-call-phone-video1.mp4"},{imageSrc:"/videos/tx-video-call-cover.webp",title:"zh"===e?"腾讯云音视频微通话（TWeCall）设备通话演示":"Tencent Cloud Audio and Video WeCall (TWeCall) Device Call Demonstration",videoSrc:"/videos/wx-twecall-video1.mp4"},{imageSrc:"/videos/voice-wakeup.webp",title:"zh"===e?"语音唤醒演示":"Voice Wake-up Demonstration",videoSrc:"zh"===e?"/videos/voice-wakeup-video1.mp4":"/videos/voice-wakeup-en-video1.mp4"},{imageSrc:"/videos/device-call-device.webp",title:"zh"===e?"设备与设备通话演示":"Device-to-Device Call Demonstration",videoSrc:"/videos/device-call-device-video1.mp4"}],imageSize:{width:300,height:168},imageBox:{width:300,height:168},mode:h.H.normal};return s.jsx(s.Fragment,{children:s.jsx(h.Z,{...t})})}let y=(0,r.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\about.tsx`),{__esModule:x,$$typeof:S}=y,E=y.default;async function T({params:e,searchParams:t},i){return{title:"zh"===e.locale?"深圳市赛蓝科技有限公司-智能家居品牌-看家王智能摄像头-赛蓝加菲狗产品网站":"Cylan - Cylan Clever Dog English Site",description:"赛蓝科技 引领生活",icons:{icon:"/favicon.ico"}}}function C(){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(o,{}),s.jsx(v,{}),s.jsx(E,{})]})}(0,r.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\about.tsx#CompanyTime`),(0,r.createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\home\about.tsx#Contacts`)},68:(e,t,i)=>{"use strict";i.d(t,{H:()=>s,Z:()=>d});var s,r=i(5036),a=i(2813),n=i(2),l=i.n(n);i(878);var o=i(6274);function d({infos:e,imageSize:t,imageBox:i,mode:s="",gap:n=0,isDetail:d=!1}){let c=({imageSrc:e,title:n,tip:l="",link:d="",videoSrc:c=""})=>{let p=!!d,u=!!c,h=`flex-box-with-4items__card ${"product"===s?"flex-box-with-4items__card--product":""} ${p?"flex-box-with-4items__card--link":""} ${u?"flex-box-with-4items__card--video":""}`,m=`${""===s?"":`flex-box-with-4items__card__info--${s}`} flex-box-with-4items__card__info`,f=(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"flex-box-with-4items__card__image",style:{aspectRatio:i.width/i.height},children:u?(0,r.jsxs)("video",{width:t.width,height:t.height,controls:!0,poster:e,children:[r.jsx("source",{src:c,type:"video/mp4"}),"Your browser does not support the video tag."]}):r.jsx(a.default,{unoptimized:!0,src:e,width:t.width,height:t.height,alt:"image",style:{objectFit:"fill",width:"100%",height:"auto"}})}),(0,r.jsxs)("div",{className:m,children:[r.jsx("div",{children:n}),"pride"===s?"":l?r.jsx("span",{children:l}):""]})]});return p?r.jsx(o.default,{href:d,className:h,children:f}):r.jsx("div",{className:h,children:f})};return r.jsx("div",{className:`flex-box-with-4items ${"product"===s?"flex-box-with-4items--product":""} ${d?"flex-box-with-4items--detail":""}`,style:n?{gap:n}:{},children:e.map((e,t)=>r.jsx(l().Fragment,{children:c(e)},`${e.link}-${t}`))})}!function(e){e.normal="",e.product="product",e.pride="pride"}(s||(s={}))},9352:(e,t,i)=>{"use strict";i.d(t,{Jx:()=>a,hh:()=>n});var s=i(5008);let r=[{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.title,text:"宣布今日发布新产品"},{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"01",category:s.pJ.cylan,type:s.Iq.news,linkProductId:"01",coverSrc:"/hotspot-image-1.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"02",category:s.pJ.industry,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"03",category:s.pJ.industry,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"04",category:s.pJ.cylan,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"05",category:s.pJ.cylan,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"06",category:s.pJ.cylan,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"07",category:s.pJ.cylan,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"08",category:s.pJ.cylan,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"09",category:s.pJ.cylan,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"},{title:"看家王双向视频通话摄像机二代大屏即将问世",tip:"近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机",time:new Date,content:[{type:s.tv.paragraph,text:"今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。"},{type:s.tv.image,src:"/product/product-c31.jpg"}],id:"10",category:s.pJ.cylan,type:s.Iq.news,coverSrc:"/hotspot-image-2.png"}];async function a(e){return new Promise(t=>{let i=r.find(t=>t.id===e);t(!!i&&i)})}async function n(e){return new Promise(t=>{let i=r.findIndex(t=>t.id===e);t(!(i<0)&&{prev:i-1,prevLink:i-1<0?"":`/article/${r[i-1].id}`,prevTitle:i-1<0?"":r[i-1].title,next:i===r.length-1?-1:i+1,nextLink:i===r.length-1?"":`/article/${r[i+1].id}`,nextTitle:i===r.length-1?"":r[i+1].title})})}},878:()=>{},2119:()=>{},3141:()=>{},3754:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[638,47,563,613,782],()=>i(5296));module.exports=s})();