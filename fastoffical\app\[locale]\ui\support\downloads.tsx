import styles from './support.module.scss'
import Image from 'next/image'
import Link from 'next/link'
import { getI18n, getCurrentLocale } from '@/locales/server'

export enum DownloadType {
  android = 'Android',
  ios = 'iOS',
  miniprogram = '小程序',
}

export default async function Downloads() {
  const t = await getI18n()
  const locale = getCurrentLocale()

  return (
    <div className={styles.downloads}>
      <h2>{t('imcamApp')}</h2>
      <p>{t('imcamAppTip')}</p>
      <div className={styles.downloads__content}>
        <Card type={DownloadType.android} version="1.2.333" />
        <Card type={DownloadType.ios} version="1.2.333" />
        {locale === 'zh' && (
          <Card type={DownloadType.miniprogram} version="1.1.0" />
        )}
      </div>
    </div>
  )
}

async function Card({
  type = DownloadType.android,
  version = '',
}: {
  type: DownloadType
  version?: string
}) {
  const t = await getI18n()
  return (
    <div className={styles.downloads__content__card}>
      <h5>{type === DownloadType.miniprogram ? t('dangdang') : t('imcam')}</h5>
      <h5>
        {type === DownloadType.miniprogram
          ? t('miniProgram')
          : type === DownloadType.android
          ? t('androidApp')
          : t('iosApp')}
      </h5>
      <Image
        src={
          type === DownloadType.miniprogram
            ? '/miniprogram-qrcode.jpg'
            : '/support/download-code.webp'
        }
        width={160}
        height={160}
        alt="qrcode"
      ></Image>
      {/* <p>版本：{version}</p> */}
      {type === DownloadType.android && (
        <Link
          href={
            'https://cn-pub.oss-cn-hangzhou.aliyuncs.com/forever/app/android/IM_Cam.apk'
          }
        >
          <button>
            <Image
              src={'/download-white.svg'}
              width={20}
              height={20}
              alt=""
            ></Image>
            {t('download')}
          </button>
        </Link>
      )}
      {type === DownloadType.ios && (
        <>
          <Link href={'https://apps.apple.com/app/im-cam/id6447252954'}>
            <button>{t('goToAppstore')}</button>
          </Link>
        </>
      )}
    </div>
  )
}
