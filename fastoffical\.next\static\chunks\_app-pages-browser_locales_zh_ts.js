"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_locales_zh_ts"],{

/***/ "(app-pages-browser)/./locales/zh.ts":
/*!***********************!*\
  !*** ./locales/zh.ts ***!
  \***********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = ({\n    home: \"首页\",\n    productCenter: \"产品中心\",\n    prodoctVideos: \"产品视频\",\n    support: \"服务支持\",\n    aboutUs: \"关于我们\",\n    productCamera: \"摄像头产品\",\n    downloadClient: \"下载客户端\",\n    chinese: \"中文\",\n    english: \"英文\",\n    seeMore: \"查看更多\",\n    year: \"年\",\n    companiyTime: \"公司成立时间\",\n    deviceSells: \"设备销量\",\n    activeUsers: \"活跃用户\",\n    contactUs: \"联系我们\",\n    pride1: \"深圳全球创新创业交流会优秀创新产品奖\",\n    pride2: \"欧洲联盟知识产权局注册证书\",\n    pride3: \"外观设计专利证书\",\n    pride4: \"RoHS证书\",\n    pride5: \"国家高新技术企业证书\",\n    pride6: \"中国智能家居产业联盟会员单位\",\n    pride7: \"英特尔会员资格\",\n    pride8: \"深圳市物联网协会会员单位\",\n    pride9: \"中国智慧城市优秀产品奖\",\n    cylanAddress: \"深圳市宝安区新安街道兴东社区72区群力二路1号合成号深圳民俗文化产业园211\",\n    aboutCylan: \"关于赛蓝\",\n    cylanPrides: \"荣誉资质\",\n    followUs: \"关注我们\",\n    imcamGongzhonghao: \"看家王公众号\",\n    downloadQRcode: \"下载二维码\",\n    copyrightText: \"版权所有 \\xa9 2024 深圳市赛蓝科技有限公司\",\n    copyrightLink: \" 粤ICP备12047353号\",\n    all: \"全部\",\n    imcamApp: \"赛蓝应用支持\",\n    imcamAppTip: \"随时随地体验智能生活\",\n    miniProgram: \"微信小程序\",\n    download: \"下载\",\n    goToAppstore: \"去应用商店\",\n    aboutCylanDescription: \"深圳市赛蓝科技有限公司，具备行业领先的智能终端硬件开发能力，自主拥有的智能家居品牌“看家王”，以科技让家居更有梦想为品牌愿景，始终践行“让家居生活更智能、更便捷”的使命，坚持品质创新、用户价值为先的核心原则，致力于为用户提供智能、舒适、便捷、安全、节能的家庭智能硬件设备。\\n\\n  “看家王”依托于赛蓝云平台的核心优势，以移动物联网为基础，通过打造多条智能家居产业价值链条，将全方位的为智能家庭构建智能化、数字化、便捷化的生活方式。\\n  \\n  目前，“看家王”产品线已囊括智能摄像头、台灯、自动投食机等产品线，并以先导性迅速占领市场，引领2B业务发展。产品获得欧盟RoHS环保认证，不仅在国内畅销，更远销北美、南美、欧洲等海外市场，赢得了广大消费者的一致认可，直接为国内外消费者带来便携化、数字化、智能化生活体验。\",\n    aboutCylanQuotesTitle1: \"愿景\",\n    aboutCylanQuotesTitle2: \"使命\",\n    aboutCylanQuotesTitle3: \"价值观\",\n    aboutCylanQuotesText1: \"科技让家居更有梦想！\",\n    aboutCylanQuotesText2: \"让家居生活更智能、更便捷！\",\n    aboutCylanQuotesText3: \"智能、舒适、便捷、安全、节能\",\n    career: \"发展历程\",\n    careerDescription: \"赛蓝成立于2005年，自成立以来，赛蓝始终致力于成为智能家居行业的创新者。赛蓝的发展历程充满了挑战和机遇，但我们始终秉持着创新、质量和客户至上的理念。目前在双向视频通话智能摄像机产品有30W+年销量，看家王APP累计获得45万+活跃用户。\",\n    page404: \"404 - 页面不存在\",\n    page404Description: \"抱歉，您访问的页面好像不存在，试试以下操作\",\n    page404Tip1: \"1、检查网址是否正确\",\n    page404Tip2: \"2、回到首页或者向上一页\",\n    backToHome: \"返回到首页\",\n    backPage: \"返回上一页\",\n    backToTop: \"返回顶部\",\n    help: \"使用帮助\",\n    detail: \"详情\",\n    time: \"时间\",\n    prevArticle: \"上一篇\",\n    nextArticle: \"下一篇\",\n    hot: \"热门\",\n    imcam: \"看家王\",\n    androidApp: \"安卓应用\",\n    iosApp: \"iOS应用\",\n    dangdang: \"铛铛看家\",\n    productTranslator: \"翻译机产品\",\n    robotQA: \"机器人问答演示\",\n    translateView: \"翻译对话演示\"\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./locales/zh.ts\n"));

/***/ })

}]);