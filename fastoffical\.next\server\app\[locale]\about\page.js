(()=>{var e={};e.id=853,e.ids=[853],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},4188:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>r.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>l,routeModule:()=>m,tree:()=>_});var a=i(482),s=i(9108),n=i(2563),r=i.n(n),o=i(8300),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);i.d(t,c);let _=["",{children:["[locale]",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,5248)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\about\\page.tsx"],d="/[locale]/about/page",u={require:i,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/about/page",pathname:"/[locale]/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:_}})},5865:(e,t,i)=>{Promise.resolve().then(i.bind(i,9649))},9649:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>m});var a=i(5344),s=i(9378),n=i.n(s),r=i(9410),o=i(355),c=i(3898),_=i(6112),l=i(3729),d=i(8428),u=i(2716);function m(){return a.jsx(u.e4,{locale:(0,u.eV)(),children:a.jsx(p,{})})}function p(){let e=(0,u.QT)(),[t,i]=(0,l.useState)([{id:"0",text:e("aboutCylan")},{id:"1",text:e("cylanPrides")},{id:"2",text:e("contactUs")}]),[s,c]=(0,l.useState)(t[0].id),[_,d]=(0,l.useState)(s),m=(0,l.useRef)(null),p=e=>{d(e)};return(0,a.jsxs)("div",{className:n().about,children:[a.jsx("div",{className:`${n().about__image} hide-on-small`,children:a.jsx(r.default,{src:"/about-banner.webp",width:1920,height:900,alt:"",style:{width:"100%",height:"100%",objectFit:"fill",objectPosition:"center"},unoptimized:!0})}),a.jsx("h6",{id:"about-cylan"}),a.jsx("div",{className:"hide-on-small",children:a.jsx(o.default,{title:e("aboutUs"),iconSrc:"/about-icon.svg",currentTab:s,tabs:t,showBanner:!1,background:"transparent",onTabChange:e=>{p(e)}})}),a.jsx("div",{className:"hide-on-medium hide-on-large",children:a.jsx(o.default,{title:e("aboutUs"),iconSrc:"/about-icon.svg",currentTab:s,tabs:t,bannerMobileSrc:"/about-banner-mobile.jpg",background:"rgb(214,218,211)",onTabChange:e=>{p(e)}})}),a.jsx(l.Suspense,{children:a.jsx(h,{contentRef:m,tabs:t,setCurrentTab:c,trigger:_})})]})}function h({tabs:e,setCurrentTab:t=()=>{},contentRef:i,trigger:s}){let r=(0,l.useRef)(null),o=(0,l.useRef)(null),_=(0,l.useRef)(null),m=(0,l.useRef)(!0),p=(0,d.useRouter)(),h=(0,d.usePathname)(),j=(0,l.useCallback)(i=>{let a;a=i===e[0].id?"about-cylan":i===e[1].id?"prides":"contacts",t(i),p.replace(`${h}/#${a}`)},[e,t,p,h]);return(0,l.useEffect)(()=>{let i=()=>{let i=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop,a=r.current?.getBoundingClientRect().top;a&&i<a&&t(e[0].id)};return window.addEventListener("scroll",i),()=>{window.removeEventListener("scroll",i)}},[j,e,t]),(0,l.useEffect)(()=>{m.current?m.current=!1:j(s)},[s,j]),(0,a.jsxs)(u.e4,{locale:(0,u.eV)(),children:[(0,a.jsxs)("div",{ref:i,className:n().about__content,children:[a.jsx("div",{ref:r,children:a.jsx(x,{})}),a.jsx(b,{}),a.jsx("div",{ref:o,children:a.jsx(g,{})}),a.jsx("h6",{id:"contacts"}),a.jsx("div",{ref:_,className:`${n().about__content__contacts} hide-on-small`,children:a.jsx(c.Contacts,{isAboutPage:!0})})]}),a.jsx("div",{className:`${n().about__content__contacts} hide-on-medium hide-on-large `,children:a.jsx(c.Contacts,{isAboutPage:!0})})]})}function x(){let e=(0,u.QT)(),t=[{imageSrc:"/hotspot-image-1.png",title:e("aboutCylanQuotesTitle1"),text:e("aboutCylanQuotesText1")},{imageSrc:"/hotspot-image-1.png",title:e("aboutCylanQuotesTitle2"),text:e("aboutCylanQuotesText2")},{imageSrc:"/hotspot-image-1.png",title:e("aboutCylanQuotesTitle3"),text:e("aboutCylanQuotesText3")}],i=({imageSrc:e,title:t,text:i})=>a.jsx("div",{className:n().about__content__main__quotes__item,children:(0,a.jsxs)("div",{children:[a.jsx("h5",{children:t}),a.jsx("span",{children:i})]})});return(0,a.jsxs)("div",{className:n().about__content__main,children:[a.jsx("h1",{children:e("aboutCylan")}),a.jsx("p",{children:e("aboutCylanDescription")}),a.jsx("div",{className:n().about__content__main__quotes,children:t.map((e,t)=>a.jsx(i,{...e},t))})]})}function b(){var e;let t;let i=(0,u.QT)();return(e=t||(t={}))[e.first=0]="first",e[e.normal=1]="normal",e[e.last=2]="last",(0,a.jsxs)("div",{className:n().about__content__career,children:[a.jsx("h1",{children:i("career")}),a.jsx("p",{children:i("careerDescription")}),a.jsx("div",{className:n().about__content__career__companytime,children:a.jsx(c.CompanyTime,{isAboutPage:!0})}),a.jsx("h6",{id:"prides"})]})}function g(){var e;let t;let i=(0,u.QT)();(e=t||(t={}))[e.small=0]="small",e[e.normal=1]="normal",e[e.large=2]="large";let[s,o]=(0,l.useState)([{text:i("pride1"),show:!1},{text:i("pride2"),show:!1},{text:i("pride3"),show:!1},{text:i("pride4"),show:!1},{text:i("pride5"),show:!1},{text:i("pride6"),show:!1},{text:i("pride7"),show:!1},{text:i("pride8"),show:!1},{text:i("pride9"),show:!1}]),c=({src:e,size:t=1,index:i})=>(0,a.jsxs)("div",{onClick:()=>{o(s.map((e,t)=>(t===i&&(e.show=!e.show),e)))},className:`${n().about__content__prides__list__item} ${2===t?n()["about__content__prides__list__item--large"]:1===t?n()["about__content__prides__list__item--normal"]:n()["about__content__prides__list__item--small"]}`,children:[a.jsx(r.default,{src:e,width:2===t?478:231,height:0===t?154:326,alt:"",style:{height:"100%",width:"100%",objectFit:"fill"},unoptimized:!0}),s[i].show&&a.jsx("div",{className:n().about__content__prides__list__item__cover,children:s[i].text})]});return a.jsx(a.Fragment,{children:(0,a.jsxs)("div",{className:`${n().about__content__prides}`,children:[a.jsx("h1",{children:i("cylanPrides")}),(0,a.jsxs)("div",{className:"hide-on-small",children:[(0,a.jsxs)("div",{className:n().about__content__prides__list,children:[a.jsx(c,{src:"/pride-image-1.jpg",index:0}),a.jsx(c,{src:"/pride-image-2.jpg",size:2,index:1}),a.jsx(c,{src:"/pride-image-3.jpg",index:2}),a.jsx(c,{src:"/pride-image-4.jpg",index:3})]}),(0,a.jsxs)("div",{className:n().about__content__prides__list,children:[a.jsx(c,{src:"/pride-image-5.jpg",size:0,index:4}),a.jsx(c,{src:"/pride-image-6.jpg",size:0,index:5}),a.jsx(c,{src:"/pride-image-7.jpg",size:0,index:6}),a.jsx(c,{src:"/pride-image-8.jpg",size:0,index:7}),a.jsx(c,{src:"/pride-image-9.jpg",size:0,index:8})]})]}),a.jsx(_.Z,{})]})})}},6112:(e,t,i)=>{"use strict";i.d(t,{Z:()=>r});var a=i(5344),s=i(9410),n=i(2716);function r(){return a.jsx(n.e4,{locale:(0,n.eV)(),children:a.jsx(o,{})})}function o(){let e=(0,n.QT)(),t=[{imageSrc:"/pride-image-1.jpg",title:e("pride1"),imageWidth:120},{imageSrc:"/pride-image-2.jpg",title:e("pride2"),imageWidth:242},{imageSrc:"/pride-image-3.jpg",title:e("pride3"),imageWidth:120},{imageSrc:"/pride-image-4.jpg",title:e("pride4"),imageWidth:120},{imageSrc:"/pride-image-5.jpg",title:e("pride5"),imageWidth:240},{imageSrc:"/pride-image-6.jpg",title:e("pride6"),imageWidth:240},{imageSrc:"/pride-image-7.jpg",title:e("pride7"),imageWidth:240},{imageSrc:"/pride-image-8.jpg",title:e("pride8"),imageWidth:240},{imageSrc:"/pride-image-9.jpg",title:e("pride9"),imageWidth:240}],i=({imageSrc:e,title:t,imageWidth:i})=>(0,a.jsxs)("div",{style:{width:i},children:[a.jsx("div",{children:a.jsx(s.default,{src:e,height:160,width:i,alt:"",unoptimized:!0})}),a.jsx("div",{children:t})]});return a.jsx("div",{className:"cylan-certificates hide-on-medium hide-on-large",children:t.map((e,t)=>a.jsx(i,{...e},t))})}},7572:(e,t,i)=>{"use strict";i.d(t,{H:()=>a,Z:()=>_});var a,s=i(5344),n=i(9410),r=i(3729),o=i.n(r);i(4507);var c=i(6506);function _({infos:e,imageSize:t,imageBox:i,mode:a="",gap:r=0,isDetail:_=!1}){let l=({imageSrc:e,title:r,tip:o="",link:_="",videoSrc:l=""})=>{let d=!!_,u=!!l,m=`flex-box-with-4items__card ${"product"===a?"flex-box-with-4items__card--product":""} ${d?"flex-box-with-4items__card--link":""} ${u?"flex-box-with-4items__card--video":""}`,p=`${""===a?"":`flex-box-with-4items__card__info--${a}`} flex-box-with-4items__card__info`,h=(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"flex-box-with-4items__card__image",style:{aspectRatio:i.width/i.height},children:u?(0,s.jsxs)("video",{width:t.width,height:t.height,controls:!0,poster:e,children:[s.jsx("source",{src:l,type:"video/mp4"}),"Your browser does not support the video tag."]}):s.jsx(n.default,{unoptimized:!0,src:e,width:t.width,height:t.height,alt:"image",style:{objectFit:"fill",width:"100%",height:"auto"}})}),(0,s.jsxs)("div",{className:p,children:[s.jsx("div",{children:r}),"pride"===a?"":o?s.jsx("span",{children:o}):""]})]});return d?s.jsx(c.default,{href:_,className:m,children:h}):s.jsx("div",{className:m,children:h})};return s.jsx("div",{className:`flex-box-with-4items ${"product"===a?"flex-box-with-4items--product":""} ${_?"flex-box-with-4items--detail":""}`,style:r?{gap:r}:{},children:e.map((e,t)=>s.jsx(o().Fragment,{children:l(e)},`${e.link}-${t}`))})}!function(e){e.normal="",e.product="product",e.pride="pride"}(a||(a={}))},355:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>_});var a=i(5344);i(4507);var s=i(9410);function n({src:e="/search-banner.webp",mobileSrc:t="/search-banner.webp"}){return(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"banner hide-on-small",children:a.jsx(s.default,{src:e,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})}),a.jsx("div",{className:"banner hide-on-medium hide-on-large",children:a.jsx(s.default,{src:t,width:1920,height:220,alt:"",className:"banner__image",unoptimized:!0})})]})}var r=i(3729),o=i.n(r),c=i(6506);function _({title:e="",iconSrc:t="",tabs:i=[],currentTab:r,bannerSrc:_="/pic_shipinbg@2x (1).webp",bannerMobileSrc:d="/pic_shipinbg@2x (1).webp",background:u="rgb(179, 220, 252)",onTabChange:m=()=>{},showBanner:p=!0,isSearch:h=!1,isLink:x=!1}){let b=e=>{m(e)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"page-tabs",style:u?{background:u}:{},children:[p&&a.jsx(n,{src:_,mobileSrc:d}),a.jsx(()=>a.jsx("div",{className:"page-tabs__content",children:(0,a.jsxs)("div",{className:"page-tabs__content__title",children:[a.jsx(s.default,{src:t,width:34,height:34,alt:""}),a.jsx("h1",{children:e})]})}),{}),!h&&a.jsx(({isLink:e=!1})=>a.jsx("div",{className:"page-tabs__content__items hide-on-small",children:i.map((t,i)=>a.jsx(o().Fragment,{children:e?a.jsx(c.default,{href:`${t.id}`,children:a.jsx("button",{className:`page-tabs__content__items__item ${r===t.id?"page-tabs__content__items__item--active":""}`,onClick:()=>b(t.id),children:t.text})}):a.jsx("button",{className:`page-tabs__content__items__item ${r===t.id?"page-tabs__content__items__item--active":""}`,onClick:()=>b(t.id),children:t.text})},i))}),{isLink:x})]}),a.jsx(l,{onTabChange:m,tabs:i,currentTab:r,isLink:x})]})}function l({tabs:e,currentTab:t,onTabChange:i,isLink:s}){let n=({id:e,text:s})=>a.jsx("div",{className:`${t===e?"page-tabs__tabs-small__tab--active":""} page-tabs__tabs-small__tab`,onClick:()=>{i(e)},children:s});return a.jsx("div",{className:"page-tabs__tabs-small hide-on-medium hide-on-large",children:e.map((e,t)=>a.jsx(o().Fragment,{children:s?a.jsx(c.default,{href:e.id,children:a.jsx(n,{...e})}):a.jsx(n,{...e})},t))})}},3898:(e,t,i)=>{"use strict";i.r(t),i.d(t,{CompanyTime:()=>m,Contacts:()=>h,default:()=>d});var a=i(5344),s=i(5620),n=i.n(s),r=i(6506),o=i(9410),c=i(7572),_=i(6112),l=i(2716);function d(){return a.jsx(l.e4,{locale:(0,l.eV)(),children:a.jsx(u,{})})}function u(){let e=(0,l.QT)();return(0,a.jsxs)("div",{className:n().about,children:[a.jsx("div",{className:n().about__cover}),(0,a.jsxs)("div",{className:n().about__content,children:[a.jsx("h3",{children:e("aboutUs")}),a.jsx(m,{}),a.jsx(p,{}),a.jsx(h,{})]})]})}function m({isAboutPage:e=!1}){let t=(0,l.QT)(),i=(0,l.eV)(),s=({num:e,unit:t,text:i})=>(0,a.jsxs)("div",{className:n().about__content__time__item,children:[(0,a.jsxs)("div",{children:[a.jsx("span",{children:e}),a.jsx("span",{children:t})]}),a.jsx("div",{children:i})]}),r=()=>a.jsx("div",{style:{height:46,width:0,opacity:.1,border:`1px solid ${e?"var(--gray)":"#fff"}`}});return(0,a.jsxs)("div",{className:`${n().about__content__time} ${e?n()["about__content__time--page"]:""}`,children:[a.jsx(s,{num:2005,unit:t("year"),text:t("companiyTime")}),a.jsx(r,{}),a.jsx(s,{num:"zh"===i?45:450,unit:"zh"===i?"万+":"K+",text:t("deviceSells")}),a.jsx(r,{}),a.jsx(s,{num:"zh"===i?90:900,unit:"zh"===i?"万+":"K+",text:t("activeUsers")})]})}function p(){let e=(0,l.QT)(),t={infos:[{imageSrc:"/pride-image-6.jpg",title:e("pride6")},{imageSrc:"/pride-image-9.jpg",title:e("pride9")},{imageSrc:"/pride-image-5.jpg",title:e("pride5")},{imageSrc:"/pride-image-2.jpg",title:e("pride2")}],imageSize:{width:300,height:200},imageBox:{width:300,height:200},mode:c.H.pride};return(0,a.jsxs)("div",{className:n().about__prides,children:[a.jsx("div",{className:"hide-on-small",style:{marginTop:30},children:a.jsx(c.Z,{...t})}),a.jsx(_.Z,{})]})}function h({isAboutPage:e=!1}){let t=(0,l.QT)(),i=({iconUrl:e,text:t})=>(0,a.jsxs)("div",{className:n().contacts__info__item,children:[a.jsx(o.default,{src:e,width:18,height:20,alt:"icon"}),a.jsx("div",{style:{display:"flex",flexDirection:"column",gap:4},children:t.split("/n").map((e,t)=>a.jsx("div",{children:e},t))})]});return(0,a.jsxs)("div",{className:`${n().contacts} ${e?n()["contacts--page"]:""}`,children:[(0,a.jsxs)("div",{className:n().contacts__info,children:[a.jsx("div",{className:n().contacts__info__title,children:t("contactUs")}),(0,a.jsxs)("div",{className:n().contacts__info__items,children:[a.jsx(i,{iconUrl:"/contact-position.svg",text:t("cylanAddress")}),a.jsx(i,{iconUrl:"/contact-email.svg",text:"<EMAIL>"}),a.jsx(i,{iconUrl:"/contact-phone.svg",text:"+86-0755-83073491"})]})]}),a.jsx("div",{className:n().contacts__address,children:a.jsx(r.default,{href:"https://map.baidu.com/poi/%E5%90%88%E6%88%90%E5%8F%B7%E6%B7%B1%E5%9C%B3%E6%B0%91%E4%BF%97%E6%96%87%E5%8C%96%E4%BA%A7%E4%B8%9A%E5%9B%AD/@12682764.738888016,2566016.5718568345,14z?uid=eb63e5cd850d1ef4a3acc4a1&ugc_type=3&ugc_ver=1&device_ratio=1&compat=1&pcevaname=pc4.1&querytype=detailConInfo",children:a.jsx(o.default,{src:"/address-image.webp",width:625,height:249,alt:"address"})})})]})}},9378:e=>{e.exports={about:"about_about__wvePq",about__image:"about_about__image__KxBNO",about__content:"about_about__content__LS1_A",about__content__main__quotes:"about_about__content__main__quotes__gj59l",about__content__main__quotes__item:"about_about__content__main__quotes__item__fN1Rr",about__content__career__companytime:"about_about__content__career__companytime__HUZ14",about__content__career__timeline:"about_about__content__career__timeline__hCaJK",about__content__career__timeline__item:"about_about__content__career__timeline__item__r5rr0","about__content__career__timeline__item--reverse":"about_about__content__career__timeline__item--reverse__s8Qhz",about__content__prides:"about_about__content__prides__0DhgW",about__content__prides__list:"about_about__content__prides__list__LSiiG",about__content__prides__list__item:"about_about__content__prides__list__item__BPyny","about__content__prides__list__item--normal":"about_about__content__prides__list__item--normal__tpjHv","about__content__prides__list__item--large":"about_about__content__prides__list__item--large__VfdUa","about__content__prides__list__item--small":"about_about__content__prides__list__item--small__fRQpL",about__content__prides__list__item__cover:"about_about__content__prides__list__item__cover__GN83j",about__content__contacts:"about_about__content__contacts__S9y1Y"}},5248:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>_,generateMetadata:()=>c});var a=i(5036);let s=(0,i(6843).createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\ui\about\about-content.tsx`),{__esModule:n,$$typeof:r}=s,o=s.default;async function c({params:e,searchParams:t},i){return{title:"zh"===e.locale?"关于我们":"About Us",description:"赛蓝科技 引领生活",icons:{icon:"/favicon.ico"}}}function _(){return a.jsx(o,{})}}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[638,47,563,7],()=>i(4188));module.exports=a})();