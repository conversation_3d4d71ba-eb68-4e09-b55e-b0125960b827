import styles from './news.module.scss'
import PageTabs from '../ui/components/page-tabs'
import Flex4ItemsBox, {
  Flex4ItemsInfo,
  itemsMode,
} from '../ui/components/flex-4items-box'
import Flex2ItemsBox from '../ui/components/flex-2items-box'
import Pagination from '../ui/components/pagination'

export default function Page() {
  const tabs = [
    {
      id: '0',
      text: '全部',
    },
    {
      id: '1',
      text: '行业资讯',
    },
    {
      id: '2',
      text: '赛蓝资讯',
    },
  ]

  return (
    <div className={styles.news}>
      <PageTabs
        title="新闻资讯"
        iconSrc="/news-icon.svg"
        currentTab={'0'}
        tabs={tabs}
        bannerSrc="/news-banner.webp"
        background="rgb(127,175,243)"
      />
      <List />
      <ListPagination />
    </div>
  )
}

function List() {
  const ListInfo: {
    infos: Flex4ItemsInfo
    imageSize: {
      width: number
      height: number
    }
    imageBox: {
      width: number
      height: number
    }
    mode: itemsMode
  } = {
    infos: [
      {
        imageSrc: '/hotspot-image-2.png',
        title: '疑似OPPO K10系列通过认证,支持80W快充',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/hotspot-image-2.png',
        title: '疑似OPPO K10系列通过认证,支持80W快充',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/hotspot-image-2.png',
        title: 'OPPO A96上架官网，这配置价格你心动吗？',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/hotspot-image-2.png',
        title: 'OPPO ',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/hotspot-image-2.png',
        title: 'OPPO ',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/hotspot-image-2.png',
        title: 'OPPO ',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/hotspot-image-2.png',
        title: 'OPPO ',
        tip: '2021-03-05',
      },
      {
        imageSrc: '/hotspot-image-2.png',
        title: 'OPPO ',
        tip: '2021-03-05',
      },
    ],
    imageSize: {
      width: 300,
      height: 200,
    },
    imageBox: {
      width: 300,
      height: 200,
    },
    mode: itemsMode.normal,
  }

  const infos = [
    {
      imageSrc: '/hotspot-image-2.png',
      title: '疑似OPPO K10系列通过认证,支持80W快充',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
    {
      imageSrc: '/hotspot-image-1.png',
      title: '首批天玑8000系列旗舰!疑似OPPO K10系列入网',
      description:
        'OPPO Find X5系列,双芯影像旗舰,首发搭载OPPO自研马里亚纳MariSilicon X 芯片,2月24日 19:00 全球发布.',
      time: '2020-11-27',
      link: '/article',
    },
  ]

  return (
    <>
      <div className={styles.news__list}>
        <Flex2ItemsBox infos={infos} />
      </div>
    </>
  )
}

function ListPagination() {
  return (
    <div className={styles.news__pagination}>
      <Pagination currentPage={1} count={27}></Pagination>
    </div>
  )
}
