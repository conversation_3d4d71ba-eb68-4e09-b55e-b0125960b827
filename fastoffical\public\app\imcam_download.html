﻿<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="textml; charset=utf-8"/>
<title>Im Cam App</title>
</head>
<body>
    <script type="text/javascript"> 
        var language = navigator.language || navigator.userLanguage;
        var userAgent = navigator.userAgent;
        if (language.indexOf('zh') > -1) {
            document.title = '看家王客户端'
            //自动跳转
            if (/iPad|iPhone|iPod/.test(userAgent)) {
                document.location = "https://apps.apple.com/app/im-cam/id6447252954?l=zh";
            } else if (/Android/.test(userAgent)) {
                //document.location = "https://cn-pub.oss-cn-hangzhou.aliyuncs.com/forever/app/android/IM_Cam.apk";
                // 判断是否为中文繁体
                if (language.toLowerCase() === 'zh-tw' || language.toLowerCase() === 'zh-hk') {
                    console.log('中文繁体');
                    document.location.href = "https://play.google.com/store/apps/details?id=com.cylan.chatcam";
                } else {
                  var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                  var charactersLength = characters.length;
                  var result = "";
                  for (var i = 0; i < 6; i++) {
                    result += characters.charAt(Math.floor(Math.random() * charactersLength));
                  }
                  document.location.href = "http://www.jfgou.com/app/imcam_download_and.html#" + result;
                }
            } else {
                document.location = "https://apps.apple.com/app/im-cam/id6447252954?l=zh";
            }
        } else {
            document.title = 'Im Cam App'
            if (/iPad|iPhone|iPod/.test(userAgent)) {
                document.location = "https://apps.apple.com/app/im-cam/id6447252954?l=en";
            } else if (/Android/.test(userAgent)) {
                //document.location = "https://sg-jfg-pub.oss-ap-southeast-1.aliyuncs.com/forever/app/android/IM_Cam.apk";
                document.location.href = "https://play.google.com/store/apps/details?id=com.cylan.chatcam";
            } else {
                document.location = "https://apps.apple.com/app/im-cam/id6447252954?l=en";
            }
        }
    </script> 
</body>
<html>