﻿<html>
<head lang="en">
    <meta charset="UTF-8"/>
    <meta content="mobiSiteGalore" name="Generator" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta name="apple-touch-fullscreen" content="YES" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta name="format-detection" content="telephone=no">
    <title>Im Cam App</title>
    <style type="text/css">
        html{background: #ffffff}
        body {
            margin: 0;
            padding: 0;
        }
        .mt10 {margin-top: 0.625em;}
        .mt60{margin-top: 6.25em;}
        .mt100{margin-top: 6.25em;}
        .section1{width: 100%;text-align: center}
        .section1 img {
            width: 4.625em;
            height: 4.625em;
        }
        @media screen and (min-width:250px) and (max-width:768px){
            .section1 img {text-align: center;margin: 0 auto;}
            .section1 .img{text-align: center;margin: 0 auto;}
        }
        @media screen and (min-width:768px){
            .section1 img{text-align: center; margin: 50px auto}
        }
        .appName {font-size: 1em;color: #222222;}
        .tip {
            font-size: 1em;
            color: #555555;
        }
        .download {
            background: #3A85FD;
            width: 8em;
            margin: 0 auto;
            padding: 0.7em 1em;
            border-radius: 8px 8px 8px 8px;
            color: #fff;
        }
        .browserTipBox {background: #E3EEFF;display: flex;align-items: center;}
        .browserTip {margin-left: 1.3em;color: #3A85FD;font-size: 0.9em;}
        .arrowImg {
            width: 2.5em;
            margin: 0.6em;
        }
        .adsBox {
            width: 100%;
            position: relative;
        }
        .closeAdsBox {
            position: absolute;
            top: 0;
            right: 0;
            padding: 0.25em 0.5em;
            background: rgba(0,0,0,0.30);
            border-radius: 1.25em;
            z-index: 20;
        }
        .closeAdsText {
            font-size: 0.75em;
            color: #ffffff;
            line-height: 1.25em;
        }
        .closeIcon {
            width: 1.25em;
            vertical-align: middle;
        }
    </style>
    <script src="./js/jquery-1.8.0.min.js"></script>
    <script>
        
    </script>
</head>
<body>
    <section class="browserTipBox"><div class="browserTip">Click the button in the upper right corner, and then in the pop-up menu, click "Open in Browser" to install</div><img class="arrowImg" src="image/arrow.png" /></section>
    <section class="section1 mt60"><img class="img" src="image/logo_imcam.png" /></section>
    <section class="section1 mt10"><div class="appName">IM Cam</div><div class="tip mt10">For Android device</div></section>
    <section class="section1 mt10 downloadBox"><div class="download">Install</div></section>
    <div class="adsBox mt100">
        <div class="closeAdsBox"><span class="closeAdsText">close</span><img class="img closeIcon" src="image/icon_close.png" /></div>
        <!---广告集成代码 开始 div需要放在body内，需要把appid替换为生产id，其他不动  appId：adsplus-app-2886299098 ---->
        <div id="adsplusId"></div>
        <script async src="https://d.adsplus.cn/adsplus1.2.js?appid=adsplus-app-2886299098&containerAdId=adsplusId"  crossorigin="anonymous"></script> 
        <!---广告集成代码 结束---->
    </div>
    
</body>
<script>
    var language = navigator.language || navigator.userLanguage;
    if (language.indexOf('zh') > -1) {
        $(".appName").text("看家王");
        $(".tip").text('适用于Android设备');
        $(".download").text('安装');
        $(".browserTip").text('点击右上角按钮，然后在弹出的菜单中，点击在浏览器中打开，即可安装');
        $(".closeAdsText").text("关闭");
    } else {
        $(".appName").text("IM Cam");
        $(".tip").text('For Android device');
        $(".download").text('Install');
        $(".browserTip").text('Click the button in the upper right corner, and then in the pop-up menu, click "Open in Browser" to install');
        $(".closeAdsText").text("Close");
    }
    $(".browserTipBox").hide()
    var u = navigator.userAgent;
    var is_mobile = !!u.match(/AppleWebKit.*Mobile.*/); //是否为移动终端
    console.log(is_mobile)
    if (is_mobile) {//判断是否是移动设备打开
        var ua = navigator.userAgent.toLowerCase();//获取判断用的对象
        // 在微信中打开  在新浪微博客户端打开  在QQ里面打开(安卓链接包含MicroMessenger和QQ所以要去除)   在腾讯微博
        if (ua.match(/MicroMessenger/i) == "micromessenger" || ua.match(/WeiBo/i) == "weibo" || (ua.match(/qq/i) == "qq" && ua.match(/MicroMessenger/i) != "micromessenger") || ua.match(/txmicro/i) == "txmicro") {
            $(".browserTipBox").show()
            $(".downloadBox").hide()
        } else {
            $(".browserTipBox").hide()
            $(".downloadBox").show()
        }
    } else {
        //否则就是PC浏览器打开
        $(".browserTipBox").hide()
        $(".downloadBox").show()
    }
    $(".download").click(function (event) {
        if (language.indexOf('zh') > -1) {
            document.location = "https://cn-pub.oss-cn-hangzhou.aliyuncs.com/forever/app/android/IM_Cam.apk";
        } else {
            document.location = "https://sg-jfg-pub.oss-ap-southeast-1.aliyuncs.com/forever/app/android/IM_Cam.apk";
        }
    });
    $(".closeAdsBox").click(function (event) {
        $(".adsBox").remove()
    });
</script>
</html>