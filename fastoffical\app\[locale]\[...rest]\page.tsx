import { notFound } from 'next/navigation'

import { PageProps } from '@/data/type'
import type { Metadata, ResolvingMetadata } from 'next'

export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    title: locale === 'zh' ? '页面未找到 - 赛蓝科技' : 'Page not found - Cylan',
    description: '赛蓝科技 引领生活',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default function CatchAllPage() {
  notFound()
}
