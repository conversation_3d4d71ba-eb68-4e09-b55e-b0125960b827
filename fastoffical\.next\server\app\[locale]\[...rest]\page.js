"use strict";(()=>{var e={};e.id=984,e.ids=[984],e.modules={7849:e=>{e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{e.exports=require("path")},7310:e=>{e.exports=require("url")},2496:(e,t,a)=>{a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>p,routeModule:()=>u,tree:()=>c});var o=a(482),r=a(9108),i=a(2563),n=a.n(i),s=a(8300),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);a.d(t,l);let c=["",{children:["[locale]",{children:["[...rest]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4745)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\[...rest]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\[...rest]\\page.tsx"],d="/[locale]/[...rest]/page",f={require:a,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/[...rest]/page",pathname:"/[locale]/[...rest]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4745:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i,generateMetadata:()=>r});var o=a(867);async function r({params:e,searchParams:t},a){return{title:"zh"===e.locale?"页面未找到 - 赛蓝科技":"Page not found - Cylan",description:"赛蓝科技 引领生活",icons:{icon:"/favicon.ico"}}}function i(){(0,o.notFound)()}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),o=t.X(0,[638,47,563,7],()=>a(2496));module.exports=o})();