:root {
  --text-dark: #222;
  --text-description: #555;
  --text-mark: #888;

  --color-theme: #0075eb;
  --color-red-tip: #e53862;
  --color-background: #f5f5f5;

  --gray: #aaa;
  --gray-1: #bbb;
  --gray-2: #ccc;
  --gray-3: #ddd;
  --gray-4: #eee;
  --gray-5: #f2f2f2;
  --gray-6: #f4f4f4;
  --gray-7: #f6f6f6;

  --font-large: 18px;
  --font-medium: 16px;
  --font-normal: 14px;
  --font-small: 12px;
  --font-bold: 500;
  --font-bolder: 600;
  --font-heavy: 800;

  --width-content: 1280px;
}
html {
  width: 100%;
  height: 100%;
}
/* 整个滚动条 */
::-webkit-scrollbar {
  width: 10px; /* 滚动条宽度 */
  height: 10px; /* 滚动条高度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* 轨道背景色 */
  border-radius: 5px; /* 轨道圆角 */
}

/* 滚动条把手 */
::-webkit-scrollbar-thumb {
  background-color: #888; /* 把手背景色 */
  border-radius: 5px; /* 把手圆角 */
}

/* 滚动条把手在悬停和活动时的样式 */
::-webkit-scrollbar-thumb:hover,
::-webkit-scrollbar-thumb:active {
  background-color: #555; /* 把手悬停和活动时的背景色 */
}
body {
  background-color: var(--color-background);
  margin: 0;
  padding: 0;
  color: var(--text-dark);
  scrollbar-width: thin;
  scrollbar-color: #888 #f0f0f0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
main {
  flex: 1;
}
div {
  box-sizing: border-box;
  font-size: var(--font-normal);
}
a {
  text-decoration: none;
  color: inherit;
}
input:focus {
  outline: none;
  border: none;
  box-sizing: border-box;
}
button {
  background-color: transparent;
  box-sizing: border-box;
  border: none;
  &:hover {
    cursor: pointer;
  }
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-block: 0;
  font-weight: var(--font-bolder);
  margin: 0 auto;
}
p {
  margin: 0;
  margin-block: 0;
}
h1 {
  font-size: 26px;
}
h2 {
  font-size: 24px;
}
h3 {
  font-size: 22px;
}
h4 {
  font-size: 20px;
}
h5 {
  font-size: 18px;
}
h6 {
  font-size: 16px;
}

/* 小屏: 0 - 450px */
@media (max-width: 450px) {
  .hide-on-small {
    display: none !important;
  }
}

/* 中屏: 451px - 1280px */
@media (min-width: 451px) and (max-width: 1280px) {
  .hide-on-medium {
    display: none !important;
  }
}

/* 大屏: 1281px 以上 */
@media (min-width: 1281px) {
  .hide-on-large {
    display: none !important;
  }
}

@media (min-width: 751px) and (max-width: 1280px) {
  :root {
    --width-content: calc(100vw - 10px);
  }
}

@media (min-width: 451px) and (max-width: 750px) {
  :root {
    width: 750px;
    --width-content: 740px;
  }
}

@media (min-width: 321px) and (max-width: 450px) {
  :root {
    --width-content: 100vw;
  }
}

@media (max-width: 320px) {
  :root {
    --width-content: 320px;
  }
}
