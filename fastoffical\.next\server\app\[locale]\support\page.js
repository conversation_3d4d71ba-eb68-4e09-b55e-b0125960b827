(()=>{var e={};e.id=725,e.ids=[725],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},9807:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>p,routeModule:()=>f,tree:()=>c});var r=a(482),o=a(9108),s=a(2563),i=a.n(s),n=a(8300),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let c=["",{children:["[locale]",{children:["support",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4775)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,5492)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,6529)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,8157)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,2917)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,1429)),"D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,7481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["D:\\---AProjects---\\imcam-officialsite\\fastoffical\\app\\[locale]\\support\\page.tsx"],u="/[locale]/support/page",d={require:a,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/[locale]/support/page",pathname:"/[locale]/support",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2074:(e,t,a)=>{Promise.resolve().then(a.bind(a,3728))},3728:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(8428);function o(){return(0,r.useRouter)().replace("/support/download_client"),null}},5492:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s,generateMetadata:()=>o});var r=a(5036);async function o({params:e},t){return{title:"zh"===e.locale?"服务支持":"Support",description:"赛蓝科技 引领生活",icons:{icon:"/favicon.ico"}}}function s({children:e}){return r.jsx("div",{children:e})}},4775:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>s,__esModule:()=>o,default:()=>i});let r=(0,a(6843).createProxy)(String.raw`D:\---AProjects---\imcam-officialsite\fastoffical\app\[locale]\support\page.tsx`),{__esModule:o,$$typeof:s}=r,i=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[638,47,563,613],()=>a(9807));module.exports=r})();