import './c31.scss'
import Image from 'next/image'
import Link from 'next/link'
import { getI18n, getCurrentLocale } from '@/locales/server'
import type { Metadata, ResolvingMetadata } from 'next'
import { PageProps } from '@/data/type'

export async function generateMetadata(
  { params, searchParams }: PageProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const locale = params.locale
  return {
    title: locale === 'zh' ? '看家王双向通话智能摄像机C31' : 'IM CAM C31',
    description:
      '赛蓝科技 引领生活 看家王双向通话智能摄像机C31 看家王带屏摄像机',
    icons: {
      icon: '/favicon.ico',
    },
  }
}

export default async function Page({ params, searchParams }: PageProps) {
  const t = await getI18n()
  const locale = await getCurrentLocale()

  const Cover = ({ src }: { src: string }) => {
    return (
      <div className="c31__banner__cover">
        <Image
          width={1920}
          height={664}
          alt=""
          src={src}
          style={{ height: '100%', width: '100%' }}
          unoptimized
        ></Image>
      </div>
    )
  }

  return (
    <>
      <div className="c31-outter">
        <div className="c31">
          <div className="c31__banner1 c31__banner">
            <Cover src="/c31/pic-01bg.jpg" />
            <div className="c31__banner__main">
              <div className="c31__banner1__product">
                <Image
                  src={'/c31/pic-01-ys.png'}
                  width={445}
                  height={664}
                  alt=""
                  unoptimized
                ></Image>
              </div>
              <div
                className="c31__banner1__description"
                style={
                  locale === 'en' ? { marginLeft: 20 } : { marginLeft: 100 }
                }
              >
                <div className="c31__banner1__icon">
                  <Image
                    src={'/c31/imcam-icon.png'}
                    width={74}
                    height={74}
                    alt=""
                    unoptimized
                  ></Image>
                </div>
                {locale === 'en' ? (
                  <p>Communicate and Protect Anytime, Anywhere!</p>
                ) : (
                  <p>随时随地沟通和守护</p>
                )}
                <span>
                  {locale === 'zh' ? '看家王C31摄像机' : 'IM CAM C31'}
                </span>

                <div className="c31__banner1__qrcode">
                  <Link href={'/support/download_client'}>
                    <button>
                      {locale === 'zh' ? '下载APP' : 'Download App'}
                    </button>
                  </Link>
                  <div className="c31__banner1">
                    <Image
                      src={'/c31/qrcode.png'}
                      width={156}
                      height={166}
                      alt=""
                    ></Image>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="c31__banner2 c31__banner">
            <Cover src={'/c31/pic-bg02-4.jpg'} />
            <div className="c31__banner__main">
              <div className="c31__banner__content">
                <h2>
                  {locale === 'zh'
                    ? '安防监控，报警推送'
                    : 'Security Monitoring，Alarm Push'}
                </h2>
                <p>
                  {locale === 'zh'
                    ? '智能人形检测，高清红外夜视'
                    : 'Humanoid Detection, High Definition Night Vision.'}
                </p>
                <div>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner2-icon1.png'
                        : '/c31/banner2-en-icon1.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner2-icon2.png'
                        : '/c31/banner2-en-icon2.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner2-icon3.png'
                        : '/c31/banner2-en-icon3.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                </div>
              </div>
              <div className="c31__banner__image">
                <Image
                  src={
                    locale === 'zh'
                      ? '/c31/pic-02-ys-cn.png'
                      : '/c31/pic_02_en.png'
                  }
                  width={945}
                  height={484}
                  alt=""
                  unoptimized
                ></Image>
              </div>
            </div>
          </div>

          <div className="c31__banner3 c31__banner">
            <Cover src="/c31/pic-01bg.jpg" />
            <div className="c31__banner__main">
              <div className="c31__banner__content">
                <h2>
                  {locale === 'zh'
                    ? '一键呼叫，双向视频通话'
                    : 'One-Touch Call, Two-Way Video Calling'}
                </h2>
                <p>
                  {locale === 'zh'
                    ? '1. 设备呼叫设备，设备呼叫手机，一键轻松操作。'
                    : '1. Device-to-Device Calling, Device-to-Mobile Calling, Easy One-Click Operation.'}
                  <br></br>
                  {locale === 'zh'
                    ? '2. 自定义设置呼叫按钮，随心指定呼叫爸爸或妈妈。'
                    : '2. Customizable Call Buttons, Personalize Calling for Dad or Mom'}
                </p>
                <div>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner3-icon1.png'
                        : '/c31/banner3-en-icon1.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner3-icon2.png'
                        : '/c31/banner3-en-icon2.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner3-icon3.png'
                        : '/c31/banner3-en-icon3.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                </div>
              </div>
              <div className="c31__banner__image">
                <Image
                  src={'/c31/pic_03_ys.png'}
                  width={1080}
                  height={480}
                  alt=""
                  unoptimized
                ></Image>
              </div>
            </div>
          </div>

          <div className="c31__banner4 c31__banner">
            <Cover src="/c31/pic-bg02-4.jpg" />
            <div className="c31__banner__main">
              <div className="c31__banner__content">
                <h2>
                  {locale === 'zh'
                    ? '视频通话 ，移动跟随'
                    : 'Video Calling with Move Following'}
                </h2>
                <div>
                  <Image
                    src={'/c31/banner4-icon1.png'}
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner4-icon2.png'
                        : '/c31/banner4-en-icon2.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                  <Image
                    src={
                      locale === 'zh'
                        ? '/c31/banner4-icon3.png'
                        : '/c31/banner4-en-icon3.png'
                    }
                    width={62}
                    height={62}
                    alt=""
                    unoptimized
                  ></Image>
                </div>
              </div>
              <div className="c31__banner__image">
                <Image
                  src={
                    locale === 'zh'
                      ? '/c31/pic-04-ys-cn.png'
                      : '/c31/pic-04-ys-en.png'
                  }
                  width={1165}
                  height={514}
                  alt=""
                  unoptimized
                ></Image>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
