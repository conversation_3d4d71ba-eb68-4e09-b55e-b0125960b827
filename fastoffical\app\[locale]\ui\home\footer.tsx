'use client'
import React from 'react'
import styles from './home.module.scss'
import Image from 'next/image'
import Link from 'next/link'
import NavList from '../components/nav-list'
import { useState } from 'react'
import { useI18n, I18nProviderClient, useCurrentLocale } from '@/locales/client'

export default function FooterLayout() {
  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <Footer></Footer>
    </I18nProviderClient>
  )
}

function Footer() {
  const t = useI18n()

  type LinkInfo = {
    link: string
    text: string
  }

  const renderLink = ({ link, text }: { link: string; text: string }) => {
    return (
      <a href={link}>
        <span>{text}</span>
      </a>
    )
  }

  const FooterItem = ({
    title,
    links,
  }: {
    title: string
    links: Array<LinkInfo>
  }) => (
    <div className={styles.footer__links__item}>
      <div>{title}</div>
      {links.map((item, index) => (
        <React.Fragment key={`${item.link}-${index}`}>
          {renderLink({ link: item.link, text: item.text })}
        </React.Fragment>
      ))}
    </div>
  )

  const productLinks: LinkInfo[] = [
    {
      link: '/product?tab=01',
      text: t('productCamera'),
    },
    {
      link: '/product?tab=02',
      text: t('productTranslator'),
    },
  ]

  const supportLinks: LinkInfo[] = [
    {
      link: '/support/download_client',
      text: t('downloadClient'),
    },
    {
      link: '/support/help',
      text: t('help'),
    },
  ]

  const aboutLinks: LinkInfo[] = [
    {
      link: '/about#about-cylan',
      text: t('aboutCylan'),
    },
    {
      link: '/about#prides',
      text: t('cylanPrides'),
    },
    {
      link: '/about#contacts',
      text: t('contactUs'),
    },
  ]

  const Follow = () => {
    const [isShowPop, setIsShowpop] = useState(false)

    return (
      <div className={styles.footer__links__follow}>
        <div>{t('followUs')}</div>
        <div
          onMouseEnter={() => {
            setIsShowpop(true)
          }}
          onMouseLeave={() => {
            setIsShowpop(false)
          }}
          className={styles.footer__links__follow__weixin}
        >
          <Image
            src={'/weixin.svg'}
            width={20}
            height={20}
            alt="weixin"
          ></Image>
          {isShowPop && (
            <div>
              <Image
                src={'/support/imcam-gongzhonghao.jpg'}
                width={140}
                height={140}
                alt=""
              ></Image>
              <span>{t('imcamGongzhonghao')}</span>
              <a
                className="hide-on-medium hide-on-large"
                href="/support/imcam-gongzhonghao.jpg"
                download
              >
                {t('downloadQRcode')}
              </a>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={styles.footer}>
      <div className={`${styles.footer__logo} hide-on-medium hide-on-large`}>
        <Link href={'/'}>
          <Image
            src={'/cylan_logo-white.png'}
            width={125}
            height={44}
            alt="logo"
            unoptimized
          ></Image>
        </Link>
      </div>
      <div className={`${styles.footer__links} hide-on-small`}>
        <div className={`${styles.footer__logo}`}>
          <Link href={'/'}>
            <Image
              src={'/cylan_logo-white.png'}
              width={125}
              height={44}
              alt="logo"
              unoptimized
            ></Image>
          </Link>
        </div>
        <FooterItem title={t('productCenter')} links={productLinks} />
        {/* <FooterItem title="产品视频" links={[link, link, link]} /> */}
        <FooterItem title={t('support')} links={supportLinks} />
        {/* <FooterItem title="新闻资讯" links={[link, link]} /> */}
        <FooterItem title={t('aboutUs')} links={aboutLinks} />
        <Follow />
      </div>
      <div className="hide-on-large hide-on-medium">
        <NavList isFooter />
        <Follow />
      </div>
      <div className={styles.footer__copyright}>
        {t('copyrightText')}
        <Link
          className={styles.footer__copyright__link}
          href={'https://beian.miit.gov.cn/'}
          target="_blank"
        >
          {t('copyrightLink')}
        </Link>
      </div>
    </div>
  )
}
