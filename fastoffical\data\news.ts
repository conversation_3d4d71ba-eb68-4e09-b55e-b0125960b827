import { NewsData, ArticleContentType, NewsCategory, ArticleType, PrevAndNextArticle } from "./type";

const newsDatas: Array<NewsData> = [
  {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.title,
        text: '宣布今日发布新产品'
      },
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '01',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    linkProductId: '01',
    coverSrc: '/hotspot-image-1.png'
  },
  {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '02',
    category: NewsCategory.industry
    ,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '03',
    category: NewsCategory.industry,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '04',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '05',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '06',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '07',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '08',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '09',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }, {
    title: '看家王双向视频通话摄像机二代大屏即将问世',
    tip: '近期，看家王将推出一款全新的大屏摄像机摄像机摄像机摄像机摄像机摄像机',
    time: new Date(),
    content: [
      {
        type: ArticleContentType.paragraph,
        text: '今日 Redmi 红米手机官方宣布，开年大作 Redmi K50 电竞版将于 2 月 16 日发布，官方称“为 K 系列用户，打造一部硬核的 Dream Phone”。Redmi K50 电竞版将搭载骁龙 8 Gen 1 处理器，支持 VRS 可变分辨率渲染技术，配备 LPDDR5 内存和 UFS3.1 闪存。'
      },
      {
        type: ArticleContentType.image,
        src: '/product/product-c31.jpg'
      }
    ],
    id: '10',
    category: NewsCategory.cylan,
    type: ArticleType.news,
    coverSrc: '/hotspot-image-2.png'
  }
]

export default async function getNewsDatas(): Promise<NewsData[]> {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(newsDatas)
    }, 10);
  })
}

export async function getNewsData(id: string): Promise<NewsData | false> {
  return new Promise(resolve => {
    const newsData = newsDatas.find(news => news.id === id)
    if (newsData) resolve(newsData)
    else resolve(false)
  })
}

export async function getPrevAndNextNewsData(id: string): Promise<PrevAndNextArticle | false> {
  return new Promise(resolve => {
    const index = newsDatas.findIndex((item) => item.id === id)
    if (index < 0) {
      resolve(false)
    } else {
      resolve({
        prev: index - 1,
        prevLink: index - 1 < 0 ? '' : `/article/${newsDatas[index - 1].id}`,
        prevTitle: index - 1 < 0 ? '' : newsDatas[index - 1].title,
        next: index === newsDatas.length - 1 ? -1 : index + 1,
        nextLink: index === newsDatas.length - 1 ? '' : `/article/${newsDatas[index + 1].id}`,
        nextTitle: index === newsDatas.length - 1 ? '' : newsDatas[index + 1].title,
      })
    }
  })
}

export async function getHotNews(): Promise<NewsData[]> {
  return new Promise((resolve) => {
    resolve(newsDatas.filter((_, index) => index < 3))
  })
}