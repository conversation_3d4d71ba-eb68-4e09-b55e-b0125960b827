'use client'
import styles from './product.module.scss'
import PageTabs from '../components/page-tabs'
import { useState } from 'react'
import Flex4ItemsBox, {
  Flex4ItemsInfo,
  itemsMode,
} from '../components/flex-4items-box'
import Pagination from '../components/pagination'
import { ProductData } from '@/data/type'
import { useI18n, useCurrentLocale, I18nProviderClient } from '@/locales/client'

type Tab = {
  id: string
  name: string
  nameEn: string
}

export default function ProductListLayout({
  groups,
  products,
}: {
  groups: Array<Tab>
  products: ProductData[]
}) {
  return (
    <I18nProviderClient locale={useCurrentLocale()}>
      <ProductList groups={groups} products={products}></ProductList>
    </I18nProviderClient>
  )
}

function ProductList({
  groups,
  products,
  initialTab,
}: {
  groups: Array<Tab>
  products: ProductData[]
  initialTab?: string
}) {
  const t = useI18n()

  const tabs = [...groups]
  tabs.unshift({
    id: '00',
    name: t('all'),
    nameEn: t('all'),
  })

  const _tabs = [
    {
      id: '0',
      text: '全部',
    },
    {
      id: '1',
      text: '摄像机类型',
    },
  ]

  // 先定义辅助函数
  const getCurrentProducts = (page: number, groupId: string) => {
    let datas = products.filter(
      (_, index) => index >= (page - 1) * 8 && index < page * 8
    )
    if (groupId !== tabs[0].id)
      datas = datas.filter((item) => item.groupId === groupId)

    return datas
  }

  const getTabProducts = (groupId: string) => {
    if (groupId === tabs[0].id) return products
    else {
      return products.filter((item) => item.groupId === groupId)
    }
  }

  // 根据initialTab设置初始选中的tab
  const getInitialTab = () => {
    if (initialTab) {
      // 检查initialTab是否存在于tabs中
      const foundTab = tabs.find(tab => tab.id === initialTab)
      if (foundTab) {
        return initialTab
      }
    }
    return tabs[0].id
  }

  const initialTabValue = getInitialTab()
  const [currentTab, setCurrentTab] = useState(initialTabValue)
  const [count, setCount] = useState(getTabProducts(initialTabValue).length)
  const [currentPage, setCurrentPage] = useState(1)
  const [currentProducts, setCurrentProducts] = useState(
    getCurrentProducts(1, initialTabValue)
  )

  return (
    <>
      <PageTabs
        title={t('productCenter')}
        iconSrc="/product-center-icon.svg"
        currentTab={currentTab}
        tabs={_tabs}
        background="rgb(200,228,250)"
        bannerSrc="/product/banner-pc.jpg"
        bannerMobileSrc="/product/banner-mobile.png"
        isSearch
        onTabChange={(tab: string) => {
          setCurrentTab(tab)
        }}
      />
      <div className={styles['product-list']}>
        <Tabs
          tabs={tabs}
          currentTab={currentTab}
          onClick={(id: string) => {
            setCurrentTab(id)
            setCurrentPage(1)
            const products = getCurrentProducts(1, id)
            setCount(getTabProducts(id).length)
            setCurrentProducts(products)
          }}
        />
        <List products={currentProducts} />
        <ListPagination
          currentPage={currentPage}
          count={count}
          onChange={(page: number) => {
            setCurrentPage(page)
            setCurrentProducts(getCurrentProducts(page, currentTab))
          }}
        />
      </div>
    </>
  )
}

function Tabs({
  tabs,
  currentTab,
  onClick,
}: {
  tabs: Array<Tab>
  currentTab: string
  onClick: Function
}) {
  const locale = useCurrentLocale()

  const TabItem = ({
    name,
    id,
    nameEn,
  }: {
    name: string
    id: string
    nameEn: string
  }) => (
    <button
      onClick={() => {
        if (id !== currentTab) onClick(id)
      }}
      className={`${styles['product-list__tabs__item']} ${
        currentTab === id ? styles['product-list__tabs__item--active'] : ''
      }`}
    >
      {locale === 'zh' ? name : nameEn}
    </button>
  )

  return (
    <div className={`${styles['product-list__tabs']} hide-on-small`}>
      {tabs.map((item, index) => (
        <TabItem {...item} key={index}></TabItem>
      ))}
    </div>
  )
}

function List({ products }: { products: ProductData[] }) {
  const locale = useCurrentLocale()

  const infos: Flex4ItemsInfo = products.map((item) => {
    return {
      imageSrc: item.imageSrc,
      title: locale === 'zh' ? item.name : item.nameEn,
      tip: locale === 'zh' ? item.description : item.descriptionEn,
      link: item.id === 'c31' ? `/product/c31` : '',
    }
  })
  const productsInfo: {
    infos: Flex4ItemsInfo
    imageSize: {
      width: number
      height: number
    }
    imageBox: {
      width: number
      height: number
    }
    mode: itemsMode
  } = {
    infos,
    imageSize: {
      width: 200,
      height: 200,
    },
    imageBox: {
      width: 300,
      height: 300,
    },
    mode: itemsMode.product,
  }

  return (
    <div className={styles['product-list__items']}>
      <Flex4ItemsBox {...productsInfo} />
    </div>
  )
}

function ListPagination({
  currentPage,
  count,
  onChange,
}: {
  count: number
  currentPage: number
  onChange: Function
}) {
  return (
    <>
      <div className={styles.pagination}>
        <Pagination
          currentPage={currentPage}
          count={count}
          onChange={(page: number) => {
            onChange(page)
          }}
        ></Pagination>
      </div>
    </>
  )
}
