/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[9].use[3]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[11].oneOf[9].use[4]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[11].oneOf[9].use[5]!./app/[locale]/page.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.page_page404container__c1LIB {
  position: relative;
  height: 100%;
  min-height: 300px;
}

.page_page404__1cE3u {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 29.5px;
}
.page_page404__content__oco9m {
  width: 240px;
  display: flex;
  flex-direction: column;
}
.page_page404__content__oco9m p {
  font-size: var(--font-large);
}
.page_page404__content__oco9m span {
  margin-top: 10px;
  font-size: var(--font-small);
  color: var(--text-description);
}
.page_page404__buttons__8oWnx {
  display: flex;
  gap: 15px;
  justify-content: center;
}
.page_page404__buttons__8oWnx button {
  height: 38px;
  font-size: 17px;
  padding: 0 15px;
  line-height: 38px;
  border-radius: 4px;
  background-color: #fff;
}
.page_page404__buttons__back__z0MU7 {
  color: var(--text-description);
}
.page_page404__buttons__home__sx9Sv {
  color: var(--color-theme);
}
