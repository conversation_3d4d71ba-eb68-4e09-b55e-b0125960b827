'use client'
import styles from './home.module.scss'
import Link from 'next/link'
import Image from 'next/image'
import DropdownWindow from '../components/dropdown-window'
import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import NavList from '../components/nav-list'
import {
  useChangeLocale,
  useCurrentLocale,
  useI18n,
  I18nProviderClient,
} from '@/locales/client'
import { useSelectedLayoutSegment } from 'next/navigation'

export default function NavLayout() {
  const selectedLayoutSegment = useSelectedLayoutSegment()
  const isNotFoundPage = selectedLayoutSegment === '__DEFAULT__'

  if (isNotFoundPage) return <div></div>
  else
    return (
      // eslint-disable-next-line react-hooks/rules-of-hooks
      <I18nProviderClient locale={useCurrentLocale()}>
        <Nav />
      </I18nProviderClient>
    )
}

function Nav() {
  const t = useI18n()
  const [isScrolled, setIsScrolled] = useState(false)
  const [isShowMenu, setShowMenu] = useState(false)

  useEffect(() => {
    // 监听滚动事件
    const handleScroll = () => {
      // 获取当前滚动位置
      const scrollTop = window.scrollY
      // 根据滚动位置是否大于0来判断是否添加投影效果
      setIsScrolled(scrollTop > 0)
    }

    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  const handleNavListClick = () => {
    setShowMenu(false)
  }

  return (
    <div style={{ position: 'relative' }}>
      <div className={styles.nav__placeholder}></div>
      <div
        className={`${styles.nav} ${isScrolled ? styles['nav--scrolled'] : ''
          } ${isShowMenu ? styles['nav--scrolled'] : ''}`}
      >
        <div className={styles.nav__content}>
          {/* 官网图片 */}
          <Link href={'/'} style={{ height: 44 }}>
            <Image
              src={'/cylan_logo.png'}
              width={125}
              height={44}
              alt="Cylan Logo"
              unoptimized
            ></Image>
          </Link>

          {/* 导航列表 */}
          <div className={`${styles.nav__list} hide-on-small hide-on-medium`}>
            <NavListItem title={t('home')} link="/" />
            <NavListItem
              title={t('productCenter')}
              showArrow
              link="/product"
              links={[
                {
                  link: '/product?tab=01',
                  text: t('productCamera'),
                },
                {
                  link: '/product?tab=02',
                  text: t('productTranslator'),
                },
              ]}
            />
            {/* <NavListItem title={'产品视频'} showArrow link="/videos" /> */}
            <NavListItem
              title={t('support')}
              showArrow
              link="/support"
              links={[
                {
                  link: '/support/download_client',
                  text: t('downloadClient'),
                },
                {
                  link: '/support/help',
                  text: t('help'),
                },
              ]}
            />
            {/* <NavListItem title={'新闻资讯'} link="/news" /> */}
            <NavListItem title={t('aboutUs')} link="/about" />
          </div>

          {/* 搜索和语言 */}
          <div className={styles.nav__right}>
            <Language />
            {/* <Search /> */}
            <button
              onClick={() => {
                setShowMenu(!isShowMenu)
              }}
              className={`${styles.nav__right__menu} hide-on-large`}
            >
              <Image
                src={'/menu.svg'}
                width={49}
                height={50}
                alt="menu"
              ></Image>
            </button>
          </div>
        </div>
      </div>
      {isShowMenu && (
        <>
          <div className={`${styles.nav__drop} hide-on-large`}>
            <NavList onClick={() => handleNavListClick()} />
          </div>
          <div
            className={styles.nav__mask}
            onClick={() => {
              setShowMenu(false)
            }}
          ></div>
        </>
      )}
    </div>
  )
}

function ArrowDown() {
  return <Image src={'/arrow-down.svg'} height={10} width={16} alt=""></Image>
}

function NavListItem({
  title,
  link = '/',
  showArrow = false,
  links = [],
}: {
  title: string
  showArrow?: boolean
  link: string
  links?: Array<{
    link: string
    text: string
  }>
}) {
  // 鼠标悬停
  const [isHovered, setIsHovered] = useState(false)

  const handleMouseEnter = () => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  let pathname = usePathname()
  if (pathname.includes('/zh/') || pathname.includes('/en/')) {
    pathname = pathname.replace(/\/zh|\/en/, '')
  } else {
    pathname = '/'
  }

  pathname = '/' + pathname.split('/')[1]

  return (
    <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      {showArrow ? (
        <DropdownWindow
          onClick={() => setIsHovered(false)}
          list={links}
          show={isHovered}
        >
          <Link href={link}>
            <div
              className={`${styles.nav__list__item} ${pathname === link.split('?')[0]
                  ? styles['nav__list__item--active']
                  : ''
                }`}
            >
              {title} {showArrow && <ArrowDown />}
            </div>
          </Link>
        </DropdownWindow>
      ) : (
        <Link href={link}>
          <div
            className={`${styles.nav__list__item} ${styles['nav__list__item--link']
              } ${pathname === `/${link.split('/')[1]}`
                ? styles['nav__list__item--active']
                : ''
              }`}
          >
            {title} {showArrow && <ArrowDown />}
          </div>
        </Link>
      )}
    </div>
  )
}

function Language() {
  const changeLocale = useChangeLocale()
  const currentLocal = useCurrentLocale()
  const t = useI18n()

  const LangItem = ({
    lang,
    isActive = false,
  }: {
    lang: 'zh' | 'en'
    isActive?: boolean
  }) => {
    return (
      <span
        onClick={() => {
          if (isActive) return
          changeLocale(lang)
        }}
        className={`${isActive ? styles['nav__right__language__text--active'] : ''
          } ${styles['nav__right__language__text']}`}
      >
        {lang === 'zh' ? t('chinese') : t('english')}
      </span>
    )
  }
  return (
    <div className={styles.nav__right__language}>
      <LangItem lang="zh" isActive={currentLocal === 'zh'} />
      <span>/</span>
      <LangItem lang="en" isActive={currentLocal === 'en'} />
    </div>
  )
}

function Search() {
  return (
    <div className={styles.nav__right__search}>
      <Image
        src={'/search-icon.svg'}
        width={20}
        height={20}
        alt="Search icon"
      ></Image>
    </div>
  )
}
