.c31 {
  min-width: 1920px;
  margin: auto;
  &__banner {
    position: relative;
    height: 664px;
    &__cover {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
    &__image {
      position: absolute;
      right: 378px;
      bottom: 0;
    }
    &__main {
      height: 100%;
      width: 1920px;
      margin: auto;
      position: relative;
      display: flex;
      align-items: center;
    }
    &__content {
      position: absolute;
      left: 397px;
      top: 90px;
      display: flex;
      flex-direction: column;
      gap: 30px;
      h2 {
        margin: 0;
        text-align: left;
        font-weight: normal;
        font-size: 46px;
      }
      p {
        line-height: 28px;
        font-size: var(--font-medium);
        color: var(--text-description);
      }
      div {
        display: flex;
        gap: 18px;
      }
    }
  }
  &__banner1 {
    &__product {
      margin-left: 377px;
    }
    &__description {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 720px;
    }
    &__icon {
    }
    span {
      color: var(--text-description);
      font-size: 24px;
      margin-top: 10px;
    }
    &__qrcode {
      margin-top: 20px;
      position: relative;
      button {
        border-radius: 8px;
        border: 1px solid var(--color-theme);
        height: 46px;
        line-height: 46px;
        text-align: center;
        color: var(--color-theme);
        font-size: 20px;
        padding: 0 40px;
      }
      > div {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
      }
      &:hover {
        > div {
          display: block;
        }
        button {
          background-color: var(--color-theme);
          color: #fff;
        }
      }
    }
    p {
      font-size: 32px;
      margin-top: 20px;
    }
  }
}

@media (max-width: 450px) {
  .c31-outter {
    width: 100vw;
    height: 100vh;
    overflow: scroll;
    position: fixed;
    right: 0;
    bottom: 0;
    z-index: 100;
    background-color: var(--color-background);
    .c31 {
      position: absolute;
      left: 0;
      top: 0;
      transform: translate(-25%, -20%) scale(0.6);
    }
  }
}
