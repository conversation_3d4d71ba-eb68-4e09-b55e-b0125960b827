import { HelpData, ArticleType, ArticleContentType, Group, PrevAndNextArticle } from "./type";

const helpDatas: Array<HelpData> = [{
  type: ArticleType.support,
  title: '设备绑定失败怎么办',
  titleEn: 'Device binding failed',
  tip: '',
  time: new Date('2024-04-25'),
  content: [{
    type: ArticleContentType.paragraph,
    text: '设备绑定失败，可尝试以下操作：'
  },
  {
    type: ArticleContentType.paragraph,
    text: '1. 配置Wi-Fi密码时，选择可用网络并输入正确的Wi-Fi密码。'
  },
  {
    type: ArticleContentType.paragraph,
    text: '2. 设备只支持2.4Ghz的网络。选择Wi-Fi时，请勿选择5Ghz的网络。'
  },
  {
    type: ArticleContentType.paragraph,
    text: '3. 确认您所配置的Wi-Fi可以正常访问互联网。'
  }],
  contentEn: [{
    type: ArticleContentType.paragraph,
    text: 'Device binding failed. Please try the following:'
  },
  {
    type: ArticleContentType.paragraph,
    text: '1. When configuring the Wi-Fi password, select an available network and enter the correct Wi-Fi password.'
  },
  {
    type: ArticleContentType.paragraph,
    text: '2. The device only supports 2.4GHz networks. When selecting Wi-Fi, make sure not to choose a 5GHz network.'
  },
  {
    type: ArticleContentType.paragraph,
    text: '3. Verify that the Wi-Fi you configured has internet access.'
  }],
  id: 'help__01',
  coverSrc: '/support/help/help__01.jpg',
  groupId: '01',
  groupName: 'App使用帮助',
  groupNameEn: 'App FAQ',
  categoryName: '使用帮助',
  categoryNameEn: 'Help'
}, {
  type: ArticleType.support,
  title: '蓝牙搜索不到设备怎么办',
  titleEn: 'Bluetooth device not found',
  tip: '',
  time: new Date('2024-04-25'),
  content: [{
    type: ArticleContentType.paragraph,
    text: '1. 请确保手机已经打开蓝牙。'
  },
  {
    type: ArticleContentType.paragraph,
    text: '2. 请确保设备画面处于待配网状态。如下图所示。'
  }, {
    type: ArticleContentType.image,
    src: '/support/help/help__02__image1.png'
  }],
  contentEn: [{
    type: ArticleContentType.paragraph,
    text: `1. Please ensure that your phone's Bluetooth is turned on.`
  },
  {
    type: ArticleContentType.paragraph,
    text: '2. Make sure that the device screen is in pairing mode. Refer to the illustration below.'
  }, {
    type: ArticleContentType.image,
    src: '/support/help/help__02__image1.png'
  }],
  id: 'help__02',
  coverSrc: '/support/help/help__02.jpg',
  groupId: '01',
  groupName: 'App使用帮助',
  groupNameEn: 'App FAQ',
  categoryName: '使用帮助',
  categoryNameEn: 'Help'
}, {
  type: ArticleType.support,
  title: '设备告警消息没有推送怎么办',
  titleEn: 'Device alarm messages are not being pushed',
  tip: '',
  time: new Date('2024-04-25'),
  content: [{
    type: ArticleContentType.paragraph,
    text: '1.如果您想收到移动报警消息，确认APP内设备【设置】-【侦测报警设置】-【移动侦测开关】是否开启，开启了才会推给您移动报警消息。'
  },
  {
    type: ArticleContentType.paragraph,
    text: '2.如果已授权APP消息通知权限，确认APP内【消息】-右上角【设置】是否开启了【设备告警通知】，如果关闭了设备告警通知则不会推送消息。'
  },
  {
    type: ArticleContentType.paragraph,
    text: '3.如果您是安卓手机，建议您到APP【我的】-【视频来电设置】中查看是否授权APP有通知权限。如果您是IOS手机，建议您授权APP【通知管理】权限。'
  }],
  contentEn: [{
    type: ArticleContentType.paragraph,
    text: '1.If you want to receive motion alarm messages, ensure in the app that the device [Settings] - [Motion Detection Settings] - [Motion Detection Switch] is enabled. Motion alarm messages are only pushed when this setting is turned on.'
  },
  {
    type: ArticleContentType.paragraph,
    text: `2.If you have granted app notification permissions, verify in the app's [Messages] - [Settings] (top right corner) that [Device Alarm Notifications] are enabled. If disabled, alarm messages won't be pushed.`
  },
  {
    type: ArticleContentType.paragraph,
    text: `3.If you are using an Android phone, check if you have granted notification permissions in [My] - [Video Call Settings] within the app. If you are using an iOS phone, ensure the app has notification management permissions.`
  }],
  id: 'help__03',
  coverSrc: '/support/help/help__03.jpg',
  groupId: '01',
  groupName: 'App使用帮助',
  groupNameEn: 'App FAQ',
  categoryName: '使用帮助',
  categoryNameEn: 'Help'
}, {
  type: ArticleType.support,
  title: '设备掉线怎么办',
  titleEn: 'Device disconnected',
  tip: '',
  time: new Date('2024-04-25'),
  content: [{
    type: ArticleContentType.paragraph,
    text: '1. 请检查设备的wifi信号是否为较好。如果wifi信号较差，请将设备尽量放在距离路由器近的地方。'
  }, {
    type: ArticleContentType.image,
    src: '/support/help/help__04__image1.jpg'
  },
  {
    type: ArticleContentType.paragraph,
    text: '2. 请检查设备所连接的wifi网络是否能够流畅上网。'
  },
  {
    type: ArticleContentType.paragraph,
    text: '2. 设备只支持2.4Ghz的网络。选择Wi-Fi时，请勿选择5Ghz的网络。'
  },
  ],
  contentEn: [{
    type: ArticleContentType.paragraph,
    text: `1. Please check if the device has a strong Wi-Fi signal. If the Wi-Fi signal is weak, try placing the device closer to the router if possible.`
  }, {
    type: ArticleContentType.image,
    src: '/support/help/help__04__image1.jpg'
  },
  {
    type: ArticleContentType.paragraph,
    text: `2. Verify that the Wi-Fi network the device is connected to has a stable internet connection and can access the internet smoothly.`
  },
  {
    type: ArticleContentType.paragraph,
    text: `3. Check the power cord and adapter; if they don't match, frequent power shortages and disconnections may occur.`
  }],
  id: 'help__04',
  coverSrc: '/support/help/help__04.jpg',
  groupId: '01',
  groupName: 'App使用帮助',
  groupNameEn: 'App FAQ',
  categoryName: '使用帮助',
  categoryNameEn: 'Help'
}]

const helpGroups: Group[] = [{
  id: '01',
  name: 'App使用帮助',
  nameEn: 'App FAQ',
  categoryName: '使用帮助',
  categoryNameEn: 'Help'
}]

export async function getHelpDatas(): Promise<HelpData[]> {
  return new Promise(resolve => {
    resolve(helpDatas)
  })
}

export async function getHelpData(id: string): Promise<HelpData | undefined> {
  return new Promise(resolve => {
    const data = helpDatas.find(i => i.id === id)
    resolve(data)
  })
}

export async function getHotHelpDatas(): Promise<HelpData[]> {
  return new Promise(resolve => {
    const datas = helpDatas.filter((_, index) => index < 4)
    resolve(datas)
  })
}

export async function getHelpGroups(): Promise<Group[]> {
  return new Promise(resolve => {
    resolve(helpGroups)
  })
}

export async function getPrevAndNextHelpData(id: string): Promise<PrevAndNextArticle> {
  return new Promise(resolve => {
    let currentIndex: number = -1

    const currentData = helpDatas.find((i, index) => {
      if (i.id === id) {
        currentIndex = index
        return true
      }
    })

    const data = {
      prev: -1,
      prevLink: '',
      prevTitle: '',
      prevTitleEn: '',
      next: -1,
      nextLink: '',
      nextTitle: '',
      nextTitleEn: ''
    }

    if (currentIndex === -1) {
      resolve(data)
      return
    } else {
      const prevData = helpDatas.find((item, index) => {
        if (index === currentIndex - 1) {
          data.prev = index
          data.prevLink = '/article/' + item.id
          data.prevTitle = item.title
          data.prevTitleEn = item.titleEn ? item.titleEn : ''
          return true
        }
      })
      const nextData = helpDatas.find((item, index) => {
        if (index === currentIndex + 1) {
          data.next = index
          data.nextLink = '/article/' + item.id
          data.nextTitle = item.title
          data.nextTitleEn = item.titleEn ? item.titleEn : ''
          return true
        }
      })
      resolve(data)
    }

  })
}